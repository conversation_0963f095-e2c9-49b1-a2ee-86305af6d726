export type StorageType = 'google_photos' | 'google_photos_oauth';

export interface TripPhotoDetails {
  id: string;
  trip_name: string;
  trip_description: string | null;
  featured_image_url: string | null;
  access_password_hash: string | null; // Only hashed passwords stored
  storage_type: StorageType | null; // Matches database enum
  google_photos_album_id: string | null;
  oauth_user_email: string | null;
  oauth_refresh_token: string | null;
  oauth_refresh_token_encrypted: string | null; // Added from database
  security_version: number | null; // Added from database
  manual_shareable_url: string | null; // Manual shareable link for public access
  created_at: string;
  updated_at: string;
}

export interface TripPhotoDetailsFormData extends Omit<TripPhotoDetails, 'id' | 'created_at' | 'updated_at' | 'google_photos_album_id' | 'oauth_refresh_token' | 'oauth_refresh_token_encrypted' | 'security_version' | 'access_password_hash'> {
  // Form data doesn't need server-managed fields
  // Google Photos fields are managed by OAuth flow
  // access_password is for form input (gets hashed before storage)
  access_password: string | null;
}