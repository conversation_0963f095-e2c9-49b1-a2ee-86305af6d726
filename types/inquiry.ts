import { Trip } from './trip';

export type InquiryStatus = 'new' | 'in_progress' | 'resolved' | 'closed';

export interface Inquiry {
  id: string;
  name: string;
  email: string;
  phone?: string;
  subject?: string;
  message: string;
  inquiry_type?: string;
  trip_id?: string;
  status: InquiryStatus;
  admin_notes?: string;
  responded_at?: string;
  metadata?: Record<string, any>;
  created_at: string;
  updated_at: string;
  trips?: Pick<Trip, 'title' | 'slug'>;
}