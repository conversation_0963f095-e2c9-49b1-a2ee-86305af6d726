/**
 * Comprehensive API types for the application
 */

// Base API response structure
export interface ApiResponse<T = any> {
  success: boolean;
  data?: T;
  error?: string;
  message?: string;
  timestamp?: string;
}

// Pagination types
export interface PaginationParams {
  page?: number;
  limit?: number;
  search?: string;
  sortBy?: string;
  sortOrder?: 'asc' | 'desc';
}

export interface PaginationInfo {
  page: number;
  limit: number;
  total: number;
  totalPages: number;
  hasNext: boolean;
  hasPrev: boolean;
}

export interface PaginatedResponse<T> extends ApiResponse<T[]> {
  pagination: PaginationInfo;
}

// Error response types
export interface ApiError {
  message: string;
  code: string;
  timestamp: string;
  context?: Record<string, any>;
  stack?: string; // Only in development
}

export interface ApiErrorResponse {
  success: false;
  error: ApiError;
}

// Authentication types
export interface LoginRequest {
  email: string;
  password: string;
  rememberMe?: boolean;
}

export interface LoginResponse {
  success: boolean;
  user?: {
    id: string;
    email: string;
    role: string;
    name?: string;
  };
  token?: string;
  expiresAt?: string;
}

export interface PasswordVerificationRequest {
  albumId: string;
  password: string;
}

export interface PasswordVerificationResponse {
  success: boolean;
  downloadLink?: string;
  albumName?: string;
}

// Photo Album types
export interface PhotoAlbumRequest {
  trip_name: string;
  trip_description?: string;
  featured_image_url?: string;
  access_password?: string;
}

export interface PhotoAlbumResponse {
  id: string;
  trip_name: string;
  trip_description?: string;
  featured_image_url?: string;
  storage_type: string;
  google_photos_album_id?: string;
  oauth_user_email?: string;
  manual_shareable_url?: string;
  created_at: string;
  updated_at: string;
}

export interface PhotoAlbumUpdateRequest {
  trip_name?: string;
  trip_description?: string;
  featured_image_url?: string;
  access_password?: string;
  manual_shareable_url?: string;
  google_photos_album_id?: string;
  oauth_user_email?: string;
}

// File upload types
export interface FileUploadRequest {
  file: File;
  albumId?: string;
  watermark?: boolean;
}

export interface FileUploadResponse {
  success: boolean;
  url?: string;
  publicId?: string;
  width?: number;
  height?: number;
  format?: string;
  size?: number;
}

export interface WatermarkPreviewRequest {
  imageUrl: string;
  watermarkUrl?: string;
  position?: 'top-right' | 'top-left' | 'bottom-right' | 'bottom-left' | 'center';
  scale?: number;
}

export interface WatermarkPreviewResponse {
  success: boolean;
  watermarkedUrl?: string;
  originalUrl?: string;
}

// Google Photos integration types
export interface GooglePhotosAuthRequest {
  albumId: string;
  redirectUrl?: string;
}

export interface GooglePhotosAuthResponse {
  success: boolean;
  authUrl?: string;
  state?: string;
}

export interface GooglePhotosCallbackRequest {
  code: string;
  state: string;
  albumId: string;
}

export interface GooglePhotosUploadRequest {
  albumId: string;
  images: string[]; // Array of image URLs to upload
}

export interface GooglePhotosUploadResponse {
  success: boolean;
  uploadedCount?: number;
  failedCount?: number;
  albumUrl?: string;
  errors?: string[];
}

// Blog types
export interface BlogRequest {
  title: string;
  content: string;
  excerpt?: string;
  featured_image?: string;
  slug?: string;
  published?: boolean;
  tags?: string[];
  meta_description?: string;
}

export interface BlogResponse {
  id: string;
  title: string;
  content: string;
  excerpt?: string;
  featured_image?: string;
  slug: string;
  published: boolean;
  tags?: string[];
  meta_description?: string;
  author_id?: string;
  created_at: string;
  updated_at: string;
  published_at?: string;
}

// Gallery types
export interface GalleryRequest {
  title: string;
  description?: string;
  featured_image?: string;
  images?: string[];
  is_featured?: boolean;
}

export interface GalleryResponse {
  id: string;
  title: string;
  description?: string;
  featured_image?: string;
  images?: string[];
  is_featured: boolean;
  created_at: string;
  updated_at: string;
}

// Trip types
export interface TripRequest {
  title: string;
  description?: string;
  featured_image?: string;
  price?: number;
  duration?: string;
  location?: string;
  max_participants?: number;
  difficulty_level?: 'easy' | 'moderate' | 'hard';
  includes?: string[];
  excludes?: string[];
  itinerary?: any;
  is_featured?: boolean;
}

export interface TripResponse {
  id: string;
  title: string;
  description?: string;
  featured_image?: string;
  price?: number;
  duration?: string;
  location?: string;
  max_participants?: number;
  difficulty_level?: string;
  includes?: string[];
  excludes?: string[];
  itinerary?: any;
  is_featured: boolean;
  slug: string;
  created_at: string;
  updated_at: string;
}

// Inquiry types
export interface InquiryRequest {
  name: string;
  email: string;
  phone?: string;
  message: string;
  trip_id?: string;
  preferred_date?: string;
  number_of_participants?: number;
}

export interface InquiryResponse {
  id: string;
  name: string;
  email: string;
  phone?: string;
  message: string;
  trip_id?: string;
  preferred_date?: string;
  number_of_participants?: number;
  status: 'new' | 'contacted' | 'quoted' | 'booked' | 'cancelled';
  created_at: string;
  updated_at: string;
}

// Team member types
export interface TeamMemberRequest {
  name: string;
  position: string;
  bio?: string;
  image?: string;
  email?: string;
  phone?: string;
  social_links?: Record<string, string>;
  is_featured?: boolean;
}

export interface TeamMemberResponse {
  id: string;
  name: string;
  position: string;
  bio?: string;
  image?: string;
  email?: string;
  phone?: string;
  social_links?: Record<string, string>;
  is_featured: boolean;
  created_at: string;
  updated_at: string;
}

// Rate limiting types
export interface RateLimitInfo {
  limit: number;
  remaining: number;
  reset: number;
  retryAfter?: number;
}

export interface RateLimitResponse extends ApiErrorResponse {
  rateLimit: RateLimitInfo;
}

// Validation types
export interface ValidationError {
  field: string;
  message: string;
  value?: any;
}

export interface ValidationErrorResponse extends ApiErrorResponse {
  validationErrors: ValidationError[];
}

// Generic request/response helpers
export type CreateRequest<T> = Omit<T, 'id' | 'created_at' | 'updated_at'>;
export type UpdateRequest<T> = Partial<CreateRequest<T>>;

// API endpoint types
export interface ApiEndpoint {
  method: 'GET' | 'POST' | 'PUT' | 'PATCH' | 'DELETE';
  path: string;
  requiresAuth?: boolean;
  rateLimit?: {
    windowMs: number;
    max: number;
  };
}

// Webhook types
export interface WebhookPayload<T = any> {
  event: string;
  data: T;
  timestamp: string;
  signature?: string;
}

// Search types
export interface SearchRequest {
  query: string;
  filters?: Record<string, any>;
  pagination?: PaginationParams;
}

export interface SearchResponse<T> extends PaginatedResponse<T> {
  query: string;
  totalResults: number;
  searchTime: number;
}

// Export types
export interface ExportRequest {
  format: 'csv' | 'xlsx' | 'json';
  filters?: Record<string, any>;
  fields?: string[];
}

export interface ExportResponse {
  success: boolean;
  downloadUrl?: string;
  filename?: string;
  expiresAt?: string;
}
