export type TripDifficulty = 'easy' | 'moderate' | 'challenging' | 'extreme';

export type TransportMode = 'train' | 'bus' | 'car' | 'flight';

export interface TripItinerary {
  day: number;
  title: string;
  subheading?: string;
  description: string;
  activities?: string[];
  meals?: {
    breakfast?: boolean;
    lunch?: boolean;
    dinner?: boolean;
  };
  accommodation?: string;
  transport_mode?: TransportMode | null;
}

export interface CancellationPolicy {
  days_before: number;
  refund_percentage: number;
  description?: string;
}

// Client-side Trip interface - only includes fields needed for display
export interface ClientTrip {
  id: string;
  title: string;
  slug: string;
  description: string | null;
  detailed_description: string | null;
  destination: string;
  days: number;
  nights: number;
  min_age: number | null;
  max_age: number | null;
  price_per_person: number;
  difficulty: TripDifficulty;
  inclusions: string[] | null;
  exclusions: string[] | null;
  itinerary: TripItinerary[] | null;
  featured_image_url: string | null;
  is_trek: boolean | null;
  is_active: boolean | null;
  is_featured: boolean | null;
  category: string | null;
  mode_of_travel: string | null;
  pickup_location: string | null;
  drop_location: string | null;
  property_used: string | null;
  activities: string[] | null;
  optional_activities: string[] | null;
  benefits: string[] | null;
  safety_supervision: string[] | null;
  things_to_carry: string[] | null;
  available_dates: string[] | null; // departure dates in YYYY-MM-DD format
  payment_terms: string | null;
  cancellation_policy: Record<string, string> | null;
  special_notes: string[] | null;
}

// Transform function to convert full Trip to ClientTrip
export function toClientTrip(trip: any): ClientTrip {
  return {
    id: trip.id,
    title: trip.title,
    slug: trip.slug,
    description: trip.description,
    detailed_description: trip.detailed_description,
    destination: trip.destination,
    days: trip.days,
    nights: trip.nights,
    min_age: trip.min_age,
    max_age: trip.max_age,
    price_per_person: trip.price_per_person,
    difficulty: trip.difficulty,
    inclusions: trip.inclusions,
    exclusions: trip.exclusions,
    itinerary: trip.itinerary,
    featured_image_url: trip.featured_image_url,
    is_trek: trip.is_trek,
    is_active: trip.is_active,
    is_featured: trip.is_featured,
    category: trip.category,
    mode_of_travel: trip.mode_of_travel,
    pickup_location: trip.pickup_location,
    drop_location: trip.drop_location,
    property_used: trip.property_used,
    activities: trip.activities,
    optional_activities: trip.optional_activities,
    benefits: trip.benefits,
    safety_supervision: trip.safety_supervision,
    things_to_carry: trip.things_to_carry,
    available_dates: trip.available_dates,
    payment_terms: trip.payment_terms,
    cancellation_policy: trip.cancellation_policy,
    special_notes: trip.special_notes,
  };
}
