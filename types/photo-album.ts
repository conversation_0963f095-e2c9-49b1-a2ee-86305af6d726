export type StorageType = 'google_photos' | 'google_photos_oauth';

export interface PhotoAlbum {
  id: string;
  trip_name: string;
  trip_description?: string;
  featured_image_url?: string;
  access_password_hash?: string; // Only hashed passwords stored in database
  storage_type: StorageType | null; // Matches database enum
  google_photos_album_id?: string;
  oauth_user_email?: string;
  // Note: Refresh tokens are handled server-side only for security
  security_version?: number; // Added from database
  manual_shareable_url?: string; // Manual shareable link for public access
  created_at: string;
  updated_at: string;
}

export interface CreatePhotoAlbumRequest {
  trip_name: string;
  trip_description?: string;
  featured_image_url?: string;
  access_password?: string;
}

export interface UpdatePhotoAlbumRequest {
  trip_name?: string;
  trip_description?: string;
  featured_image_url?: string;
  access_password?: string;
  manual_shareable_url?: string;
}
