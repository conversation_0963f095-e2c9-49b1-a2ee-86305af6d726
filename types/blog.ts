export interface BlogPost {
  id: string;
  title: string;
  slug: string;
  content: string;
  excerpt?: string;
  featured_image_url?: string;
  author: string;
  category?: string;
  tags?: string[];
  is_published?: boolean;
  published_at?: string;
  seo_title?: string;
  seo_description?: string;
  created_at: string;
  updated_at: string;
}

export interface BlogFormData {
  title: string;
  content: string;
  author: string;
  excerpt?: string;
  featured_image_url?: string;
  category?: string;
  tags?: string[];
  is_published?: boolean;
  seo_title?: string;
  seo_description?: string;
}