# =============================================================================
# Positive7 WEBSITE - ENVIRONMENT CONFIGURATION
# =============================================================================
# This file contains all environment variables needed for production deployment
# Copy this file to .env.local and fill in your actual values
# =============================================================================

# =============================================================================
# CORE SUPABASE CONFIGURATION (REQUIRED)
# =============================================================================
# Get these from your Supabase project settings
NEXT_PUBLIC_SUPABASE_URL=your_supabase_url
NEXT_PUBLIC_SUPABASE_ANON_KEY=your_supabase_anon_key
SUPABASE_SERVICE_ROLE_KEY=your_supabase_service_role_key
NEXT_PUBLIC_SUPABASE_PROJECT_ID=your_project_id

# =============================================================================
# SITE CONFIGURATION (REQUIRED)
# =============================================================================
# Your production domain (e.g., https://yourdomain.com)
NEXT_PUBLIC_SITE_URL=http://localhost:3000

# =============================================================================
# SECURITY CONFIGURATION (REQUIRED FOR PRODUCTION)
# =============================================================================
# Generate a strong 32-character encryption key for production
# Use: openssl rand -hex 32
ENCRYPTION_KEY=your_32_character_encryption_key_here

# =============================================================================
# EMAIL CONFIGURATION (REQUIRED)
# =============================================================================
# SMTP Configuration for sending emails
SMTP_HOST=smtp.gmail.com
SMTP_PORT=587
SMTP_SECURE=false
SMTP_USER=<EMAIL>
SMTP_PASS=your_app_password
SMTP_FROM=<EMAIL>

# Alternative Email Configuration (Legacy support)
EMAIL_SERVICE=gmail
EMAIL_USER=<EMAIL>
EMAIL_PASSWORD=your_app_password
NOTIFICATION_EMAIL=<EMAIL>

# =============================================================================
# CLOUDINARY CONFIGURATION (REQUIRED)
# =============================================================================
# Get these from your Cloudinary dashboard
NEXT_PUBLIC_CLOUDINARY_CLOUD_NAME=your_cloud_name
NEXT_PUBLIC_CLOUDINARY_API_KEY=your_api_key
CLOUDINARY_API_KEY=your_api_key
CLOUDINARY_API_SECRET=your_api_secret

# =============================================================================
# GOOGLE SERVICES CONFIGURATION (REQUIRED FOR PHOTO FEATURES)
# =============================================================================
# Google Service Account (for Google Drive/Photos API)
GOOGLE_CLIENT_EMAIL=your_service_account_email
GOOGLE_PRIVATE_KEY=your_service_account_private_key

# Google Photos OAuth2 (for photo uploads)
GOOGLE_PHOTOS_CLIENT_ID=your_oauth2_client_id
GOOGLE_PHOTOS_CLIENT_SECRET=your_oauth2_client_secret
GOOGLE_PHOTOS_REDIRECT_URI=http://localhost:3000/api/auth/google/callback

# =============================================================================
# OPTIONAL INTEGRATIONS
# =============================================================================
# Weather API (OpenWeatherMap) - Optional
OPENWEATHER_API_KEY=your_openweather_api_key

# Instagram API - Optional
INSTAGRAM_ACCESS_TOKEN=your_instagram_access_token

# Google Analytics - Optional
NEXT_PUBLIC_GA_ID=your_google_analytics_id

# =============================================================================
# PRODUCTION DEPLOYMENT CONFIGURATION
# =============================================================================
# Node.js Environment (development, production, test)
NODE_ENV=development

# =============================================================================
# EXTERNAL LOGGING & MONITORING (OPTIONAL)
# =============================================================================
# For production error tracking (e.g., Sentry, LogRocket)
# SENTRY_DSN=your_sentry_dsn
# LOGROCKET_APP_ID=your_logrocket_app_id

# =============================================================================
# RATE LIMITING & SECURITY (PRODUCTION DEFAULTS)
# =============================================================================
# These are handled automatically by the application
# You can override them if needed:
# RATE_LIMIT_MAX_REQUESTS=100
# RATE_LIMIT_WINDOW_MS=900000

# =============================================================================
# DEPLOYMENT PLATFORM SPECIFIC
# =============================================================================
# Vercel (automatically set by Vercel)
# VERCEL_URL=your_vercel_url
# VERCEL_ENV=production

# =============================================================================
# SETUP INSTRUCTIONS
# =============================================================================
# 1. Copy this file to .env.local
# 2. Fill in all REQUIRED values above
# 3. Generate ENCRYPTION_KEY: openssl rand -hex 32
# 4. Set up Supabase project and get credentials
# 5. Set up Cloudinary account and get credentials
# 6. Configure Google Service Account for Photos API
# 7. Set up SMTP email service (Gmail App Password recommended)
# 8. Test in development before deploying to production
