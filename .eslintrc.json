{"extends": ["next/core-web-vitals", "eslint:recommended", "plugin:@typescript-eslint/recommended"], "plugins": ["unused-imports"], "rules": {"@typescript-eslint/no-unused-vars": "warn", "@typescript-eslint/no-explicit-any": "off", "react/no-unescaped-entities": "off", "react-hooks/exhaustive-deps": "off", "jsx-a11y/alt-text": "warn", "@next/next/no-img-element": "warn", "react-hooks/rules-of-hooks": "warn", "unused-imports/no-unused-imports": "warn", "unused-imports/no-unused-vars": ["warn", {"vars": "all", "varsIgnorePattern": "^_", "args": "after-used", "argsIgnorePattern": "^_"}]}}