#!/usr/bin/env node

/**
 * This script runs after npm install and handles OS-specific dependencies
 */

const { execSync } = require('child_process');
const fs = require('fs');
const path = require('path');
const os = require('os');

console.log('Running post-install script...');

// Function to check if a package is installed
function isPackageInstalled(packageName) {
  try {
    require.resolve(packageName);
    return true;
  } catch (e) {
    return false;
  }
}

// Function to install a package if it's not already installed
function ensurePackageInstalled(packageName, version) {
  if (!isPackageInstalled(packageName)) {
    console.log(`Installing ${packageName}@${version}...`);
    try {
      execSync(`npm install ${packageName}@${version} --no-save`, { stdio: 'inherit' });
      console.log(`Successfully installed ${packageName}`);
    } catch (error) {
      console.error(`Failed to install ${packageName}: ${error.message}`);
    }
  } else {
    console.log(`${packageName} is already installed.`);
  }
}

// Handle OS-specific dependencies
const platform = os.platform();
console.log(`Detected platform: ${platform}`);

// Ensure sharp is installed correctly for the current platform
if (['win32', 'darwin', 'linux'].includes(platform)) {
  ensurePackageInstalled('sharp', '^0.34.2');
}

// Production-ready postinstall - no file modifications needed

console.log('Post-install script completed.'); 