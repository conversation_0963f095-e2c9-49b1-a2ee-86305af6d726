const CACHE_NAME = 'positive7-v1748960800000';
const STATIC_CACHE = 'positive7-static-v1748960800000';
const DYNAMIC_CACHE = 'positive7-dynamic-v1748960800000';
const IMAGE_CACHE = 'positive7-images-v1748960800000';
const API_CACHE = 'positive7-api-v1748960800000';
const COMPONENT_CACHE = 'positive7-components-v1748960800000';

// Assets to cache immediately
const STATIC_ASSETS = [
  '/',
  '/offline',
  '/manifest.json',
  '/icons/icon-192x192.png',
  '/icons/icon-512x512.png'
];

// Install event - cache static assets
self.addEventListener('install', (event) => {
  console.log('Service Worker: Installing...');

  event.waitUntil(
    caches.open(STATIC_CACHE)
      .then((cache) => {
        console.log('Service Worker: Caching static assets');
        return cache.addAll(STATIC_ASSETS);
      })
      .then(() => {
        console.log('Service Worker: Static assets cached');
        return self.skipWaiting();
      })
      .catch((error) => {
        console.error('Service Worker: Error caching static assets', error);
      })
  );
});

// Activate event - clean up old caches
self.addEventListener('activate', (event) => {
  console.log('Service Worker: Activating...');

  event.waitUntil(
    caches.keys()
      .then((cacheNames) => {
        return Promise.all(
          cacheNames.map((cacheName) => {
            if (cacheName !== STATIC_CACHE &&
                cacheName !== DYNAMIC_CACHE &&
                cacheName !== IMAGE_CACHE) {
              console.log('Service Worker: Deleting old cache', cacheName);
              return caches.delete(cacheName);
            }
          })
        );
      })
      .then(() => {
        console.log('Service Worker: Activated');
        return self.clients.claim();
      })
  );
});

// Fetch event - implement caching strategies
self.addEventListener('fetch', (event) => {
  const { request } = event;
  const url = new URL(request.url);

  // Skip non-GET requests
  if (request.method !== 'GET') {
    return;
  }

  // Skip non-HTTP(S) schemes (chrome-extension, etc.)
  if (!url.protocol.startsWith('http')) {
    return;
  }

  // Skip external requests (except for images)
  if (url.origin !== location.origin && !isImageRequest(request)) {
    return;
  }

  // Handle different types of requests
  if (isImageRequest(request)) {
    event.respondWith(handleImageRequest(request));
  } else if (isAPIRequest(request)) {
    event.respondWith(handleAPIRequest(request));
  } else if (isPageRequest(request)) {
    event.respondWith(handlePageRequest(request));
  } else {
    event.respondWith(handleStaticRequest(request));
  }
});

// Check if request is for an image
function isImageRequest(request) {
  return request.destination === 'image' ||
         /\.(jpg|jpeg|png|gif|webp|svg|ico)$/i.test(new URL(request.url).pathname);
}

// Check if request is for API
function isAPIRequest(request) {
  return request.url.includes('/api/');
}

// Check if request is for a page
function isPageRequest(request) {
  return request.mode === 'navigate' ||
         (request.method === 'GET' && request.headers.get('accept')?.includes('text/html'));
}

// Handle image requests - Cache First strategy
async function handleImageRequest(request) {
  try {
    const cache = await caches.open(IMAGE_CACHE);
    const cachedResponse = await cache.match(request);

    if (cachedResponse) {
      return cachedResponse;
    }

    const networkResponse = await fetch(request);

    if (networkResponse.ok) {
      cache.put(request, networkResponse.clone());
    }

    return networkResponse;
  } catch (error) {
    console.error('Service Worker: Error handling image request', error);
    // Return a fallback image if available
    return caches.match('/icons/icon-192x192.png');
  }
}

// Handle API requests - Network Only strategy for dynamic content
async function handleAPIRequest(request) {
  const url = new URL(request.url);

  // Never cache dynamic API endpoints that need real-time data
  const dynamicEndpoints = ['/api/trips', '/api/trips-photos', '/api/blog', '/api/galleries', '/api/admin'];
  const isDynamicAPI = dynamicEndpoints.some(endpoint => url.pathname.startsWith(endpoint));

  if (isDynamicAPI) {
    // Network only - no caching for dynamic content
    try {
      const networkResponse = await fetch(request);
      return networkResponse;
    } catch (error) {
      console.error('Service Worker: Network request failed for dynamic API', error);
      return new Response(
        JSON.stringify({
          error: 'Offline',
          message: 'You are currently offline. Please check your connection.'
        }),
        {
          status: 503,
          statusText: 'Service Unavailable',
          headers: { 'Content-Type': 'application/json' }
        }
      );
    }
  }

  // For other APIs, use network first with cache fallback
  try {
    const networkResponse = await fetch(request);

    if (networkResponse.ok) {
      const cache = await caches.open(DYNAMIC_CACHE);
      cache.put(request, networkResponse.clone());
    }

    return networkResponse;
  } catch (error) {
    console.error('Service Worker: Network request failed, trying cache', error);

    const cachedResponse = await caches.match(request);
    if (cachedResponse) {
      return cachedResponse;
    }

    // Return offline response for API requests
    return new Response(
      JSON.stringify({
        error: 'Offline',
        message: 'You are currently offline. Please check your connection.'
      }),
      {
        status: 503,
        statusText: 'Service Unavailable',
        headers: { 'Content-Type': 'application/json' }
      }
    );
  }
}

// Handle page requests - Network Only for dynamic pages, Stale While Revalidate for others
async function handlePageRequest(request) {
  const url = new URL(request.url);

  // Never cache dynamic pages that need real-time data
  const dynamicPages = ['/trips', '/blog', '/gallery', '/trips-photos', '/admin'];
  const isDynamicPage = dynamicPages.some(page => url.pathname.startsWith(page));

  if (isDynamicPage) {
    // Network only - no caching for dynamic pages
    try {
      const networkResponse = await fetch(request);
      return networkResponse;
    } catch (error) {
      console.error('Service Worker: Network request failed for dynamic page', error);
      return caches.match('/offline') || new Response(
        '<html><body><h1>You are offline</h1><p>Please check your internet connection.</p></body></html>',
        { headers: { 'Content-Type': 'text/html' } }
      );
    }
  }

  // For other pages, use stale while revalidate
  try {
    const cache = await caches.open(DYNAMIC_CACHE);
    const cachedResponse = await cache.match(request);

    const networkResponsePromise = fetch(request)
      .then((networkResponse) => {
        if (networkResponse.ok) {
          cache.put(request, networkResponse.clone());
        }
        return networkResponse;
      })
      .catch(() => null);

    // Return cached version immediately if available
    if (cachedResponse) {
      // Update cache in background
      networkResponsePromise;
      return cachedResponse;
    }

    // Wait for network if no cache
    const networkResponse = await networkResponsePromise;

    if (networkResponse) {
      return networkResponse;
    }

    // Return offline page if available
    return caches.match('/offline') || new Response(
      '<html><body><h1>You are offline</h1><p>Please check your internet connection.</p></body></html>',
      { headers: { 'Content-Type': 'text/html' } }
    );

  } catch (error) {
    console.error('Service Worker: Error handling page request', error);
    return caches.match('/offline');
  }
}

// Handle static requests - Cache First strategy
async function handleStaticRequest(request) {
  try {
    const cache = await caches.open(STATIC_CACHE);
    const cachedResponse = await cache.match(request);

    if (cachedResponse) {
      return cachedResponse;
    }

    const networkResponse = await fetch(request);

    if (networkResponse.ok) {
      cache.put(request, networkResponse.clone());
    }

    return networkResponse;
  } catch (error) {
    console.error('Service Worker: Error handling static request', error);
    throw error;
  }
}


