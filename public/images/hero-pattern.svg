
    <svg width="100" height="100" viewBox="0 0 100 100" xmlns="http://www.w3.org/2000/svg">
      <defs>
        <pattern id="grid" width="20" height="20" patternUnits="userSpaceOnUse">
          <path d="M 20 0 L 0 0 0 20" fill="none" stroke="#E5E7EB" stroke-width="0.5" opacity="0.3"/>
        </pattern>
      </defs>
      <rect width="100" height="100" fill="url(#grid)"/>
      <circle cx="25" cy="25" r="2" fill="#3B82F6" opacity="0.4"/>
      <circle cx="75" cy="25" r="2" fill="#10B981" opacity="0.4"/>
      <circle cx="25" cy="75" r="2" fill="#8B5CF6" opacity="0.4"/>
      <circle cx="75" cy="75" r="2" fill="#F59E0B" opacity="0.4"/>
      <circle cx="50" cy="50" r="3" fill="#EF4444" opacity="0.4"/>
    </svg>
  