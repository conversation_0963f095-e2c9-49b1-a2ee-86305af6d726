import { NextRequest, NextResponse } from 'next/server';
import { createServerClient } from '@supabase/ssr';
import { getAdminUser } from '@/lib/auth-server';
import { trackApiCall, trackSecurityEvent, PerformanceMonitor } from '@/lib/monitoring';
import { getClientIP } from '@/lib/error-handler';

export const config = {
  matcher: [
    /*
     * Match all request paths except for the ones starting with:
     * - _next/static (static files)
     * - _next/image (image optimization files)
     * - favicon.ico (favicon file)
     * - public folder files
     */
    '/((?!_next/static|_next/image|favicon.ico|.*\\.(?:svg|png|jpg|jpeg|gif|webp)$).*)',
  ],
};

// Simple in-memory rate limiter for login attempts
// This would be better implemented with Redis in production
const loginAttempts = new Map<string, { count: number, timestamp: number }>();
const MAX_ATTEMPTS = 5;
const WINDOW_MS = 15 * 60 * 1000; // 15 minutes

export async function middleware(request: NextRequest) {
  const startTime = performance.now();
  const ip = getClientIP(request);
  const userAgent = request.headers.get('user-agent') || '';
  const path = request.nextUrl.pathname;
  const method = request.method;

  // Create response with enhanced security headers
  const response = NextResponse.next({
    headers: {
      // Cache control headers
      'Cache-Control': 'no-store, no-cache, must-revalidate, proxy-revalidate, max-age=0',
      'Pragma': 'no-cache',
      'Expires': '0',
      'Surrogate-Control': 'no-store',

      // Enhanced security headers
      'Content-Security-Policy':
        "default-src 'self'; " +
        "script-src 'self' 'unsafe-inline' 'unsafe-eval' https://storage.googleapis.com https://*.googletagmanager.com https://*.google-analytics.com https://vercel.live; " +
        "style-src 'self' 'unsafe-inline' https://fonts.googleapis.com; " +
        "img-src 'self' data: https: blob:; " +
        "font-src 'self' https://fonts.gstatic.com; " +
        "connect-src 'self' https://*.supabase.co https://soaoagcuubtzojytoati.supabase.co wss://soaoagcuubtzojytoati.supabase.co https://*.googleapis.com https://*.google-analytics.com https: wss:; " +
        "frame-src 'self' https://www.google.com https://www.youtube.com https://vercel.live; " +
        "object-src 'none'; " +
        "base-uri 'self'; " +
        "form-action 'self';",
      'X-Content-Type-Options': 'nosniff',
      'X-Frame-Options': 'DENY',
      'X-XSS-Protection': '1; mode=block',
      'Referrer-Policy': 'strict-origin-when-cross-origin',
      'Permissions-Policy': 'camera=(), microphone=(), geolocation=(self), payment=(), usb=()',
      'Strict-Transport-Security': 'max-age=63072000; includeSubDomains; preload',
      'Cross-Origin-Embedder-Policy': 'require-corp',
      'Cross-Origin-Opener-Policy': 'same-origin',
      'Cross-Origin-Resource-Policy': 'cross-origin',
      'X-Processing-Time': `${(performance.now() - startTime).toFixed(2)}ms`,
    }
  });

  // Add CORS headers for API routes
  if (path.startsWith('/api/')) {
    response.headers.set('Access-Control-Allow-Origin', '*');
    response.headers.set('Access-Control-Allow-Methods', 'GET, POST, PUT, DELETE, OPTIONS');
    response.headers.set('Access-Control-Allow-Headers', 'Content-Type, Authorization');
  }

  // Handle preflight requests
  if (method === 'OPTIONS') {
    return new Response(null, { status: 200, headers: response.headers });
  }

  const url = request.nextUrl.clone();
  
  // Enhanced rate limiting and security monitoring
  if (url.pathname === '/api/admin/login') {
    const now = Date.now();

    // Clean up old entries
    Array.from(loginAttempts.entries()).forEach(([key, data]) => {
      if (now - data.timestamp > WINDOW_MS) {
        loginAttempts.delete(key);
      }
    });

    const attempts = loginAttempts.get(ip);

    if (attempts && attempts.count >= MAX_ATTEMPTS && now - attempts.timestamp < WINDOW_MS) {
      // Track security event
      trackSecurityEvent({
        type: 'rate_limit_exceeded',
        severity: 'high',
        description: `Too many login attempts from IP: ${ip}`,
        ip,
        userAgent,
        metadata: { attempts: attempts.count, path: url.pathname },
      });

      return new NextResponse(
        JSON.stringify({ error: 'Too many login attempts. Please try again later.' }),
        { status: 429, headers: { 'Content-Type': 'application/json', 'Retry-After': '900' } }
      );
    }

    // Update attempts counter
    loginAttempts.set(ip, {
      count: (attempts?.count || 0) + 1,
      timestamp: attempts?.timestamp || now
    });

    // Log login attempt in development only
    if (process.env.NODE_ENV === 'development') {
      console.log(`🔐 Login attempt from ${ip} (${(attempts?.count || 0) + 1}/${MAX_ATTEMPTS})`);
    }
  }

  // Monitor API calls
  if (path.startsWith('/api/')) {
    const monitor = new PerformanceMonitor(`api_${method}_${path}`, {
      ip,
      userAgent,
      path,
      method,
    });

    // Add request ID for debugging
    if (process.env.NODE_ENV === 'development') {
      response.headers.set('X-Request-ID', crypto.randomUUID());
    }
  }

  // Security monitoring for sensitive endpoints
  const sensitiveEndpoints = [
    '/api/admin/',
    '/api/trips-photos/verify-password',
    '/api/contact',
    '/api/inquiries',
  ];

  if (sensitiveEndpoints.some(endpoint => path.startsWith(endpoint))) {
    if (process.env.NODE_ENV === 'development') {
      console.log(`🔒 Sensitive endpoint access: ${method} ${path} from ${ip}`);
    }
  }

  // Check if request is for admin routes
  if (url.pathname.startsWith('/admin') && url.pathname !== '/admin/login') {
    try {
      // Create modern Supabase client for middleware
      const supabase = createServerClient(
        process.env.NEXT_PUBLIC_SUPABASE_URL!,
        process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!,
        {
          cookies: {
            get(name: string) {
              return request.cookies.get(name)?.value
            },
            set(name: string, value: string, options: any) {
              response.cookies.set({ name, value, ...options })
            },
            remove(name: string, options: any) {
              response.cookies.set({ name, value: '', ...options })
            },
          },
        }
      );

      // Get the current user session
      const { data: { user }, error: userError } = await supabase.auth.getUser();

      if (userError || !user) {
        // Enhanced debugging for authentication issues
        if (process.env.NODE_ENV === 'development') {
          console.log('🔐 Middleware auth check failed:', {
            path: url.pathname,
            userError: userError?.message,
            hasUser: !!user,
            cookies: request.cookies.getAll().map(c => ({ name: c.name, hasValue: !!c.value }))
          });
        }

        // No valid session, redirect to login
        const redirectResponse = NextResponse.redirect(new URL('/admin/login', request.url));
        return redirectResponse;
      }

      // Get admin user with roles
      const adminUser = await getAdminUser(user.id);

      if (!adminUser || !adminUser.is_active || adminUser.roles.length === 0) {
        // Enhanced debugging for admin user issues
        if (process.env.NODE_ENV === 'development') {
          console.log('🔐 Admin user check failed:', {
            path: url.pathname,
            userId: user.id,
            hasAdminUser: !!adminUser,
            isActive: adminUser?.is_active,
            rolesCount: adminUser?.roles?.length || 0
          });
        }

        // User is not an admin or is inactive, redirect to login
        const redirectResponse = NextResponse.redirect(new URL('/admin/login', request.url));
        return redirectResponse;
      }

      // Success - log in development
      if (process.env.NODE_ENV === 'development') {
        console.log('✅ Middleware auth success:', {
          path: url.pathname,
          userId: user.id,
          username: adminUser.username,
          roles: adminUser.roles.map(r => r.name)
        });
      }

      // Update last login time (non-blocking)
      try {
        const { createAdminClient } = await import('@/lib/auth');
        const adminSupabase = createAdminClient();
        await adminSupabase
          .from('admin_profiles')
          .update({ last_login_at: new Date().toISOString() })
          .eq('id', user.id);
      } catch (updateError) {
        // Non-critical error, just log it
        console.warn('Failed to update last login time:', updateError);
      }

      // Continue with the response
      return response;
    } catch (error) {
      console.error('Error verifying admin session:', error);
      // On error, redirect to login
      return NextResponse.redirect(new URL('/admin/login', request.url));
    }
  }

  return response;
} 