'use client';

import React, { useState, useEffect } from 'react';
import Link from 'next/link';
import Image from 'next/image';
import { usePathname } from 'next/navigation';
import { motion, AnimatePresence } from 'framer-motion';
import { Menu, X, Phone, Mail, MapPin } from 'lucide-react';
import { COMPANY_INFO, NAVIGATION_ITEMS, SOCIAL_LINKS } from '@/lib/constants';
import { cn } from '@/lib/utils';


export default function Header() {
  const [isMenuOpen, setIsMenuOpen] = useState(false);
  const [isScrolled, setIsScrolled] = useState(false);
  const pathname = usePathname();

  useEffect(() => {
    const handleScroll = () => {
      setIsScrolled(window.scrollY > 10);
    };

    window.addEventListener('scroll', handleScroll);
    return () => window.removeEventListener('scroll', handleScroll);
  }, []);

  const isActivePath = (href: string) => {
    if (href === '/') return pathname === '/';
    // Handle specific conflicts between similar paths
    if (href === '/trips') {
      return pathname === '/trips' || (pathname.startsWith('/trips/') && !pathname.startsWith('/trips-photos'));
    }
    if (href === '/trips-photos') {
      return pathname.startsWith('/trips-photos');
    }
    // For other paths, use the original logic
    return pathname.startsWith(href);
  };

  return (
    <>

      {/* Main Header */}
      <header
        className={cn(
          'sticky top-0 z-50 transition-all duration-300',
          isScrolled
            ? 'bg-white/80 backdrop-blur-xl shadow-lg border-b border-white/20'
            : 'bg-white/90 backdrop-blur-lg shadow-sm'
        )}
      >
        <div className="container-custom px-4 md:px-8 lg:px-12">
          <div className="flex items-center justify-between h-16 lg:h-20">
            {/* Logo */}
            <Link href="/" className="flex items-center">
              <div className="relative h-16 w-16 lg:h-28 lg:w-28">
                <Image
                  src="/images/static/logos/logo-hd.png"
                  alt={COMPANY_INFO.name}
                  fill
                  className="object-contain"
                  priority
                />
              </div>
            </Link>

            {/* Desktop Navigation */}
            <nav className="hidden lg:flex items-center justify-end w-full space-x-4">
              {NAVIGATION_ITEMS.map((item) => (
                <Link
                  key={item.name}
                  href={item.href as any}
                  className={cn(
                    'relative text-sm font-medium transition-all duration-300 hover:text-primary-600 hover:scale-105',
                    'px-2 py-1 rounded-lg hover:bg-white/50 hover:backdrop-blur-sm',
                    isActivePath(item.href)
                      ? 'text-primary-600 bg-primary-50/80 backdrop-blur-sm'
                      : 'text-gray-700'
                  )}
                >
                  {item.name}
                  {isActivePath(item.href) && (
                    <span className="absolute bottom-0 left-1/2 transform -translate-x-1/2 w-1 h-1 bg-primary-600 rounded-full"></span>
                  )}
                </Link>
              ))}
            </nav>

            {/* Mobile Menu Button */}
            <div className="flex items-center">
              <button
                onClick={() => setIsMenuOpen(!isMenuOpen)}
                className="lg:hidden p-2 rounded-lg text-gray-700 hover:text-primary-600 hover:bg-white/50 hover:backdrop-blur-sm transition-all duration-300 hover:scale-105"
                aria-label="Toggle menu"
              >
                {isMenuOpen ? (
                  <X className="h-6 w-6" />
                ) : (
                  <Menu className="h-6 w-6" />
                )}
              </button>
            </div>
          </div>
        </div>

        {/* Mobile Menu */}
        <AnimatePresence>
          {isMenuOpen && (
            <motion.div
              initial={{ opacity: 0, height: 0 }}
              animate={{ opacity: 1, height: 'auto' }}
              exit={{ opacity: 0, height: 0 }}
              className="lg:hidden bg-white/90 backdrop-blur-xl border-t border-white/20"
            >
              <div className="container-custom py-4">
                <nav className="flex flex-col space-y-2">
                  {NAVIGATION_ITEMS.map((item) => (
                    <Link
                      key={item.name}
                      href={item.href as any}
                      className={cn(
                        'text-base font-medium transition-all duration-300 px-4 py-3 rounded-lg hover:scale-105',
                        isActivePath(item.href)
                          ? 'text-primary-600 bg-primary-50/80 backdrop-blur-sm'
                          : 'text-gray-700 hover:text-primary-600 hover:bg-white/50 hover:backdrop-blur-sm'
                      )}
                      onClick={() => setIsMenuOpen(false)}
                    >
                      {item.name}
                    </Link>
                  ))}
                </nav>
              </div>
            </motion.div>
          )}
        </AnimatePresence>
      </header>
    </>
  );
}
