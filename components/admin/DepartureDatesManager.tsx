'use client'

import React, { useState } from 'react'
import { motion, AnimatePresence } from 'framer-motion'
import { Calendar, Plus, Trash2, CheckCircle, ChevronDown, ChevronUp } from 'lucide-react'
import DatePicker from '@/components/ui/DatePicker'

interface DepartureDatesManagerProps {
  dates: string[] | null
  onChange: (dates: string[]) => void
  className?: string
}

export default function DepartureDatesManager({
  dates = [],
  onChange,
  className = ''
}: DepartureDatesManagerProps) {
  const [newDate, setNewDate] = useState('')
  const [isExpanded, setIsExpanded] = useState(false)
  const dateList = dates || []

  // Add a new departure date
  const addDate = () => {
    if (newDate && !dateList.includes(newDate)) {
      const updatedDates = [...dateList, newDate].sort() // Sort dates chronologically
      onChange(updatedDates)
      setNewDate('')
    }
  }

  // Remove a departure date
  const removeDate = (index: number) => {
    const updatedDates = dateList.filter((_, i) => i !== index)
    onChange(updatedDates)
  }

  // Update an existing departure date
  const updateDate = (index: number, newDateValue: string) => {
    if (newDateValue) {
      // Allow updating to the same value (not blocking unchanged edits)
      const updatedDates = [...dateList]
      updatedDates[index] = newDateValue
      updatedDates.sort() // Sort dates chronologically
      onChange(updatedDates)
    }
  }

  // Format date for display
  const formatDateForDisplay = (dateString: string) => {
    try {
      const date = new Date(dateString)
      return date.toLocaleDateString('en-US', {
        weekday: 'short',
        year: 'numeric',
        month: 'short',
        day: 'numeric'
      })
    } catch {
      return dateString
    }
  }

  // Get minimum date (today)
  const getMinDate = () => {
    const today = new Date()
    return today.toISOString().split('T')[0]
  }

  return (
    <div className={`space-y-4 ${className}`}>
      {/* Collapsible Header */}
      <button
        onClick={() => setIsExpanded(!isExpanded)}
        className="w-full flex items-center justify-between p-4 bg-gray-50 hover:bg-gray-100 rounded-lg transition-colors"
      >
        <div className="flex items-center gap-2">
          <Calendar className="w-5 h-5 text-blue-600" />
          <h3 className="text-xl font-semibold text-gray-900">Departure Dates</h3>
        </div>
        <div className="flex items-center gap-3">
          <div className="text-sm text-gray-600">
            {dateList.length} {dateList.length === 1 ? 'date' : 'dates'} scheduled
          </div>
          {isExpanded ? (
            <ChevronUp className="w-5 h-5 text-gray-500" />
          ) : (
            <ChevronDown className="w-5 h-5 text-gray-500" />
          )}
        </div>
      </button>

      {/* Collapsible Content */}
      <AnimatePresence>
        {isExpanded && (
          <motion.div
            initial={{ height: 0, opacity: 0 }}
            animate={{ height: 'auto', opacity: 1 }}
            exit={{ height: 0, opacity: 0 }}
            transition={{ duration: 0.3 }}
            className="overflow-hidden space-y-4"
          >
            {/* Existing dates list */}
            {dateList.length > 0 && (
              <div className="space-y-3">
                {dateList.map((date, index) => (
                  <motion.div
                    key={`${date}-${index}`}
                    initial={{ opacity: 0, y: 10 }}
                    animate={{ opacity: 1, y: 0 }}
                    exit={{ opacity: 0, y: -10 }}
                    className="flex items-center gap-3 p-3 bg-blue-50 rounded-lg border border-blue-200 group"
                  >
                    <CheckCircle className="w-4 h-4 text-green-600 flex-shrink-0" />

                    <div className="flex-1">
                      <input
                        type="date"
                        value={date}
                        onChange={(e) => updateDate(index, e.target.value)}
                        min={getMinDate()}
                        className="border-none bg-transparent p-0 text-sm font-medium text-blue-800 w-full focus:outline-none"
                      />
                    </div>

                    <button
                      onClick={() => removeDate(index)}
                      className="opacity-0 group-hover:opacity-100 text-red-500 hover:text-red-700 transition-all duration-200 p-1 rounded hover:bg-red-50"
                      title="Remove this departure date"
                    >
                      <Trash2 className="w-4 h-4" />
                    </button>
                  </motion.div>
                ))}
              </div>
            )}

            {/* Add new date */}
            <div className="border-2 border-dashed border-gray-300 rounded-lg p-4 hover:border-blue-400 transition-colors">
              <div className="flex items-center gap-3">
                <button
                  onClick={addDate}
                  disabled={!newDate || dateList.includes(newDate)}
                  className="w-8 h-8 flex items-center justify-center bg-blue-600 text-white rounded-full hover:bg-blue-700 disabled:bg-gray-300 disabled:cursor-not-allowed transition-colors flex-shrink-0"
                  title="Add departure date"
                >
                  <Plus className="w-4 h-4" />
                </button>
                <div className="flex-1">
                  <input
                    type="date"
                    value={newDate}
                    onChange={(e) => setNewDate(e.target.value)}
                    min={getMinDate()}
                    placeholder="Select departure date"
                    className="w-full p-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                  />
                </div>
              </div>

              {newDate && dateList.includes(newDate) && (
                <div className="mt-2 text-sm text-red-600">
                  This date is already added to the departure schedule.
                </div>
              )}
            </div>

            {/* Empty state */}
            {dateList.length === 0 && (
              <div className="text-center py-8 text-gray-500 bg-gray-50 rounded-lg">
                <Calendar className="w-12 h-12 mx-auto mb-3 text-gray-300" />
                <p className="text-sm font-medium">No departure dates scheduled</p>
                <p className="text-xs mt-1">Add departure dates to make this trip bookable</p>
              </div>
            )}

            {/* Helper text */}
            <div className="text-xs text-gray-500 bg-blue-50 p-3 rounded-lg">
              <strong>Tip:</strong> Add multiple departure dates throughout the year to give travelers more booking options.
              Dates are automatically sorted chronologically and must be unique.
            </div>
          </motion.div>
        )}
      </AnimatePresence>
    </div>
  )
}
