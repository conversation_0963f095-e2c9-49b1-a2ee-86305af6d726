'use client';

import { useState, memo, useCallback } from 'react';
import { useRouter } from 'next/navigation';
import { useAuth } from '@/components/providers/AuthProvider';
import ConfirmationModal from '@/components/ui/ConfirmationModal';
import {
  Users,
  Plus,
  Edit,
  Trash2,
  Shield,
  CheckCircle,
  XCircle,
  AlertTriangle
} from 'lucide-react';
import {
  useAdminUsers,
  useDeleteAdminUser,
  type AdminUser
} from '@/hooks/useAdminUsers';
import LoadingSpinner from '@/components/ui/LoadingSpinner';

// Types are now imported from the hook

export default function AdminUserManagement() {
  const router = useRouter();
  const { hasPermission, isSuperAdmin, isOwner } = useAuth();
  const [showDeleteModal, setShowDeleteModal] = useState(false);
  const [userToDelete, setUserToDelete] = useState<{ id: string; name: string } | null>(null);

  // React Query hooks
  const {
    data: usersData,
    isLoading: usersLoading,
    error: usersError
  } = useAdminUsers();

  const deleteUserMutation = useDeleteAdminUser();

  // Derived data
  const users = usersData?.users || [];
  const loading = usersLoading;
  const error = usersError;

  // Check if user has permission to manage users
  if (!hasPermission('users', 'read')) {
    return (
      <div className="text-center py-8">
        <Shield className="mx-auto h-12 w-12 text-gray-300 mb-4" />
        <p className="text-gray-500">You don't have permission to view admin users.</p>
      </div>
    );
  }

  // Navigation handlers
  const handleCreateUser = useCallback(() => {
    router.push('/admin/users/new');
  }, [router]);

  const handleEditUser = useCallback((user: AdminUser) => {
    router.push(`/admin/users/${user.id}/edit`);
  }, [router]);

  const handleDeleteClick = useCallback((user: AdminUser) => {
    setUserToDelete({
      id: user.id,
      name: user.full_name || user.username || 'Unnamed User'
    });
    setShowDeleteModal(true);
  }, []);

  const handleDeleteConfirm = async () => {
    if (!userToDelete) return;

    try {
      await deleteUserMutation.mutateAsync(userToDelete.id);
      setShowDeleteModal(false);
      setUserToDelete(null);
    } catch (error) {
      // Error is handled by the mutation hook with toast
      // Keep modal open on failure so user can retry
      console.error('Failed to delete user:', error);
    }
  };

  const handleDeleteCancel = () => {
    setShowDeleteModal(false);
    setUserToDelete(null);
  };

  if (loading) {
    return (
      <div className="flex justify-center items-center py-8">
        <LoadingSpinner />
      </div>
    );
  }

  return (
    <div className="space-y-6">
      <div className="flex justify-between items-center">
        <div>
          <h1 className="text-2xl font-bold text-gray-900">Admin Users</h1>
          <p className="text-gray-600">Manage admin users and their roles</p>
        </div>
        {hasPermission('users', 'create') && (
          <button
            onClick={handleCreateUser}
            className="inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-blue-600 hover:bg-blue-700"
          >
            <Plus className="h-4 w-4 mr-2" />
            Add User
          </button>
        )}
      </div>

      {error && (
        <div className="bg-red-50 border border-red-200 rounded-md p-4">
          <div className="flex">
            <AlertTriangle className="h-5 w-5 text-red-400" />
            <div className="ml-3">
              <p className="text-sm text-red-800">{error?.message || 'An error occurred'}</p>
            </div>
          </div>
        </div>
      )}

      <div className="bg-white shadow overflow-hidden sm:rounded-md">
        <ul className="divide-y divide-gray-200">
          {users.map((user) => (
            <UserListItem
              key={user.id}
              user={user}
              canEdit={isSuperAdmin() || isOwner()}
              onEdit={handleEditUser}
              onDelete={handleDeleteClick}
            />
          ))}
        </ul>
      </div>

      {users.length === 0 && (
        <div className="text-center py-8">
          <Users className="mx-auto h-12 w-12 text-gray-300 mb-4" />
          <p className="text-gray-500">No admin users found.</p>
        </div>
      )}

      {/* Delete Confirmation Modal */}
      {userToDelete && (
        <ConfirmationModal
          isOpen={showDeleteModal}
          onClose={handleDeleteCancel}
          onConfirm={handleDeleteConfirm}
          title="Delete Admin User"
          message={`Are you sure you want to delete "${userToDelete.name}"?\n\nThis action cannot be undone and will permanently remove the user and revoke their admin access.`}
          confirmText="Delete User"
          variant="danger"
          loading={deleteUserMutation.isPending}
        />
      )}
    </div>
  );
}

// Memoized UserListItem component for better performance
const UserListItem = memo(({
  user,
  canEdit,
  onEdit,
  onDelete
}: {
  user: AdminUser;
  canEdit: boolean;
  onEdit: (user: AdminUser) => void;
  onDelete: (user: AdminUser) => void;
}) => {
  const handleEdit = useCallback(() => onEdit(user), [onEdit, user]);
  const handleDelete = useCallback(() => onDelete(user), [onDelete, user]);

  return (
    <li className="px-6 py-4">
      <div className="flex items-center justify-between">
        <div className="flex-1">
          <div className="flex items-center">
            <div className="flex-shrink-0">
              <div className="h-10 w-10 rounded-full bg-gray-300 flex items-center justify-center">
                <Users className="h-5 w-5 text-gray-600" />
              </div>
            </div>
            <div className="ml-4">
              <div className="flex items-center">
                <p className="text-sm font-medium text-gray-900">
                  {user.full_name || user.username || 'Unnamed User'}
                </p>
                {user.is_active ? (
                  <CheckCircle className="ml-2 h-4 w-4 text-green-500" />
                ) : (
                  <XCircle className="ml-2 h-4 w-4 text-red-500" />
                )}
              </div>
              <div className="flex flex-wrap gap-1 mt-1">
                {user.roles.map(role => (
                  <span
                    key={role.id}
                    className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-blue-100 text-blue-800"
                  >
                    {role.name.replace('_', ' ').toUpperCase()}
                  </span>
                ))}
              </div>
              <p className="text-sm text-gray-500">
                Last login: {user.last_login_at ? new Date(user.last_login_at).toLocaleDateString() : 'Never'}
              </p>
            </div>
          </div>
        </div>
        {canEdit && (
          <div className="flex space-x-2">
            <button
              onClick={handleEdit}
              className="text-blue-600 hover:text-blue-900"
              title="Edit user"
            >
              <Edit className="h-4 w-4" />
            </button>
            <button
              onClick={handleDelete}
              className="text-red-600 hover:text-red-900"
              title="Delete user"
            >
              <Trash2 className="h-4 w-4" />
            </button>
          </div>
        )}
      </div>
    </li>
  );
});

UserListItem.displayName = 'UserListItem';
