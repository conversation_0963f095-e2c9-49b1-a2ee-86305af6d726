'use client';

import React, { useState } from 'react';
import { useAdminPerformanceMonitoring } from '@/hooks/useAdminPerformanceMonitoring';
import { useToast } from '@/hooks/useToast';
import { 
  Activity, 
  AlertTriangle, 
  CheckCircle, 
  XCircle, 
  Info,
  Minimize2,
  Maximize2,
  RefreshCw
} from 'lucide-react';

interface PerformanceMonitorProps {
  enabled?: boolean;
  showInProduction?: boolean;
}

export default function PerformanceMonitor({ 
  enabled = true, 
  showInProduction = false 
}: PerformanceMonitorProps) {
  const toast = useToast();
  const [isExpanded, setIsExpanded] = useState(false);
  const [showDetails, setShowDetails] = useState(false);

  // Only show in development unless explicitly enabled for production
  const shouldShow = enabled && (process.env.NODE_ENV === 'development' || showInProduction);

  const { metrics, alerts, runPerformanceCheck, clearAlerts, isMonitoring } = useAdminPerformanceMonitoring({
    enabled: shouldShow,
    onAlert: (alert) => {
      if (alert.type === 'error') {
        toast.error(`Performance Issue: ${alert.message}`);
      } else if (alert.type === 'warning') {
        toast.error(`Performance Warning: ${alert.message}`);
      }
    },
  });

  if (!shouldShow) return null;

  const getAlertIcon = (type: 'warning' | 'error' | 'info') => {
    switch (type) {
      case 'error':
        return <XCircle className="w-4 h-4 text-red-500" />;
      case 'warning':
        return <AlertTriangle className="w-4 h-4 text-yellow-500" />;
      case 'info':
        return <Info className="w-4 h-4 text-blue-500" />;
    }
  };

  const formatMetric = (value: number, unit: string = 'ms') => {
    if (unit === 'bytes') {
      if (value > 1024 * 1024) {
        return `${(value / (1024 * 1024)).toFixed(2)} MB`;
      } else if (value > 1024) {
        return `${(value / 1024).toFixed(2)} KB`;
      }
      return `${value} B`;
    }
    
    if (unit === 'ms' && value > 1000) {
      return `${(value / 1000).toFixed(2)}s`;
    }
    
    return `${value.toFixed(2)}${unit}`;
  };

  const getPerformanceScore = () => {
    if (!metrics.loadTime) return 0;
    
    let score = 100;
    
    // Deduct points for slow metrics
    if (metrics.loadTime > 3000) score -= 20;
    if (metrics.firstPaint > 1000) score -= 15;
    if (metrics.largestContentfulPaint > 2500) score -= 15;
    if (metrics.firstInputDelay > 100) score -= 10;
    if (metrics.cumulativeLayoutShift > 0.1) score -= 10;
    if (metrics.memoryUsage > 100 * 1024 * 1024) score -= 10;
    if (alerts.length > 0) score -= alerts.length * 5;
    
    return Math.max(0, score);
  };

  const performanceScore = getPerformanceScore();
  const scoreColor = performanceScore >= 80 ? 'text-green-500' : 
                    performanceScore >= 60 ? 'text-yellow-500' : 'text-red-500';

  return (
    <div className="fixed bottom-4 right-4 z-50">
      {/* Compact view */}
      {!isExpanded && (
        <button
          className="bg-white rounded-lg shadow-lg border p-3 cursor-pointer hover:shadow-xl transition-shadow focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2"
          onClick={() => setIsExpanded(true)}
          onKeyDown={(e) => {
            if (e.key === 'Enter' || e.key === ' ') {
              e.preventDefault();
              setIsExpanded(true);
            }
          }}
          aria-label={`Performance Monitor - Score: ${performanceScore}/100${alerts.length > 0 ? `, ${alerts.length} alerts` : ''}`}
          aria-expanded={false}
          role="button"
          tabIndex={0}
        >
          <div className="flex items-center space-x-2">
            <Activity className="w-5 h-5 text-blue-500" aria-hidden="true" />
            <span className={`font-semibold ${scoreColor}`} aria-label={`Performance score: ${performanceScore} out of 100`}>
              {performanceScore}
            </span>
            {alerts.length > 0 && (
              <div className="flex items-center space-x-1" role="status" aria-live="polite">
                <AlertTriangle className="w-4 h-4 text-yellow-500" aria-hidden="true" />
                <span className="text-sm text-yellow-600" aria-label={`${alerts.length} performance alerts`}>
                  {alerts.length}
                </span>
              </div>
            )}
            <Maximize2 className="w-4 h-4 text-gray-400" aria-hidden="true" />
          </div>
        </button>
      )}

      {/* Expanded view */}
      {isExpanded && (
        <div
          className="bg-white rounded-lg shadow-xl border w-96 max-h-96 overflow-hidden"
          role="dialog"
          aria-labelledby="performance-monitor-title"
          aria-modal="false"
        >
          {/* Header */}
          <div className="bg-gray-50 px-4 py-3 border-b flex items-center justify-between">
            <div className="flex items-center space-x-2">
              <Activity className="w-5 h-5 text-blue-500" aria-hidden="true" />
              <h3 id="performance-monitor-title" className="font-semibold text-gray-900">Performance Monitor</h3>
              <span className={`font-bold ${scoreColor}`} aria-label={`Performance score: ${performanceScore} out of 100`}>
                {performanceScore}/100
              </span>
            </div>
            <div className="flex items-center space-x-2">
              <button
                onClick={runPerformanceCheck}
                className="p-1 hover:bg-gray-200 rounded focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-1"
                aria-label="Refresh performance metrics"
                disabled={isMonitoring}
              >
                <RefreshCw className={`w-4 h-4 text-gray-600 ${isMonitoring ? 'animate-spin' : ''}`} aria-hidden="true" />
              </button>
              <button
                onClick={() => setIsExpanded(false)}
                className="p-1 hover:bg-gray-200 rounded focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-1"
                aria-label="Minimize performance monitor"
              >
                <Minimize2 className="w-4 h-4 text-gray-600" aria-hidden="true" />
              </button>
            </div>
          </div>

          {/* Content */}
          <div className="p-4 max-h-80 overflow-y-auto">
            {/* Alerts */}
            {alerts.length > 0 && (
              <div className="mb-4" role="region" aria-labelledby="alerts-heading">
                <div className="flex items-center justify-between mb-2">
                  <h4 id="alerts-heading" className="font-medium text-gray-900">
                    Performance Alerts ({alerts.length})
                  </h4>
                  <button
                    onClick={clearAlerts}
                    className="text-sm text-blue-600 hover:text-blue-800 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-1"
                    aria-label={`Clear all ${alerts.length} performance alerts`}
                  >
                    Clear
                  </button>
                </div>
                <div className="space-y-2" role="list" aria-label="Performance alerts">
                  {alerts.slice(0, 3).map((alert, index) => (
                    <div
                      key={index}
                      className="flex items-start space-x-2 p-2 bg-gray-50 rounded"
                      role="listitem"
                      aria-labelledby={`alert-${index}-message`}
                      aria-describedby={`alert-${index}-suggestion`}
                    >
                      <div aria-label={`${alert.type} alert`}>
                        {getAlertIcon(alert.type)}
                      </div>
                      <div className="flex-1 min-w-0">
                        <p id={`alert-${index}-message`} className="text-sm text-gray-900 truncate">
                          {alert.message}
                        </p>
                        <p id={`alert-${index}-suggestion`} className="text-xs text-gray-600 mt-1">
                          {alert.suggestion}
                        </p>
                      </div>
                    </div>
                  ))}
                  {alerts.length > 3 && (
                    <p className="text-sm text-gray-600" aria-live="polite">
                      +{alerts.length - 3} more alerts
                    </p>
                  )}
                </div>
              </div>
            )}

            {/* Key Metrics */}
            <div className="mb-4" role="region" aria-labelledby="key-metrics-heading">
              <h4 id="key-metrics-heading" className="font-medium text-gray-900 mb-2">Key Metrics</h4>
              <div className="grid grid-cols-2 gap-2 text-sm" role="list" aria-label="Key performance metrics">
                <div className="flex justify-between" role="listitem">
                  <span className="text-gray-600">Load Time:</span>
                  <span
                    className={metrics.loadTime > 3000 ? 'text-red-600' : 'text-green-600'}
                    aria-label={`Load time: ${formatMetric(metrics.loadTime || 0)} ${metrics.loadTime > 3000 ? '(slow)' : '(good)'}`}
                  >
                    {formatMetric(metrics.loadTime || 0)}
                  </span>
                </div>
                <div className="flex justify-between" role="listitem">
                  <span className="text-gray-600">First Paint:</span>
                  <span
                    className={metrics.firstPaint > 1000 ? 'text-red-600' : 'text-green-600'}
                    aria-label={`First paint: ${formatMetric(metrics.firstPaint || 0)} ${metrics.firstPaint > 1000 ? '(slow)' : '(good)'}`}
                  >
                    {formatMetric(metrics.firstPaint || 0)}
                  </span>
                </div>
                <div className="flex justify-between" role="listitem">
                  <span className="text-gray-600">Memory:</span>
                  <span
                    className={metrics.memoryUsage > 100 * 1024 * 1024 ? 'text-red-600' : 'text-green-600'}
                    aria-label={`Memory usage: ${formatMetric(metrics.memoryUsage || 0, 'bytes')} ${metrics.memoryUsage > 100 * 1024 * 1024 ? '(high)' : '(normal)'}`}
                  >
                    {formatMetric(metrics.memoryUsage || 0, 'bytes')}
                  </span>
                </div>
                <div className="flex justify-between" role="listitem">
                  <span className="text-gray-600">Queries:</span>
                  <span
                    className={metrics.queryCount > 50 ? 'text-red-600' : 'text-green-600'}
                    aria-label={`Query count: ${metrics.queryCount || 0} ${metrics.queryCount > 50 ? '(high)' : '(normal)'}`}
                  >
                    {metrics.queryCount || 0}
                  </span>
                </div>
              </div>
            </div>

            {/* Toggle detailed view */}
            <button
              onClick={() => setShowDetails(!showDetails)}
              className="text-sm text-blue-600 hover:text-blue-800 mb-2 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-1"
              aria-expanded={showDetails}
              aria-controls="detailed-metrics"
              aria-label={`${showDetails ? 'Hide' : 'Show'} detailed performance metrics`}
            >
              {showDetails ? 'Hide' : 'Show'} detailed metrics
            </button>

            {/* Detailed Metrics */}
            {showDetails && (
              <div
                id="detailed-metrics"
                className="space-y-2 text-sm"
                role="region"
                aria-labelledby="detailed-metrics-heading"
                aria-live="polite"
              >
                <h5 id="detailed-metrics-heading" className="sr-only">Detailed Performance Metrics</h5>
                <div className="grid grid-cols-2 gap-2" role="list" aria-label="Detailed performance metrics">
                  <div className="flex justify-between" role="listitem">
                    <span className="text-gray-600">DOM Ready:</span>
                    <span aria-label={`DOM content loaded in ${formatMetric(metrics.domContentLoaded || 0)}`}>
                      {formatMetric(metrics.domContentLoaded || 0)}
                    </span>
                  </div>
                  <div className="flex justify-between" role="listitem">
                    <span className="text-gray-600">LCP:</span>
                    <span aria-label={`Largest contentful paint in ${formatMetric(metrics.largestContentfulPaint || 0)}`}>
                      {formatMetric(metrics.largestContentfulPaint || 0)}
                    </span>
                  </div>
                  <div className="flex justify-between" role="listitem">
                    <span className="text-gray-600">FID:</span>
                    <span aria-label={`First input delay of ${formatMetric(metrics.firstInputDelay || 0)}`}>
                      {formatMetric(metrics.firstInputDelay || 0)}
                    </span>
                  </div>
                  <div className="flex justify-between" role="listitem">
                    <span className="text-gray-600">CLS:</span>
                    <span aria-label={`Cumulative layout shift score of ${(metrics.cumulativeLayoutShift || 0).toFixed(3)}`}>
                      {(metrics.cumulativeLayoutShift || 0).toFixed(3)}
                    </span>
                  </div>
                  <div className="flex justify-between" role="listitem">
                    <span className="text-gray-600">Cache Size:</span>
                    <span aria-label={`Cache size of ${formatMetric(metrics.cacheSize || 0, 'bytes')}`}>
                      {formatMetric(metrics.cacheSize || 0, 'bytes')}
                    </span>
                  </div>
                  <div className="flex justify-between" role="listitem">
                    <span className="text-gray-600">Stale Queries:</span>
                    <span aria-label={`${metrics.staleCacheEntries || 0} stale cache entries`}>
                      {metrics.staleCacheEntries || 0}
                    </span>
                  </div>
                </div>
              </div>
            )}

            {/* Status */}
            <div className="mt-4 pt-3 border-t">
              <div className="flex items-center justify-between text-sm">
                <span className="text-gray-600">Monitoring:</span>
                <div className="flex items-center space-x-1">
                  {isMonitoring ? (
                    <>
                      <CheckCircle className="w-4 h-4 text-green-500" />
                      <span className="text-green-600">Active</span>
                    </>
                  ) : (
                    <>
                      <XCircle className="w-4 h-4 text-red-500" />
                      <span className="text-red-600">Inactive</span>
                    </>
                  )}
                </div>
              </div>
            </div>
          </div>
        </div>
      )}
    </div>
  );
}
