'use client';

import React, { useState, useRef } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { Send, CheckCircle, AlertCircle, RefreshCw } from 'lucide-react';
import { contactFormSchema, type ContactFormData } from '@/lib/validation-schemas';
import {
  validatePhoneNumber,
  validateEmailAddress,
  validateName,
  validateMessage,
  validateSubject,
  handlePhoneInput,
  ValidationResult
} from '@/lib/validation-utils';
import { hapticFeedback } from '@/components/mobile/MobileEnhancements';

interface SimpleCaptchaProps {
  onVerify: (isValid: boolean) => void;
  onRefresh?: () => void;
}

// Simple math CAPTCHA component
function SimpleCaptcha({ onVerify, onRefresh }: SimpleCaptchaProps) {
  const [num1] = useState(() => Math.floor(Math.random() * 10) + 1);
  const [num2] = useState(() => Math.floor(Math.random() * 10) + 1);
  const [userAnswer, setUserAnswer] = useState('');
  const [isVerified, setIsVerified] = useState(false);

  const correctAnswer = num1 + num2;

  const handleAnswerChange = (value: string) => {
    setUserAnswer(value);
    const isCorrect = parseInt(value) === correctAnswer;
    setIsVerified(isCorrect);
    onVerify(isCorrect);
    
    if (isCorrect) {
      hapticFeedback.success();
    }
  };

  const handleRefresh = () => {
    setUserAnswer('');
    setIsVerified(false);
    onVerify(false);
    onRefresh?.();
  };

  return (
    <div className="space-y-2">
      <label className="block text-sm font-medium text-gray-700">
        Security Check *
      </label>
      <div className="flex items-center gap-3">
        <div className="flex items-center gap-2 text-lg font-mono bg-gray-100 px-3 py-2 rounded-lg">
          <span>{num1}</span>
          <span>+</span>
          <span>{num2}</span>
          <span>=</span>
        </div>
        <input
          type="number"
          value={userAnswer}
          onChange={(e) => handleAnswerChange(e.target.value)}
          className={`w-20 px-3 py-2 border rounded-lg focus:outline-none focus:ring-2 transition-colors ${
            userAnswer && isVerified
              ? 'border-green-500 focus:ring-green-500 bg-green-50'
              : userAnswer && !isVerified
              ? 'border-red-500 focus:ring-red-500 bg-red-50'
              : 'border-gray-300 focus:ring-blue-500'
          }`}
          placeholder="?"
          min="0"
          max="20"
        />
        <button
          type="button"
          onClick={handleRefresh}
          className="p-2 text-gray-500 hover:text-gray-700 transition-colors"
          title="Refresh CAPTCHA"
        >
          <RefreshCw className="w-4 h-4" />
        </button>
        {userAnswer && (
          <motion.div
            initial={{ scale: 0 }}
            animate={{ scale: 1 }}
            className={`w-6 h-6 ${isVerified ? 'text-green-500' : 'text-red-500'}`}
          >
            {isVerified ? <CheckCircle className="w-6 h-6" /> : <AlertCircle className="w-6 h-6" />}
          </motion.div>
        )}
      </div>
    </div>
  );
}

interface EnhancedContactFormProps {
  onSubmit?: (data: ContactFormData) => Promise<void>;
  className?: string;
}

export default function EnhancedContactForm({ onSubmit, className = '' }: EnhancedContactFormProps) {
  const [formData, setFormData] = useState<Partial<ContactFormData>>({
    name: '',
    email: '',
    phone: '',
    subject: '',
    message: '',
  });
  
  const [errors, setErrors] = useState<Partial<Record<keyof ContactFormData, string>>>({});
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [isSubmitted, setIsSubmitted] = useState(false);
  const [isCaptchaVerified, setIsCaptchaVerified] = useState(false);
  const [submitError, setSubmitError] = useState<string | null>(null);
  
  const formRef = useRef<HTMLFormElement>(null);

  const validateField = (name: keyof ContactFormData, value: string) => {
    let result: ValidationResult = { isValid: true };

    switch (name) {
      case 'name':
        result = validateName(value);
        break;
      case 'email':
        result = validateEmailAddress(value);
        break;
      case 'phone':
        result = validatePhoneNumber(value);
        break;
      case 'subject':
        result = validateSubject(value);
        break;
      case 'message':
        result = validateMessage(value);
        break;
      default:
        return true;
    }

    if (result.isValid) {
      setErrors(prev => ({ ...prev, [name]: undefined }));
      return true;
    } else {
      setErrors(prev => ({ ...prev, [name]: result.error || 'Invalid input' }));
      return false;
    }
  };

  const handleInputChange = (
    e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement | HTMLSelectElement>
  ) => {
    const { name, value } = e.target;
    let processedValue = value;

    // Special handling for phone input
    if (name === 'phone') {
      processedValue = handlePhoneInput(value);
    }

    setFormData(prev => ({ ...prev, [name]: processedValue }));

    // Real-time validation
    if (processedValue.trim()) {
      validateField(name as keyof ContactFormData, processedValue);
    } else {
      setErrors(prev => ({ ...prev, [name]: undefined }));
    }
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!isCaptchaVerified) {
      hapticFeedback.error();
      setSubmitError('Please complete the security check');
      return;
    }

    // Prevent multiple submissions
    if (isSubmitting) return;

    setIsSubmitting(true);
    setSubmitError(null);

    try {
      // Validate all fields
      const validatedData = contactFormSchema.parse(formData);
      
      // Submit the form
      if (onSubmit) {
        await onSubmit(validatedData);
      } else {
        // Default submission to API
        const response = await fetch('/api/contact', {
          method: 'POST',
          headers: { 'Content-Type': 'application/json' },
          body: JSON.stringify(validatedData),
        });

        if (!response.ok) {
          throw new Error('Failed to submit form');
        }
      }

      hapticFeedback.success();
      setIsSubmitted(true);
      
      // Reset form after successful submission
      setTimeout(() => {
        setFormData({
          name: '',
          email: '',
          phone: '',
          subject: '',
          message: '',
        });
        setIsSubmitted(false);
        setIsCaptchaVerified(false);
      }, 3000);

    } catch (error: any) {
      hapticFeedback.error();
      
      if (error.errors) {
        // Zod validation errors
        const fieldErrors: Partial<Record<keyof ContactFormData, string>> = {};
        error.errors.forEach((err: any) => {
          if (err.path?.[0]) {
            fieldErrors[err.path[0] as keyof ContactFormData] = err.message;
          }
        });
        setErrors(fieldErrors);
      } else {
        setSubmitError(error.message || 'Failed to submit form. Please try again.');
      }
    } finally {
      setIsSubmitting(false);
    }
  };

  if (isSubmitted) {
    return (
      <motion.div
        initial={{ opacity: 0, scale: 0.9 }}
        animate={{ opacity: 1, scale: 1 }}
        className={`bg-green-50 border border-green-200 rounded-2xl p-8 text-center ${className}`}
      >
        <motion.div
          initial={{ scale: 0 }}
          animate={{ scale: 1 }}
          transition={{ delay: 0.2 }}
          className="w-16 h-16 bg-green-100 rounded-full flex items-center justify-center mx-auto mb-4"
        >
          <CheckCircle className="w-8 h-8 text-green-600" />
        </motion.div>
        <h3 className="text-xl font-semibold text-green-900 mb-2">Message Sent!</h3>
        <p className="text-green-700">
          Thank you for contacting us. We'll get back to you within 24 hours.
        </p>
      </motion.div>
    );
  }

  return (
    <form
      ref={formRef}
      onSubmit={handleSubmit}
      className={`space-y-6 ${className}`}
      noValidate
    >
      {/* Name Field */}
      <div>
        <label htmlFor="name" className="block text-sm font-medium text-gray-700 mb-2">
          Full Name *
        </label>
        <input
          type="text"
          id="name"
          name="name"
          value={formData.name || ''}
          onChange={handleInputChange}
          className={`w-full px-4 py-3 border rounded-xl focus:outline-none focus:ring-2 transition-colors ${
            errors.name
              ? 'border-red-500 focus:ring-red-500 bg-red-50'
              : 'border-gray-300 focus:ring-blue-500'
          }`}
          placeholder="Enter your full name"
          required
        />
        <AnimatePresence>
          {errors.name && (
            <motion.p
              initial={{ opacity: 0, y: -10 }}
              animate={{ opacity: 1, y: 0 }}
              exit={{ opacity: 0, y: -10 }}
              className="mt-1 text-sm text-red-600"
            >
              {errors.name}
            </motion.p>
          )}
        </AnimatePresence>
      </div>

      {/* Email Field */}
      <div>
        <label htmlFor="email" className="block text-sm font-medium text-gray-700 mb-2">
          Email Address *
        </label>
        <input
          type="email"
          id="email"
          name="email"
          value={formData.email || ''}
          onChange={handleInputChange}
          className={`w-full px-4 py-3 border rounded-xl focus:outline-none focus:ring-2 transition-colors ${
            errors.email
              ? 'border-red-500 focus:ring-red-500 bg-red-50'
              : 'border-gray-300 focus:ring-blue-500'
          }`}
          placeholder="Enter your email address"
          required
        />
        <AnimatePresence>
          {errors.email && (
            <motion.p
              initial={{ opacity: 0, y: -10 }}
              animate={{ opacity: 1, y: 0 }}
              exit={{ opacity: 0, y: -10 }}
              className="mt-1 text-sm text-red-600"
            >
              {errors.email}
            </motion.p>
          )}
        </AnimatePresence>
      </div>

      {/* Phone Field */}
      <div>
        <label htmlFor="phone" className="block text-sm font-medium text-gray-700 mb-2">
          Phone Number * <span className="text-xs text-gray-500">(10 digits only)</span>
        </label>
        <input
          type="tel"
          id="phone"
          name="phone"
          value={formData.phone || ''}
          onChange={handleInputChange}
          maxLength={10}
          className={`w-full px-4 py-3 border rounded-xl focus:outline-none focus:ring-2 transition-colors ${
            errors.phone
              ? 'border-red-500 focus:ring-red-500 bg-red-50'
              : 'border-gray-300 focus:ring-blue-500'
          }`}
          placeholder="9876543210"
          required
        />
        <AnimatePresence>
          {errors.phone && (
            <motion.p
              initial={{ opacity: 0, y: -10 }}
              animate={{ opacity: 1, y: 0 }}
              exit={{ opacity: 0, y: -10 }}
              className="mt-1 text-sm text-red-600"
            >
              {errors.phone}
            </motion.p>
          )}
        </AnimatePresence>
      </div>

      {/* Subject Field */}
      <div>
        <label htmlFor="subject" className="block text-sm font-medium text-gray-700 mb-2">
          Subject *
        </label>
        <input
          type="text"
          id="subject"
          name="subject"
          value={formData.subject || ''}
          onChange={handleInputChange}
          className={`w-full px-4 py-3 border rounded-xl focus:outline-none focus:ring-2 transition-colors ${
            errors.subject
              ? 'border-red-500 focus:ring-red-500 bg-red-50'
              : 'border-gray-300 focus:ring-blue-500'
          }`}
          placeholder="What's this about?"
          required
        />
        <AnimatePresence>
          {errors.subject && (
            <motion.p
              initial={{ opacity: 0, y: -10 }}
              animate={{ opacity: 1, y: 0 }}
              exit={{ opacity: 0, y: -10 }}
              className="mt-1 text-sm text-red-600"
            >
              {errors.subject}
            </motion.p>
          )}
        </AnimatePresence>
      </div>

      {/* Message Field */}
      <div>
        <label htmlFor="message" className="block text-sm font-medium text-gray-700 mb-2">
          Message *
        </label>
        <textarea
          id="message"
          name="message"
          value={formData.message || ''}
          onChange={handleInputChange}
          rows={5}
          className={`w-full px-4 py-3 border rounded-xl focus:outline-none focus:ring-2 transition-colors resize-none ${
            errors.message
              ? 'border-red-500 focus:ring-red-500 bg-red-50'
              : 'border-gray-300 focus:ring-blue-500'
          }`}
          placeholder="Tell us more about your inquiry..."
          required
        />
        <AnimatePresence>
          {errors.message && (
            <motion.p
              initial={{ opacity: 0, y: -10 }}
              animate={{ opacity: 1, y: 0 }}
              exit={{ opacity: 0, y: -10 }}
              className="mt-1 text-sm text-red-600"
            >
              {errors.message}
            </motion.p>
          )}
        </AnimatePresence>
      </div>

      {/* CAPTCHA */}
      <SimpleCaptcha onVerify={setIsCaptchaVerified} />

      {/* Submit Error */}
      <AnimatePresence>
        {submitError && (
          <motion.div
            initial={{ opacity: 0, y: -10 }}
            animate={{ opacity: 1, y: 0 }}
            exit={{ opacity: 0, y: -10 }}
            className="bg-red-50 border border-red-200 rounded-xl p-4 flex items-center gap-3"
          >
            <AlertCircle className="w-5 h-5 text-red-600 flex-shrink-0" />
            <p className="text-red-700">{submitError}</p>
          </motion.div>
        )}
      </AnimatePresence>

      {/* Submit Button */}
      <motion.button
        type="submit"
        disabled={isSubmitting || !isCaptchaVerified}
        className={`w-full py-4 px-6 rounded-xl font-medium transition-all duration-200 flex items-center justify-center gap-3 ${
          isSubmitting || !isCaptchaVerified
            ? 'bg-gray-300 text-gray-500 cursor-not-allowed'
            : 'bg-gradient-to-r from-blue-600 to-green-600 text-white hover:from-blue-700 hover:to-green-700 hover:scale-105'
        }`}
        whileTap={{ scale: 0.98 }}
      >
        {isSubmitting ? (
          <>
            <RefreshCw className="w-5 h-5 animate-spin" />
            Sending Message...
          </>
        ) : (
          <>
            <Send className="w-5 h-5" />
            Send Message
          </>
        )}
      </motion.button>
    </form>
  );
}
