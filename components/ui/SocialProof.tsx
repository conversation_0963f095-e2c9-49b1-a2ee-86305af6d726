'use client';

import React, { useState, useEffect } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { 
  Users, 
  MapPin, 
  Star, 
  Calendar,
  CheckCircle,
  TrendingUp,
  Eye,
  Heart
} from 'lucide-react';

interface SocialProofNotification {
  id: string;
  type: 'booking' | 'review' | 'view' | 'like';
  message: string;
  location?: string;
  timestamp: Date;
  avatar?: string;
  rating?: number;
}

interface SocialProofProps {
  notifications?: SocialProofNotification[];
  autoShow?: boolean;
  interval?: number;
  position?: 'bottom-left' | 'bottom-right' | 'top-left' | 'top-right';
  maxVisible?: number;
}

const SocialProof: React.FC<SocialProofProps> = ({
  notifications = [],
  autoShow = true,
  interval = 8000,
  position = 'bottom-left',
  maxVisible = 1,
}) => {
  const [visibleNotifications, setVisibleNotifications] = useState<SocialProofNotification[]>([]);
  const [currentIndex, setCurrentIndex] = useState(0);

  // Default notifications if none provided
  const defaultNotifications: SocialProofNotification[] = [
    {
      id: '1',
      type: 'booking',
      message: '<PERSON><PERSON> from Mumbai just booked a trip to Manali',
      location: 'Mumbai',
      timestamp: new Date(Date.now() - 2 * 60 * 1000),
    },
    {
      id: '2',
      type: 'review',
      message: 'Rahul gave 5 stars to Rishikesh Adventure',
      timestamp: new Date(Date.now() - 5 * 60 * 1000),
      rating: 5,
    },
    {
      id: '3',
      type: 'view',
      message: '12 people are viewing Kerala Backwaters trip',
      timestamp: new Date(Date.now() - 1 * 60 * 1000),
    },
    {
      id: '4',
      type: 'booking',
      message: 'Anita from Delhi just booked Goa Beach Adventure',
      location: 'Delhi',
      timestamp: new Date(Date.now() - 8 * 60 * 1000),
    },
    {
      id: '5',
      type: 'like',
      message: '25 people liked Rajasthan Cultural Tour',
      timestamp: new Date(Date.now() - 3 * 60 * 1000),
    },
  ];

  const allNotifications = notifications.length > 0 ? notifications : defaultNotifications;

  useEffect(() => {
    if (!autoShow || allNotifications.length === 0) return;

    const showNotification = () => {
      const notification = allNotifications[currentIndex];
      setVisibleNotifications([notification]);
      setCurrentIndex(prev => (prev + 1) % allNotifications.length);
    };

    const intervalId = setInterval(showNotification, interval);

    // Show first notification after a delay
    const timeoutId = setTimeout(showNotification, 2000);

    return () => {
      clearInterval(intervalId);
      clearTimeout(timeoutId);
    };
  }, [autoShow, allNotifications, interval]);

  const getIcon = (type: string) => {
    switch (type) {
      case 'booking':
        return <CheckCircle className="h-4 w-4 text-green-500" />;
      case 'review':
        return <Star className="h-4 w-4 text-yellow-500" />;
      case 'view':
        return <Eye className="h-4 w-4 text-blue-500" />;
      case 'like':
        return <Heart className="h-4 w-4 text-red-500" />;
      default:
        return <TrendingUp className="h-4 w-4 text-coral-500" />;
    }
  };

  const getTimeAgo = (timestamp: Date) => {
    const now = new Date();
    const diffInMinutes = Math.floor((now.getTime() - timestamp.getTime()) / (1000 * 60));
    
    if (diffInMinutes < 1) return 'just now';
    if (diffInMinutes < 60) return `${diffInMinutes}m ago`;
    
    const diffInHours = Math.floor(diffInMinutes / 60);
    if (diffInHours < 24) return `${diffInHours}h ago`;
    
    const diffInDays = Math.floor(diffInHours / 24);
    return `${diffInDays}d ago`;
  };

  const positionClasses = {
    'bottom-left': 'bottom-4 left-4',
    'bottom-right': 'bottom-4 right-4',
    'top-left': 'top-4 left-4',
    'top-right': 'top-4 right-4',
  };

  return (
    <div className={`fixed ${positionClasses[position]} z-50 space-y-2`}>
      <AnimatePresence>
        {visibleNotifications.map((notification, index) => (
          <motion.div
            key={notification.id}
            initial={{ opacity: 0, x: position.includes('right') ? 100 : -100, scale: 0.8 }}
            animate={{ opacity: 1, x: 0, scale: 1 }}
            exit={{ opacity: 0, x: position.includes('right') ? 100 : -100, scale: 0.8 }}
            transition={{ 
              type: 'spring', 
              stiffness: 300, 
              damping: 30,
              delay: index * 0.1 
            }}
            className="bg-white rounded-2xl shadow-2xl border border-gray-100 p-4 max-w-sm backdrop-blur-sm"
          >
            <div className="flex items-start space-x-3">
              <div className="flex-shrink-0 mt-0.5">
                {getIcon(notification.type)}
              </div>
              
              <div className="flex-1 min-w-0">
                <p className="text-sm font-medium text-gray-900 leading-relaxed">
                  {notification.message}
                </p>
                
                <div className="flex items-center justify-between mt-2">
                  <div className="flex items-center space-x-2 text-xs text-gray-500">
                    {notification.location && (
                      <>
                        <MapPin className="h-3 w-3" />
                        <span>{notification.location}</span>
                      </>
                    )}
                    {notification.rating && (
                      <div className="flex items-center space-x-1">
                        {[...Array(notification.rating)].map((_, i) => (
                          <Star key={i} className="h-3 w-3 fill-yellow-400 text-yellow-400" />
                        ))}
                      </div>
                    )}
                  </div>
                  
                  <span className="text-xs text-gray-400">
                    {getTimeAgo(notification.timestamp)}
                  </span>
                </div>
              </div>
            </div>
          </motion.div>
        ))}
      </AnimatePresence>
    </div>
  );
};

export default SocialProof;

// Live activity indicator
interface LiveActivityProps {
  count: number;
  activity: string;
  className?: string;
}

export const LiveActivity: React.FC<LiveActivityProps> = ({
  count,
  activity,
  className = '',
}) => {
  return (
    <motion.div
      initial={{ opacity: 0, scale: 0.8 }}
      animate={{ opacity: 1, scale: 1 }}
      className={`inline-flex items-center space-x-2 bg-red-500 text-white px-3 py-1 rounded-full text-sm font-medium ${className}`}
    >
      <div className="w-2 h-2 bg-white rounded-full animate-pulse" />
      <span>{count} {activity}</span>
    </motion.div>
  );
};

// Trust badges
interface TrustBadgeProps {
  badges?: Array<{
    icon: React.ReactNode;
    text: string;
    subtext?: string;
  }>;
  className?: string;
}

export const TrustBadges: React.FC<TrustBadgeProps> = ({
  badges = [
    {
      icon: <Users className="h-5 w-5" />,
      text: '1000+',
      subtext: 'Happy Students',
    },
    {
      icon: <Star className="h-5 w-5" />,
      text: '4.9/5',
      subtext: 'Average Rating',
    },
    {
      icon: <CheckCircle className="h-5 w-5" />,
      text: '15+',
      subtext: 'Years Experience',
    },
  ],
  className = '',
}) => {
  return (
    <div className={`flex flex-wrap gap-4 ${className}`}>
      {badges.map((badge, index) => (
        <motion.div
          key={index}
          initial={{ opacity: 0, y: 20 }}
          whileInView={{ opacity: 1, y: 0 }}
          viewport={{ once: true }}
          transition={{ delay: index * 0.1 }}
          className="flex items-center space-x-2 bg-white/10 backdrop-blur-sm rounded-xl px-4 py-2 text-white"
        >
          <div className="text-secondary-300">
            {badge.icon}
          </div>
          <div>
            <div className="font-bold text-lg">{badge.text}</div>
            {badge.subtext && (
              <div className="text-xs opacity-80">{badge.subtext}</div>
            )}
          </div>
        </motion.div>
      ))}
    </div>
  );
};

// Recent bookings ticker
interface RecentBooking {
  name: string;
  destination: string;
  timeAgo: string;
}

interface RecentBookingsTickerProps {
  bookings?: RecentBooking[];
  speed?: number;
  className?: string;
}

export const RecentBookingsTicker: React.FC<RecentBookingsTickerProps> = ({
  bookings = [
    { name: 'Priya M.', destination: 'Manali Adventure', timeAgo: '2 min ago' },
    { name: 'Rahul S.', destination: 'Rishikesh Rafting', timeAgo: '5 min ago' },
    { name: 'Anita K.', destination: 'Goa Beach Trip', timeAgo: '8 min ago' },
    { name: 'Vikram P.', destination: 'Kerala Backwaters', timeAgo: '12 min ago' },
  ],
  speed = 30,
  className = '',
}) => {
  return (
    <div className={`bg-gradient-to-r from-coral-500 to-orange-500 text-white py-2 overflow-hidden ${className}`}>
      <motion.div
        animate={{ x: [0, -100] }}
        transition={{ duration: speed, repeat: Infinity, ease: 'linear' }}
        className="flex space-x-8 whitespace-nowrap"
      >
        {[...bookings, ...bookings].map((booking, index) => (
          <div key={index} className="flex items-center space-x-2 text-sm">
            <CheckCircle className="h-4 w-4" />
            <span className="font-medium">{booking.name}</span>
            <span>booked</span>
            <span className="font-medium">{booking.destination}</span>
            <span className="opacity-75">• {booking.timeAgo}</span>
          </div>
        ))}
      </motion.div>
    </div>
  );
};
