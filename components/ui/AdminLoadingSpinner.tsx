'use client';

import React from 'react';
import { cn } from '@/lib/utils';

export interface AdminLoadingSpinnerProps {
  size?: 'small' | 'medium' | 'large';
  variant?: 'spinner' | 'dots' | 'pulse';
  color?: 'primary' | 'secondary' | 'white' | 'gray';
  showIcon?: boolean;
  message?: string;
  submessage?: string;
  className?: string;
}

// Predefined configurations for common loading scenarios
export const AdminLoadingPresets = {
  // For table loading states
  tableLoading: {
    size: 'medium' as const,
    variant: 'spinner' as const,
    color: 'primary' as const,
    showIcon: true,
    message: 'Loading data...',
    submessage: 'Please wait while we fetch the information'
  },
  
  // For form submissions
  formSubmitting: {
    size: 'small' as const,
    variant: 'spinner' as const,
    color: 'white' as const,
    showIcon: false
  },
  
  // For page loading
  pageLoading: {
    size: 'large' as const,
    variant: 'pulse' as const,
    color: 'primary' as const,
    showIcon: true,
    message: 'Loading page...',
    submessage: 'Setting up your admin dashboard'
  },
  
  // For card/component loading
  cardLoading: {
    size: 'medium' as const,
    variant: 'dots' as const,
    color: 'gray' as const,
    showIcon: false
  }
};

export default function AdminLoadingSpinner({
  size = 'medium',
  variant = 'spinner',
  color = 'primary',
  showIcon = true,
  message,
  submessage,
  className
}: AdminLoadingSpinnerProps) {
  
  // Size classes
  const sizeClasses = {
    small: 'w-4 h-4',
    medium: 'w-8 h-8',
    large: 'w-12 h-12'
  };

  // Color classes
  const colorClasses = {
    primary: 'text-indigo-600',
    secondary: 'text-gray-600',
    white: 'text-white',
    gray: 'text-gray-400'
  };

  // Spinner variant
  const SpinnerIcon = () => (
    <svg
      className={cn(
        'animate-spin',
        sizeClasses[size],
        colorClasses[color]
      )}
      xmlns="http://www.w3.org/2000/svg"
      fill="none"
      viewBox="0 0 24 24"
    >
      <circle
        className="opacity-25"
        cx="12"
        cy="12"
        r="10"
        stroke="currentColor"
        strokeWidth="4"
      />
      <path
        className="opacity-75"
        fill="currentColor"
        d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"
      />
    </svg>
  );

  // Dots variant
  const DotsIcon = () => (
    <div className="flex space-x-1">
      {[0, 1, 2].map((i) => (
        <div
          key={i}
          className={cn(
            'rounded-full animate-pulse',
            size === 'small' ? 'w-1 h-1' : size === 'medium' ? 'w-2 h-2' : 'w-3 h-3',
            colorClasses[color].replace('text-', 'bg-')
          )}
          style={{
            animationDelay: `${i * 0.2}s`,
            animationDuration: '1s'
          }}
        />
      ))}
    </div>
  );

  // Pulse variant
  const PulseIcon = () => (
    <div
      className={cn(
        'rounded-full animate-pulse',
        sizeClasses[size],
        colorClasses[color].replace('text-', 'bg-'),
        'opacity-75'
      )}
    />
  );

  const renderIcon = () => {
    if (!showIcon) return null;
    
    switch (variant) {
      case 'spinner':
        return <SpinnerIcon />;
      case 'dots':
        return <DotsIcon />;
      case 'pulse':
        return <PulseIcon />;
      default:
        return <SpinnerIcon />;
    }
  };

  return (
    <div className={cn('flex flex-col items-center justify-center space-y-3', className)}>
      {renderIcon()}
      
      {message && (
        <div className="text-center">
          <p className={cn(
            'font-medium',
            size === 'small' ? 'text-sm' : size === 'medium' ? 'text-base' : 'text-lg',
            colorClasses[color]
          )}>
            {message}
          </p>
          
          {submessage && (
            <p className={cn(
              'mt-1 text-xs opacity-75',
              colorClasses[color]
            )}>
              {submessage}
            </p>
          )}
        </div>
      )}
    </div>
  );
}
