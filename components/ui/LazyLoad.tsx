'use client';

import { useEffect, useRef, useState } from 'react';

interface LazyLoadProps {
  children: React.ReactNode;
  threshold?: number;
  rootMargin?: string;
  placeholder?: React.ReactNode;
  className?: string;
  style?: React.CSSProperties;
}

/**
 * LazyLoad component that only renders its children when it comes into view
 * Uses IntersectionObserver to detect when the component is visible
 */
export function LazyLoad({
  children,
  threshold = 0.1,
  rootMargin = '0px',
  placeholder,
  className,
  style
}: LazyLoadProps) {
  const [isVisible, setIsVisible] = useState(false);
  const [hasBeenVisible, setHasBeenVisible] = useState(false);
  const ref = useRef<HTMLDivElement>(null);

  useEffect(() => {
    const currentRef = ref.current;
    if (!currentRef) return;

    // Check if IntersectionObserver is available
    if (!('IntersectionObserver' in window)) {
      // If not available, just show the content
      setIsVisible(true);
      setHasBeenVisible(true);
      return;
    }

    const observer = new IntersectionObserver(
      ([entry]) => {
        if (entry.isIntersecting) {
          setIsVisible(true);
          setHasBeenVisible(true);
          // Once visible, no need to observe anymore
          observer.unobserve(currentRef);
        }
      },
      {
        threshold,
        rootMargin,
      }
    );

    observer.observe(currentRef);

    return () => {
      if (currentRef) {
        observer.unobserve(currentRef);
      }
    };
  }, [threshold, rootMargin]);

  return (
    <div ref={ref} className={className} style={style}>
      {isVisible ? (
        children
      ) : (
        placeholder || (
          <div 
            className="animate-pulse bg-gray-200 rounded" 
            style={{ 
              height: hasBeenVisible ? 'auto' : '200px',
              width: '100%'
            }}
          />
        )
      )}
    </div>
  );
} 