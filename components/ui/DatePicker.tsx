'use client'

import { useState, forwardRef } from 'react'
import { Calendar } from 'lucide-react'
import { motion } from 'framer-motion'

interface DatePickerProps {
  label?: string
  value?: string
  onChange?: (value: string) => void
  placeholder?: string
  required?: boolean
  disabled?: boolean
  error?: string
  className?: string
  minDate?: string
  maxDate?: string
  name?: string
  id?: string
}

const DatePicker = forwardRef<HTMLInputElement, DatePickerProps>(({
  label,
  value = '',
  onChange,
  placeholder,
  required = false,
  disabled = false,
  error,
  className = '',
  minDate,
  maxDate,
  name,
  id
}, ref) => {
  const [isFocused, setIsFocused] = useState(false)

  const handleChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const dateValue = e.target.value
    onChange?.(dateValue)
  }

  const handleFocus = () => {
    setIsFocused(true)
  }

  const handleBlur = () => {
    setIsFocused(false)
  }

  // Format date for display (convert YYYY-MM-DD to readable format)
  const formatDateForDisplay = (dateString: string) => {
    if (!dateString) return ''
    try {
      const date = new Date(dateString)
      return date.toLocaleDateString('en-US', {
        year: 'numeric',
        month: 'long',
        day: 'numeric'
      })
    } catch {
      return dateString
    }
  }

  const inputClasses = `
    w-full p-3 border rounded-lg transition-all duration-200
    focus:ring-2 focus:ring-blue-500 focus:border-transparent
    ${error 
      ? 'border-red-300 focus:ring-red-500' 
      : isFocused 
        ? 'border-blue-500' 
        : 'border-gray-300 hover:border-gray-400'
    }
    ${disabled ? 'bg-gray-50 cursor-not-allowed' : 'bg-white'}
    ${className}
  `.trim()

  return (
    <div className="relative">
      {label && (
        <label 
          htmlFor={id || name}
          className="block text-sm font-medium text-gray-700 mb-2"
        >
          <Calendar className="w-4 h-4 inline mr-2" />
          {label}
          {required && <span className="text-red-500 ml-1">*</span>}
        </label>
      )}
      
      <div className="relative">
        <input
          ref={ref}
          type="date"
          id={id || name}
          name={name}
          value={value}
          onChange={handleChange}
          onFocus={handleFocus}
          onBlur={handleBlur}
          placeholder={placeholder}
          required={required}
          disabled={disabled}
          min={minDate}
          max={maxDate}
          className={inputClasses}
        />
        
        {/* Calendar icon overlay for better visual consistency */}
        <div className="absolute right-3 top-1/2 transform -translate-y-1/2 pointer-events-none">
          <Calendar className="w-5 h-5 text-gray-400" />
        </div>
      </div>

      {/* Display formatted date below input for better UX */}
      {value && !error && (
        <motion.div
          initial={{ opacity: 0, y: -5 }}
          animate={{ opacity: 1, y: 0 }}
          className="mt-1 text-sm text-gray-600"
        >
          Selected: {formatDateForDisplay(value)}
        </motion.div>
      )}

      {/* Error message */}
      {error && (
        <motion.div
          initial={{ opacity: 0, y: -5 }}
          animate={{ opacity: 1, y: 0 }}
          className="mt-1 text-sm text-red-600"
        >
          {error}
        </motion.div>
      )}
    </div>
  )
})

DatePicker.displayName = 'DatePicker'

export default DatePicker
