'use client';

import React, { useState } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { AlertTriangle, X, Camera, ExternalLink, Shield } from 'lucide-react';
import Button from './Button';
import { PhotoAlbum } from '@/types/photo-album';

interface TripPhotosDeleteModalProps {
  isOpen: boolean;
  onClose: () => void;
  onConfirm: () => void;
  album: PhotoAlbum;
  loading?: boolean;
}

export default function TripPhotosDeleteModal({
  isOpen,
  onClose,
  onConfirm,
  album,
  loading = false
}: TripPhotosDeleteModalProps) {
  const [confirmationText, setConfirmationText] = useState('');
  const [step, setStep] = useState<'warning' | 'confirmation'>('warning');

  const handleClose = () => {
    setStep('warning');
    setConfirmationText('');
    onClose();
  };

  const handleProceedToConfirmation = () => {
    setStep('confirmation');
  };

  const handleConfirm = () => {
    if (confirmationText === 'DELETE') {
      onConfirm();
    }
  };

  const isConfirmationValid = confirmationText === 'DELETE';

  return (
    <AnimatePresence>
      {isOpen && (
        <>
          {/* Backdrop */}
          <motion.div
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            exit={{ opacity: 0 }}
            transition={{ duration: 0.3 }}
            className="fixed inset-0 bg-black/60 backdrop-blur-sm z-50"
            onClick={handleClose}
          />
          
          {/* Modal */}
          <motion.div
            initial={{ opacity: 0, scale: 0.8, y: 50 }}
            animate={{ opacity: 1, scale: 1, y: 0 }}
            exit={{ opacity: 0, scale: 0.8, y: 50 }}
            transition={{ 
              duration: 0.4, 
              ease: [0.4, 0, 0.2, 1],
            }}
            className="fixed inset-0 z-50 flex items-center justify-center p-4"
            onClick={(e) => e.stopPropagation()}
          >
            <div className="bg-white rounded-2xl max-w-lg w-full shadow-2xl overflow-hidden">
              {step === 'warning' && (
                <>
                  {/* Warning Header */}
                  <div className="bg-gradient-to-r from-red-500 to-orange-500 p-6 text-white">
                    <div className="flex items-center gap-3">
                      <div className="w-12 h-12 bg-white/20 rounded-full flex items-center justify-center">
                        <AlertTriangle className="w-6 h-6" />
                      </div>
                      <div>
                        <h3 className="text-xl font-bold">⚠️ PERMANENT DELETION WARNING</h3>
                        <p className="text-red-100 text-sm">This action cannot be undone</p>
                      </div>
                    </div>
                  </div>

                  {/* Warning Content */}
                  <div className="p-6">
                    <div className="mb-6">
                      <div className="flex items-center gap-2 mb-3">
                        <Camera className="w-5 h-5 text-gray-600" />
                        <span className="font-semibold text-gray-900">Album: "{album.trip_name}"</span>
                      </div>
                      
                      {album.google_photos_album_id && (
                        <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-4 mb-4">
                          <div className="flex items-start gap-3">
                            <Shield className="w-5 h-5 text-yellow-600 mt-0.5 flex-shrink-0" />
                            <div>
                              <h4 className="font-semibold text-yellow-800 mb-1">Google Photos Album Limitation</h4>
                              <p className="text-sm text-yellow-700">
                                <strong>Important:</strong> Due to Google Photos API limitations, the album cannot be automatically deleted from your Google Photos account. You will need to manually delete it from{' '}
                                <a 
                                  href="https://photos.google.com" 
                                  target="_blank" 
                                  rel="noopener noreferrer"
                                  className="underline hover:no-underline inline-flex items-center gap-1"
                                >
                                  photos.google.com <ExternalLink className="w-3 h-3" />
                                </a>
                              </p>
                            </div>
                          </div>
                        </div>
                      )}
                    </div>

                    <div className="bg-red-50 border border-red-200 rounded-lg p-4 mb-6">
                      <h4 className="font-semibold text-red-800 mb-3">🚨 BACKUP YOUR IMAGES FIRST! 🚨</h4>
                      <div className="text-sm text-red-700 space-y-2">
                        <p><strong>This action will:</strong></p>
                        <ul className="list-disc list-inside space-y-1 ml-2">
                          <li>Delete the database record permanently</li>
                          <li>Remove all associated data from our system</li>
                          <li>Cannot be undone or recovered</li>
                          {album.google_photos_album_id && (
                            <li>Require manual deletion from Google Photos</li>
                          )}
                        </ul>
                      </div>
                    </div>

                    <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
                      <h4 className="font-semibold text-blue-800 mb-2">✅ Before proceeding, ensure:</h4>
                      <ul className="text-sm text-blue-700 space-y-1">
                        <li>✅ You have saved all images locally</li>
                        <li>✅ You have backups of important photos</li>
                        <li>✅ You are absolutely sure you want to delete this album</li>
                      </ul>
                    </div>
                  </div>

                  {/* Warning Actions */}
                  <div className="px-6 pb-6 flex gap-3 justify-end">
                    <Button
                      variant="outline"
                      onClick={handleClose}
                      disabled={loading}
                    >
                      Cancel
                    </Button>
                    <Button
                      onClick={handleProceedToConfirmation}
                      disabled={loading}
                      className="bg-red-600 hover:bg-red-700 focus:ring-red-500"
                    >
                      I Understand, Continue
                    </Button>
                  </div>
                </>
              )}

              {step === 'confirmation' && (
                <>
                  {/* Confirmation Header */}
                  <div className="bg-gradient-to-r from-red-600 to-red-700 p-6 text-white">
                    <div className="flex items-center justify-between">
                      <div className="flex items-center gap-3">
                        <div className="w-10 h-10 bg-white/20 rounded-full flex items-center justify-center">
                          <AlertTriangle className="w-5 h-5" />
                        </div>
                        <div>
                          <h3 className="text-lg font-bold">Final Confirmation</h3>
                          <p className="text-red-100 text-sm">Type "DELETE" to confirm</p>
                        </div>
                      </div>
                      <button
                        onClick={handleClose}
                        className="text-white/80 hover:text-white transition-colors"
                        disabled={loading}
                      >
                        <X className="w-5 h-5" />
                      </button>
                    </div>
                  </div>

                  {/* Confirmation Content */}
                  <div className="p-6">
                    <div className="mb-6">
                      <p className="text-gray-700 mb-4">
                        You are about to permanently delete <strong>"{album.trip_name}"</strong>.
                      </p>
                      
                      <div className="mb-4">
                        <label htmlFor="confirmation-input" className="block text-sm font-medium text-gray-700 mb-2">
                          Type <span className="font-mono bg-gray-100 px-1 rounded">DELETE</span> to confirm:
                        </label>
                        <input
                          id="confirmation-input"
                          type="text"
                          value={confirmationText}
                          onChange={(e) => setConfirmationText(e.target.value)}
                          className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-red-500 focus:border-red-500"
                          placeholder="Type DELETE here"
                          disabled={loading}
                          autoFocus
                        />
                      </div>

                      {confirmationText && confirmationText !== 'DELETE' && (
                        <p className="text-sm text-red-600">
                          You must type "DELETE" exactly to confirm.
                        </p>
                      )}
                    </div>
                  </div>

                  {/* Confirmation Actions */}
                  <div className="px-6 pb-6 flex gap-3 justify-end">
                    <Button
                      variant="outline"
                      onClick={handleClose}
                      disabled={loading}
                    >
                      Cancel
                    </Button>
                    <Button
                      onClick={handleConfirm}
                      disabled={!isConfirmationValid || loading}
                      loading={loading}
                      className="bg-red-600 hover:bg-red-700 focus:ring-red-500 disabled:bg-gray-300"
                    >
                      Delete Permanently
                    </Button>
                  </div>
                </>
              )}
            </div>
          </motion.div>
        </>
      )}
    </AnimatePresence>
  );
}
