'use client';

import React from 'react';
import { motion } from 'framer-motion';

// Base skeleton component
interface SkeletonProps {
  className?: string;
  variant?: 'rectangular' | 'circular' | 'text';
  animation?: 'pulse' | 'wave' | 'shimmer';
  width?: string;
  height?: string;
}

const Skeleton: React.FC<SkeletonProps> = ({
  className = '',
  variant = 'rectangular',
  animation = 'shimmer',
  width,
  height,
}) => {
  const baseClasses = 'bg-gray-200 relative overflow-hidden';
  
  const variantClasses = {
    rectangular: 'rounded-lg',
    circular: 'rounded-full',
    text: 'rounded',
  };

  const animationClasses = {
    pulse: 'animate-pulse',
    wave: 'animate-pulse',
    shimmer: '',
  };

  const style = {
    width: width || (variant === 'circular' ? height : undefined),
    height: height || (variant === 'text' ? '1em' : undefined),
  };

  return (
    <div
      className={`${baseClasses} ${variantClasses[variant]} ${animationClasses[animation]} ${className}`}
      style={style}
    >
      {animation === 'shimmer' && (
        <motion.div
          animate={{ x: ['-100%', '100%'] }}
          transition={{ duration: 1.5, repeat: Infinity, ease: 'linear' }}
          className="absolute inset-0 bg-gradient-to-r from-transparent via-white/40 to-transparent"
        />
      )}
    </div>
  );
};

// Trip card skeleton
export const TripCardSkeleton: React.FC = () => (
  <div className="card-modern overflow-hidden p-0">
    <Skeleton height="288px" className="w-full" />
    <div className="p-8">
      <Skeleton height="32px" className="w-3/4 mb-4" />
      <Skeleton height="20px" className="w-1/2 mb-4" />
      <div className="space-y-2 mb-6">
        <Skeleton height="16px" className="w-full" />
        <Skeleton height="16px" className="w-5/6" />
        <Skeleton height="16px" className="w-4/6" />
      </div>
      <Skeleton height="20px" className="w-1/3 mb-8" />
      <Skeleton height="56px" className="w-full rounded-2xl" />
    </div>
  </div>
);

// Blog card skeleton
export const BlogCardSkeleton: React.FC = () => (
  <div className="card-modern overflow-hidden">
    <Skeleton height="200px" className="w-full" />
    <div className="p-6">
      <Skeleton height="24px" className="w-3/4 mb-3" />
      <Skeleton height="16px" className="w-1/2 mb-4" />
      <div className="space-y-2 mb-4">
        <Skeleton height="14px" className="w-full" />
        <Skeleton height="14px" className="w-5/6" />
      </div>
      <div className="flex items-center justify-between">
        <Skeleton height="16px" className="w-1/4" />
        <Skeleton height="32px" className="w-20 rounded-lg" />
      </div>
    </div>
  </div>
);

// Statistics skeleton
export const StatsSkeleton: React.FC = () => (
  <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
    {[...Array(3)].map((_, index) => (
      <div key={index} className="text-center">
        <Skeleton variant="circular" height="64px" className="mx-auto mb-4" />
        <Skeleton height="40px" className="w-20 mx-auto mb-2" />
        <Skeleton height="20px" className="w-32 mx-auto mb-1" />
        <Skeleton height="16px" className="w-40 mx-auto" />
      </div>
    ))}
  </div>
);

// Hero section skeleton
export const HeroSkeleton: React.FC = () => (
  <div className="relative h-screen min-h-[600px] bg-gray-200 animate-pulse">
    <div className="absolute inset-0 flex items-center justify-center">
      <div className="text-center max-w-4xl px-4">
        <Skeleton height="80px" className="w-96 mx-auto mb-6" />
        <Skeleton height="4px" className="w-60 mx-auto mb-8" />
        <Skeleton height="60px" className="w-full max-w-2xl mx-auto mb-12" />
        <div className="flex flex-col sm:flex-row gap-6 justify-center">
          <Skeleton height="60px" className="w-48 rounded-2xl" />
          <Skeleton height="60px" className="w-48 rounded-2xl" />
        </div>
      </div>
    </div>
  </div>
);

// Gallery skeleton
export const GallerySkeleton: React.FC<{ count?: number }> = ({ count = 12 }) => (
  <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-4">
    {[...Array(count)].map((_, index) => (
      <Skeleton
        key={index}
        height="250px"
        className="w-full rounded-xl"
        animation="shimmer"
      />
    ))}
  </div>
);

// Form skeleton
export const FormSkeleton: React.FC = () => (
  <div className="space-y-6">
    <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
      <div>
        <Skeleton height="16px" className="w-20 mb-2" />
        <Skeleton height="48px" className="w-full rounded-lg" />
      </div>
      <div>
        <Skeleton height="16px" className="w-24 mb-2" />
        <Skeleton height="48px" className="w-full rounded-lg" />
      </div>
    </div>
    <div>
      <Skeleton height="16px" className="w-16 mb-2" />
      <Skeleton height="48px" className="w-full rounded-lg" />
    </div>
    <div>
      <Skeleton height="16px" className="w-20 mb-2" />
      <Skeleton height="120px" className="w-full rounded-lg" />
    </div>
    <Skeleton height="48px" className="w-32 rounded-lg" />
  </div>
);

// Navigation skeleton
export const NavSkeleton: React.FC = () => (
  <div className="flex items-center justify-between p-4">
    <Skeleton variant="circular" height="40px" />
    <div className="hidden lg:flex space-x-8">
      {[...Array(6)].map((_, index) => (
        <Skeleton key={index} height="20px" className="w-16" />
      ))}
    </div>
    <Skeleton height="40px" className="w-24 rounded-lg lg:hidden" />
  </div>
);

// Table skeleton
export const TableSkeleton: React.FC<{ rows?: number; cols?: number }> = ({ 
  rows = 5, 
  cols = 4 
}) => (
  <div className="w-full">
    <div className="grid grid-cols-4 gap-4 p-4 border-b border-gray-200">
      {[...Array(cols)].map((_, index) => (
        <Skeleton key={index} height="20px" className="w-full" />
      ))}
    </div>
    {[...Array(rows)].map((_, rowIndex) => (
      <div key={rowIndex} className="grid grid-cols-4 gap-4 p-4 border-b border-gray-100">
        {[...Array(cols)].map((_, colIndex) => (
          <Skeleton key={colIndex} height="16px" className="w-full" />
        ))}
      </div>
    ))}
  </div>
);

export default Skeleton;
