'use client';

import { useState, useEffect } from 'react';
import Image from 'next/image';
import { AnimatePresence, motion } from 'framer-motion';

interface SplashScreenProps {
  onComplete?: () => void;
  duration?: number;
}

export default function SplashScreen({
  onComplete,
  duration = 3000
}: SplashScreenProps) {
  const [show, setShow] = useState(true);
  const [dimension, setDimension] = useState({ width: 0, height: 0 });
  const [isExiting, setIsExiting] = useState(false);

  useEffect(() => {
    setDimension({ width: window.innerWidth, height: window.innerHeight });
  }, []);

  useEffect(() => {
    const timer = setTimeout(() => {
      setIsExiting(true);
      // Call onComplete after exit animation
      setTimeout(() => {
        setShow(false);
        if (onComplete) {
          onComplete();
        }
      }, 800);
    }, duration);

    return () => clearTimeout(timer);
  }, [duration, onComplete]);

  // SVG path animations for elegant exit
  const initialPath = `M0 0 L${dimension.width} 0 L${dimension.width} ${dimension.height} Q${dimension.width / 2} ${dimension.height + 300} 0 ${dimension.height} L0 0`;
  const targetPath = `M0 0 L${dimension.width} 0 L${dimension.width} ${dimension.height} Q${dimension.width / 2} ${dimension.height} 0 ${dimension.height} L0 0`;

  const slideUp = {
    initial: {
      top: 0,
    },
    exit: {
      top: "-100vh",
      transition: { duration: 0.8, ease: [0.76, 0, 0.24, 1], delay: 0.2 },
    },
  };

  const curve = {
    initial: {
      d: initialPath,
      transition: { duration: 0.7, ease: [0.76, 0, 0.24, 1] },
    },
    exit: {
      d: targetPath,
      transition: { duration: 0.7, ease: [0.76, 0, 0.24, 1], delay: 0.3 },
    },
  };

  const fadeIn = {
    initial: {
      opacity: 0,
    },
    enter: {
      opacity: 1,
      transition: { duration: 0.8, delay: 0.3 },
    },
  };

  return (
    <AnimatePresence>
      {show && (
        <motion.div
          variants={slideUp}
          initial="initial"
          animate={isExiting ? "exit" : "initial"}
          className="fixed inset-0 w-screen h-screen flex items-center justify-center bg-white z-[9999]"
        >
          {dimension.width > 0 && (
            <>
              {/* Main content */}
              <motion.div
                variants={fadeIn}
                initial="initial"
                animate="enter"
                className="flex flex-col items-center justify-center absolute z-10"
              >
                {/* Logo */}
                <motion.div
                  className="relative w-80 h-60"
                  initial={{ scale: 0.8, opacity: 0 }}
                  animate={{ scale: 1, opacity: 1 }}
                  transition={{
                    duration: 0.6,
                    delay: 0.5,
                    ease: [0.25, 0.46, 0.45, 0.94]
                  }}
                >
                  <Image
                    src="/images/static/logos/logo-hd.png"
                    alt="Positive7 Logo"
                    fill
                    className="object-contain"
                    priority
                  />
                </motion.div>

                {/* Tagline */}
                <motion.p
                  className="font-playwrite font-bold text-gray-600 text-4xl font-light tracking-wide"
                  initial={{ opacity: 0, y: 10 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ duration: 0.6, delay: 0.8 }}
                >
                  Bring Learning to Life
                </motion.p>

                {/* Minimal loading indicator */}
                <motion.div
                  className="mt-8 flex space-x-1"
                  initial={{ opacity: 0 }}
                  animate={{ opacity: 1 }}
                  transition={{ delay: 1.2 }}
                >
                  {[...Array(3)].map((_, i) => (
                    <motion.div
                      key={i}
                      className="w-2 h-2 bg-gray-400 rounded-full"
                      animate={{
                        scale: [1, 1.2, 1],
                        opacity: [0.4, 1, 0.4],
                      }}
                      transition={{
                        duration: 1.2,
                        repeat: Infinity,
                        delay: i * 0.2,
                        ease: "easeInOut"
                      }}
                    />
                  ))}
                </motion.div>
              </motion.div>

              {/* Elegant SVG curve animation */}
              <svg className="absolute top-0 w-full h-[calc(100%+300px)]">
                <motion.path
                  variants={curve}
                  initial="initial"
                  animate={isExiting ? "exit" : "initial"}
                  fill="#f8fafc"
                />
              </svg>
            </>
          )}
        </motion.div>
      )}
    </AnimatePresence>
  );
}