'use client';

import React from 'react';

interface LoadingSpinnerProps {
  size?: 'small' | 'medium' | 'large';
  color?: string;
  className?: string;
  variant?: 'spinner' | 'dots' | 'pulse' | 'bars';
  showText?: boolean;
  text?: string;
}

export default function LoadingSpinner({
  size = 'medium',
  color = 'text-indigo-600',
  className = '',
  variant = 'spinner',
  showText = false,
  text = 'Loading...'
}: LoadingSpinnerProps) {
  const sizeClasses = {
    small: 'w-4 h-4',
    medium: 'w-8 h-8',
    large: 'w-12 h-12'
  };

  const dotSizeClasses = {
    small: 'w-1 h-1',
    medium: 'w-2 h-2',
    large: 'w-3 h-3'
  };

  const barSizeClasses = {
    small: 'w-1 h-3',
    medium: 'w-1 h-4',
    large: 'w-1.5 h-6'
  };

  const textSizeClasses = {
    small: 'text-xs',
    medium: 'text-sm',
    large: 'text-base'
  };

  // Extract color class for consistent theming with improved robustness and regex patterns
  const extractColorFromClass = (colorString: string): string => {
    const supportedColors = [
      'indigo', 'blue', 'green', 'red', 'yellow', 'purple', 'pink', 'gray',
      'slate', 'zinc', 'neutral', 'stone', 'orange', 'amber', 'lime', 'emerald',
      'teal', 'cyan', 'sky', 'violet', 'fuchsia', 'rose'
    ];

    // Use more precise regex patterns for better accuracy and safety
    for (const colorName of supportedColors) {
      // Match color name as whole word or with Tailwind prefixes/suffixes
      const colorPattern = new RegExp(`\\b(?:text-|bg-|border-)?${colorName}(?:-\\d+)?\\b`);
      if (colorPattern.test(colorString)) {
        return colorName;
      }
    }

    return 'indigo'; // fallback to indigo
  };

  const colorClass = extractColorFromClass(color);

  const renderSpinner = () => {
    switch (variant) {
      case 'dots':
        return (
          <div className="flex space-x-1">
            {[0, 1, 2].map((i) => (
              <div
                key={i}
                className={`bg-${colorClass}-600 rounded-full ${dotSizeClasses[size]} animate-pulse`}
                style={{
                  animationDelay: `${i * 0.2}s`,
                  animationDuration: '1.4s'
                }}
              />
            ))}
          </div>
        );

      case 'pulse':
        return (
          <div className={`bg-${colorClass}-600 rounded-full ${sizeClasses[size]} animate-pulse`} />
        );

      case 'bars':
        return (
          <div className="flex space-x-1 items-end">
            {[0, 1, 2, 3].map((i) => (
              <div
                key={i}
                className={`bg-${colorClass}-600 rounded-sm ${barSizeClasses[size]}`}
                style={{
                  animation: `bounce 1.4s ease-in-out ${i * 0.16}s infinite both`
                }}
              />
            ))}
          </div>
        );

      default: // spinner
        return (
          <div className={`animate-spin rounded-full border-2 border-gray-200 border-t-${colorClass}-600 ${sizeClasses[size]}`} />
        );
    }
  };

  return (
    <div className={`flex flex-col items-center justify-center ${className}`}>
      {renderSpinner()}
      {showText && (
        <p className={`mt-2 text-gray-600 ${textSizeClasses[size]} font-medium`}>
          {text}
        </p>
      )}
    </div>
  );
}
