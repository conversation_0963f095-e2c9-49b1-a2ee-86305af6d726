'use client';

import React from 'react';
import { cn } from '@/lib/utils';
import AdminLoadingSpinner, { AdminLoadingPresets } from './AdminLoadingSpinner';

export interface AdminTableLoadingProps {
  columns?: number;
  rows?: number;
  showHeader?: boolean;
  message?: string;
  submessage?: string;
  variant?: 'skeleton' | 'spinner' | 'minimal';
  className?: string;
}

export default function AdminTableLoading({
  columns = 4,
  rows = 5,
  showHeader = true,
  message,
  submessage,
  variant = 'skeleton',
  className
}: AdminTableLoadingProps) {

  // Skeleton loading variant
  const SkeletonTable = () => (
    <div className="w-full">
      {/* Table Header Skeleton */}
      {showHeader && (
        <div className="border-b border-gray-200 bg-gray-50 px-6 py-3">
          <div className="flex space-x-4">
            {Array.from({ length: columns }).map((_, i) => (
              <div
                key={i}
                className={cn(
                  'h-4 bg-gray-300 rounded animate-pulse',
                  i === 0 ? 'w-32' : i === columns - 1 ? 'w-20' : 'w-24'
                )}
              />
            ))}
          </div>
        </div>
      )}

      {/* Table Rows Skeleton */}
      <div className="divide-y divide-gray-200">
        {Array.from({ length: rows }).map((_, rowIndex) => (
          <div key={rowIndex} className="px-6 py-4">
            <div className="flex space-x-4 items-center">
              {Array.from({ length: columns }).map((_, colIndex) => (
                <div
                  key={colIndex}
                  className={cn(
                    'h-4 bg-gray-200 rounded animate-pulse',
                    colIndex === 0 ? 'w-32' : 
                    colIndex === 1 ? 'w-48' :
                    colIndex === columns - 1 ? 'w-20' : 'w-24'
                  )}
                  style={{
                    animationDelay: `${(rowIndex * 0.1) + (colIndex * 0.05)}s`
                  }}
                />
              ))}
            </div>
          </div>
        ))}
      </div>
    </div>
  );

  // Spinner loading variant
  const SpinnerTable = () => (
    <div className="w-full">
      {/* Table Header */}
      {showHeader && (
        <div className="border-b border-gray-200 bg-gray-50 px-6 py-3">
          <div className="flex space-x-4">
            {Array.from({ length: columns }).map((_, i) => (
              <div
                key={i}
                className={cn(
                  'h-4 bg-gray-300 rounded',
                  i === 0 ? 'w-32' : i === columns - 1 ? 'w-20' : 'w-24'
                )}
              />
            ))}
          </div>
        </div>
      )}

      {/* Loading Spinner in Center */}
      <div className="flex items-center justify-center py-16">
        <AdminLoadingSpinner
          {...AdminLoadingPresets.tableLoading}
          message={message}
          submessage={submessage}
        />
      </div>
    </div>
  );

  // Minimal loading variant
  const MinimalTable = () => (
    <div className="w-full">
      {/* Table Header */}
      {showHeader && (
        <div className="border-b border-gray-200 bg-gray-50 px-6 py-3">
          <div className="flex space-x-4">
            {Array.from({ length: columns }).map((_, i) => (
              <div
                key={i}
                className={cn(
                  'h-4 bg-gray-300 rounded',
                  i === 0 ? 'w-32' : i === columns - 1 ? 'w-20' : 'w-24'
                )}
              />
            ))}
          </div>
        </div>
      )}

      {/* Simple loading rows */}
      <div className="divide-y divide-gray-200">
        {Array.from({ length: Math.min(rows, 3) }).map((_, rowIndex) => (
          <div key={rowIndex} className="px-6 py-4">
            <div className="flex space-x-4 items-center opacity-50">
              {Array.from({ length: columns }).map((_, colIndex) => (
                <div
                  key={colIndex}
                  className={cn(
                    'h-3 bg-gray-200 rounded',
                    colIndex === 0 ? 'w-32' : 
                    colIndex === 1 ? 'w-48' :
                    colIndex === columns - 1 ? 'w-20' : 'w-24'
                  )}
                />
              ))}
            </div>
          </div>
        ))}
      </div>

      {/* Loading indicator at bottom */}
      <div className="flex items-center justify-center py-8 border-t border-gray-100">
        <AdminLoadingSpinner
          size="small"
          variant="dots"
          color="gray"
          message={message || "Loading more data..."}
        />
      </div>
    </div>
  );

  const renderTable = () => {
    switch (variant) {
      case 'skeleton':
        return <SkeletonTable />;
      case 'spinner':
        return <SpinnerTable />;
      case 'minimal':
        return <MinimalTable />;
      default:
        return <SkeletonTable />;
    }
  };

  return (
    <div className={cn('bg-white shadow-sm rounded-lg border border-gray-200 overflow-hidden', className)}>
      {renderTable()}
    </div>
  );
}

// Predefined table loading configurations
export const AdminTableLoadingPresets = {
  // Standard data table
  dataTable: {
    columns: 5,
    rows: 8,
    showHeader: true,
    variant: 'skeleton' as const,
    message: 'Loading data...',
    submessage: 'Please wait while we fetch your information'
  },

  // User management table
  userTable: {
    columns: 6,
    rows: 6,
    showHeader: true,
    variant: 'skeleton' as const,
    message: 'Loading users...',
    submessage: 'Fetching user accounts and permissions'
  },

  // Simple list
  simpleList: {
    columns: 3,
    rows: 5,
    showHeader: false,
    variant: 'minimal' as const,
    message: 'Loading...'
  },

  // Large dataset
  largeDataset: {
    columns: 7,
    rows: 10,
    showHeader: true,
    variant: 'spinner' as const,
    message: 'Loading large dataset...',
    submessage: 'This may take a few moments'
  }
};
