'use client';

import React from 'react';
import Image from 'next/image';
import Link from 'next/link';
import { motion } from 'framer-motion';
import { 
  Calendar, 
  MapPin, 
  Users, 
  Heart,
  ArrowRight,
  Clock,
  Star,
  Camera,
  Plane,
  Mail
} from 'lucide-react';

const upcomingFeatures = [
  {
    icon: Users,
    title: 'Family Packages',
    description: 'Specially designed holiday packages for families with children of all ages'
  },
  {
    icon: Heart,
    title: 'Romantic Getaways',
    description: 'Perfect destinations and experiences for couples seeking memorable moments'
  },
  {
    icon: MapPin,
    title: 'Destination Variety',
    description: 'From hill stations to beaches, cultural sites to adventure destinations'
  },
  {
    icon: Camera,
    title: 'Photography Tours',
    description: 'Capture stunning landscapes and cultural moments with guided photo tours'
  },
  {
    icon: Star,
    title: 'Luxury Experiences',
    description: 'Premium accommodations and exclusive experiences for discerning travelers'
  },
  {
    icon: Plane,
    title: 'International Tours',
    description: 'Explore global destinations with our carefully curated international packages'
  }
];

const containerVariants = {
  hidden: { opacity: 0 },
  visible: {
    opacity: 1,
    transition: {
      staggerChildren: 0.1,
    },
  },
};

const itemVariants = {
  hidden: { opacity: 0, y: 20 },
  visible: {
    opacity: 1,
    y: 0,
    transition: {
      duration: 0.6,
    },
  },
};

export default function HolidaysService() {
  return (
    <div className="py-16 lg:py-24">
      <div className="container-custom">
        {/* Hero Section */}
        <motion.div
          initial={{ opacity: 0, y: 30 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8 }}
          className="text-center mb-16"
        >
          <h1 className="text-4xl lg:text-6xl font-bold mb-6">
            <span className="bg-gradient-to-r from-primary-600 to-accent-600 bg-clip-text text-transparent">
              Positive7 Holidays
            </span>
          </h1>
          <div className="inline-flex items-center px-6 py-3 bg-orange-100 text-orange-800 rounded-full font-medium mb-6">
            <Clock className="h-5 w-5 mr-2" />
            Coming Soon...
          </div>
          <p className="text-xl text-gray-600 max-w-4xl mx-auto leading-relaxed">
            Comprehensive holiday packages designed to create unforgettable memories
          </p>
        </motion.div>

        {/* Main Content Section */}
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-12 mb-16">
          {/* Content */}
          <motion.div
            initial={{ opacity: 0, x: -30 }}
            animate={{ opacity: 1, x: 0 }}
            transition={{ duration: 0.8, delay: 0.2 }}
            className="space-y-6"
          >
            <h2 className="text-3xl font-bold mb-6">
              <span className="bg-gradient-to-r from-primary-600 to-accent-600 bg-clip-text text-transparent">
                Holiday Experiences Await
              </span>
            </h2>
            
            <div className="space-y-4 text-gray-600 leading-relaxed">
              <p>
                We're excited to announce that Positive7 Holidays is coming soon! Our team is working 
                diligently to create comprehensive holiday packages that will cater to all types of 
                travelers - from families seeking quality time together to couples looking for romantic 
                getaways.
              </p>
              
              <p>
                Building on our expertise in educational tours and adventure experiences, Positive7 Holidays 
                will offer meticulously planned vacation packages that combine comfort, adventure, and 
                cultural immersion. Whether you're dreaming of serene hill stations, pristine beaches, 
                or vibrant cultural destinations, we'll have something special for you.
              </p>
            </div>

            {/* Coming Soon Features */}
            <div className="bg-gradient-to-r from-orange-50 to-amber-50 rounded-xl p-6 border border-orange-100">
              <h3 className="text-lg font-bold text-gray-900 mb-4 flex items-center">
                <Star className="h-5 w-5 text-orange-600 mr-2" />
                What to Expect
              </h3>
              <ul className="space-y-2 text-gray-600">
                <li className="flex items-center">
                  <div className="h-2 w-2 bg-orange-400 rounded-full mr-3"></div>
                  Customizable holiday packages for all budgets
                </li>
                <li className="flex items-center">
                  <div className="h-2 w-2 bg-orange-400 rounded-full mr-3"></div>
                  Expert local guides and support
                </li>
                <li className="flex items-center">
                  <div className="h-2 w-2 bg-orange-400 rounded-full mr-3"></div>
                  Handpicked accommodations and experiences
                </li>
                <li className="flex items-center">
                  <div className="h-2 w-2 bg-orange-400 rounded-full mr-3"></div>
                  24/7 customer support during your trip
                </li>
              </ul>
            </div>
          </motion.div>

          {/* Image */}
          <motion.div
            initial={{ opacity: 0, x: 30 }}
            animate={{ opacity: 1, x: 0 }}
            transition={{ duration: 0.8, delay: 0.4 }}
            className="relative h-96 lg:h-full rounded-2xl overflow-hidden"
          >
            <Image
              src="/images/services/holidays.jpg"
              alt="Holiday Destinations"
              fill
              className="object-cover"
            />
            <div className="absolute inset-0 bg-gradient-to-t from-black/20 to-transparent" />
            <div className="absolute bottom-4 left-4 right-4">
              <div className="bg-white/90 backdrop-blur-sm rounded-lg p-4">
                <p className="text-sm font-medium text-gray-900">
                  Beautiful destinations await your discovery
                </p>
              </div>
            </div>
          </motion.div>
        </div>

        {/* Upcoming Features */}
        <motion.div
          variants={containerVariants}
          initial="hidden"
          animate="visible"
          className="mb-16"
        >
          <motion.h3
            variants={itemVariants}
            className="text-3xl font-bold mb-12 text-center"
          >
            <span className="bg-gradient-to-r from-primary-600 to-accent-600 bg-clip-text text-transparent">
              Upcoming Holiday Categories
            </span>
          </motion.h3>
          
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            {upcomingFeatures.map((feature, index) => {
              const IconComponent = feature.icon;
              
              return (
                <motion.div
                  key={index}
                  variants={itemVariants}
                  className="bg-white/70 backdrop-blur-sm rounded-xl p-6 border border-white/50 hover:shadow-lg transition-all duration-300 hover:scale-105"
                >
                  <div className="flex items-start space-x-4">
                    <div className="flex-shrink-0 p-3 bg-gradient-to-r from-orange-500 to-amber-600 rounded-lg">
                      <IconComponent className="h-6 w-6 text-white" />
                    </div>
                    <div className="flex-1">
                      <h4 className="font-bold text-gray-900 mb-2">
                        {feature.title}
                      </h4>
                      <p className="text-sm text-gray-600 leading-relaxed">
                        {feature.description}
                      </p>
                    </div>
                  </div>
                </motion.div>
              );
            })}
          </div>
        </motion.div>

        {/* Newsletter Signup */}
        <motion.div
          initial={{ opacity: 0, y: 30 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8, delay: 0.6 }}
          className="text-center"
        >
          <div className="bg-gradient-to-r from-orange-600 to-amber-600 rounded-2xl p-8 text-white">
            <h3 className="text-2xl font-bold mb-4">
              Be the First to Know!
            </h3>
            <p className="text-orange-100 mb-6 max-w-2xl mx-auto">
              Sign up to be notified when Positive7 Holidays launches. Get exclusive early access 
              to our holiday packages and special launch offers.
            </p>
            <div className="flex flex-col sm:flex-row gap-4 justify-center">
              <Link
                href="/contact"
                className="inline-flex items-center px-8 py-3 bg-white text-orange-600 font-medium rounded-full hover:bg-gray-100 transition-colors duration-300 hover:scale-105 transform"
              >
                <Mail className="mr-2 h-4 w-4" />
                Notify Me
              </Link>
              <Link
                href={"/services" as any}
                className="inline-flex items-center px-8 py-3 border-2 border-white text-white font-medium rounded-full hover:bg-white hover:text-orange-600 transition-all duration-300 hover:scale-105 transform"
              >
                Explore Other Services
                <ArrowRight className="ml-2 h-4 w-4" />
              </Link>
            </div>
          </div>
        </motion.div>
      </div>
    </div>
  );
}
