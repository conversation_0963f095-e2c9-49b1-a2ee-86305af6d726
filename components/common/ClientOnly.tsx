'use client';

import { useEffect, useState, ReactNode } from 'react';

interface ClientOnlyProps {
  children: ReactNode;
  fallback?: ReactNode;
  delay?: number;
}

/**
 * ClientOnly component prevents hydration mismatches by only rendering
 * children on the client side after hydration is complete.
 */
export function ClientOnly({ children, fallback = null, delay = 0 }: ClientOnlyProps) {
  const [hasMounted, setHasMounted] = useState(false);

  useEffect(() => {
    const timer = setTimeout(() => {
      setHasMounted(true);
    }, delay);

    return () => clearTimeout(timer);
  }, [delay]);

  if (!hasMounted) {
    return <>{fallback}</>;
  }

  return <>{children}</>;
}

/**
 * Higher-order component version of ClientOnly
 */
export function withClientOnly<P extends object>(
  Component: React.ComponentType<P>,
  fallback?: ReactNode,
  delay?: number
) {
  return function ClientOnlyWrapper(props: P) {
    return (
      <ClientOnly fallback={fallback} delay={delay}>
        <Component {...props} />
      </ClientOnly>
    );
  };
}

/**
 * Hook to check if component has mounted (client-side)
 */
export function useHasMounted() {
  const [hasMounted, setHasMounted] = useState(false);

  useEffect(() => {
    setHasMounted(true);
  }, []);

  return hasMounted;
}

/**
 * Safe client-only component that handles errors gracefully
 */
export function SafeClientOnly({ 
  children, 
  fallback = null, 
  onError 
}: {
  children: ReactNode;
  fallback?: ReactNode;
  onError?: (error: Error) => void;
}) {
  const [hasMounted, setHasMounted] = useState(false);
  const [hasError, setHasError] = useState(false);

  useEffect(() => {
    try {
      setHasMounted(true);
    } catch (error) {
      setHasError(true);
      if (onError && error instanceof Error) {
        onError(error);
      }
    }
  }, [onError]);

  if (hasError) {
    return <>{fallback}</>;
  }

  if (!hasMounted) {
    return <>{fallback}</>;
  }

  return <>{children}</>;
}
