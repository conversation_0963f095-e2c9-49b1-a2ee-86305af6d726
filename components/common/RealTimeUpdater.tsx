'use client';

import { usePublicRealTimeUpdates } from '@/hooks/useRealTimeUpdates';

interface RealTimeUpdaterProps {
  /**
   * Whether to enable real-time updates
   * Default: true
   */
  enabled?: boolean;
  
  /**
   * Whether to show a visual indicator when updates are happening
   * Default: false
   */
  showIndicator?: boolean;
  
  /**
   * Custom interval in milliseconds
   * Default: uses hook default (8 seconds)
   */
  interval?: number;
}

/**
 * Component that enables real-time updates for public pages
 * Add this to any public page that needs to reflect admin changes immediately
 * 
 * Usage:
 * <RealTimeUpdater enabled={true} showIndicator={false} />
 */
export function RealTimeUpdater({
  enabled = true,
  showIndicator = false,
  interval
}: RealTimeUpdaterProps) {
  const { triggerUpdate, isEnabled } = usePublicRealTimeUpdates(enabled, interval);

  // This component doesn't render anything visible by default
  // It just runs the real-time update logic in the background
  
  if (showIndicator && isEnabled) {
    return (
      <div className="fixed bottom-4 right-4 z-50">
        <div className="bg-green-500 text-white px-3 py-1 rounded-full text-xs flex items-center gap-2">
          <div className="w-2 h-2 bg-white rounded-full animate-pulse"></div>
          Live Updates Active
        </div>
      </div>
    );
  }

  return null;
}

/**
 * Component for debugging real-time updates
 * Shows current status and allows manual triggering
 */
export function RealTimeUpdaterDebug({ enabled = true }: { enabled?: boolean }) {
  const { triggerUpdate, isEnabled, interval } = usePublicRealTimeUpdates(enabled);

  return (
    <div className="fixed bottom-4 left-4 z-50 bg-gray-800 text-white p-4 rounded-lg shadow-lg max-w-xs">
      <h3 className="font-bold text-sm mb-2">Real-Time Updates</h3>
      <div className="text-xs space-y-1">
        <div>Status: {isEnabled ? '🟢 Active' : '🔴 Inactive'}</div>
        <div>Interval: {interval}ms</div>
        <div>Page Visible: {!document.hidden ? '👁️ Yes' : '👁️‍🗨️ No'}</div>
      </div>
      <button
        onClick={triggerUpdate}
        className="mt-2 bg-blue-500 hover:bg-blue-600 text-white px-2 py-1 rounded text-xs"
      >
        Manual Update
      </button>
    </div>
  );
}
