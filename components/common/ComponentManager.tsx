'use client';

import { ReactNode, Suspense } from 'react';
import { ClientOnly, SafeClientOnly } from './ClientOnly';
import { ErrorBoundary } from '@/components/error/ErrorBoundary';
import { COMPONENT_REGISTRY, ComponentConfig } from '@/lib/component-registry';

interface ComponentManagerProps {
  children: ReactNode;
  componentName: string;
  fallback?: ReactNode;
  errorFallback?: ReactNode;
}

/**
 * ComponentManager handles the loading, caching, and error handling
 * for all components in the application based on their configuration.
 */
export function ComponentManager({ 
  children, 
  componentName, 
  fallback = null,
  errorFallback = null 
}: ComponentManagerProps) {
  const config = COMPONENT_REGISTRY[componentName];
  
  if (!config) {
    console.warn(`Component ${componentName} not found in registry`);
    return <>{children}</>;
  }

  // Handle client-only components
  if (config.isClientOnly) {
    return (
      <ErrorBoundary fallback={errorFallback}>
        <SafeClientOnly 
          fallback={fallback}
          onError={(error) => {
            console.error(`Error in client-only component ${componentName}:`, error);
          }}
        >
          {children}
        </SafeClientOnly>
      </ErrorBoundary>
    );
  }

  // Handle server-side components with error boundaries
  return (
    <ErrorBoundary fallback={errorFallback}>
      <Suspense fallback={fallback}>
        {children}
      </Suspense>
    </ErrorBoundary>
  );
}

/**
 * Higher-order component that wraps components with proper management
 */
export function withComponentManager<P extends object>(
  Component: React.ComponentType<P>,
  componentName: string,
  options?: {
    fallback?: ReactNode;
    errorFallback?: ReactNode;
  }
) {
  return function ManagedComponent(props: P) {
    return (
      <ComponentManager 
        componentName={componentName}
        fallback={options?.fallback}
        errorFallback={options?.errorFallback}
      >
        <Component {...props} />
      </ComponentManager>
    );
  };
}

/**
 * Component loader that handles lazy loading and caching
 */
export function ComponentLoader({ 
  componentName, 
  children, 
  priority = 10 
}: {
  componentName: string;
  children: ReactNode;
  priority?: number;
}) {
  const config = COMPONENT_REGISTRY[componentName];
  
  if (!config) {
    return <>{children}</>;
  }

  // Load components based on priority
  const loadingDelay = Math.max(0, (config.priority - 1) * 100);

  if (config.isClientOnly) {
    return (
      <ClientOnly delay={loadingDelay}>
        {children}
      </ClientOnly>
    );
  }

  return <>{children}</>;
}

/**
 * Batch component loader for multiple components
 */
export function BatchComponentLoader({ 
  components 
}: {
  components: Array<{
    name: string;
    component: ReactNode;
  }>;
}) {
  // Sort components by priority
  const sortedComponents = components
    .map(comp => ({
      ...comp,
      config: COMPONENT_REGISTRY[comp.name]
    }))
    .filter(comp => comp.config)
    .sort((a, b) => (a.config?.priority || 999) - (b.config?.priority || 999));

  return (
    <>
      {sortedComponents.map(({ name, component, config }) => (
        <ComponentLoader key={name} componentName={name}>
          {component}
        </ComponentLoader>
      ))}
    </>
  );
}

/**
 * Development-only component wrapper
 */
export function DevOnly({ children }: { children: ReactNode }) {
  if (process.env.NODE_ENV !== 'development') {
    return null;
  }

  return (
    <SafeClientOnly>
      {children}
    </SafeClientOnly>
  );
}

/**
 * Production-only component wrapper
 */
export function ProdOnly({ children }: { children: ReactNode }) {
  if (process.env.NODE_ENV === 'development') {
    return null;
  }

  return <>{children}</>;
}
