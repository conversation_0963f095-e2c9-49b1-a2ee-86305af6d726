'use client'

import React, { useState, useMemo } from 'react'
import Image from 'next/image'
import Link from 'next/link'
import { motion } from 'framer-motion'
import {
  Search,
  Calendar,
  Tag,
  Clock,
  User,
  Grid3X3,
  List,
  Filter,
  RefreshCw
} from 'lucide-react'
import { usePublicBlogs } from '@/hooks/useBlogs'
import { useDebounce } from '@/hooks/useDebounce'
import LoadingSpinner from '@/components/ui/LoadingSpinner'
import { getImageWithCacheBusting } from '@/lib/image-fallbacks'

import { BlogPost } from '@/types/database'

// Helper function to calculate reading time more accurately
const calculateReadingTime = (content: string): number => {
  // Remove HTML tags and get plain text
  const plainText = content.replace(/<[^>]*>/g, '');
  // Count words (split by whitespace and filter out empty strings)
  const wordCount = plainText.split(/\s+/).filter(word => word.length > 0).length;
  // Average reading speed is 200-250 words per minute, using 200 for conservative estimate
  const readingTime = Math.ceil(wordCount / 200);
  // Minimum 1 minute reading time
  return Math.max(1, readingTime);
};

interface BlogClientProps {
  initialPosts?: BlogPost[]
}

export default function BlogClient({ initialPosts = [] }: BlogClientProps) {
  const [searchQuery, setSearchQuery] = useState('')
  const [selectedCategory, setSelectedCategory] = useState('All')
  const [viewMode, setViewMode] = useState<'grid' | 'list'>('grid')

  // Debounced search to avoid excessive API calls
  const debouncedSearch = useDebounce(searchQuery, 300)

  // Use React Query to fetch blogs with real-time updates
  const { data: blogsData, isLoading, error, refetch } = usePublicBlogs({
    search: debouncedSearch || undefined,
    category: selectedCategory !== 'All' ? selectedCategory : undefined,
    limit: 50
  })

  // Use fetched data if available, otherwise fall back to initial data
  const posts: BlogPost[] = blogsData?.data || initialPosts
  const [showFilters, setShowFilters] = useState(false)

  // Get unique categories
  const categories = useMemo(() => {
    const cats = posts
      .map(post => post.category)
      .filter((cat): cat is string => Boolean(cat))
      .filter((cat, index, arr) => arr.indexOf(cat) === index)
    return ['All', ...cats]
  }, [posts])

  // Filter posts (client-side filtering for categories when using search)
  const filteredPosts = useMemo(() => {
    if (!searchQuery && selectedCategory === 'All') {
      return posts // Use server-filtered data when no client-side filtering needed
    }

    return posts.filter(post => {
      const matchesSearch = !searchQuery ||
        post.title.toLowerCase().includes(searchQuery.toLowerCase()) ||
        post.excerpt?.toLowerCase().includes(searchQuery.toLowerCase()) ||
        post.content.toLowerCase().includes(searchQuery.toLowerCase())

      const matchesCategory = selectedCategory === 'All' || post.category === selectedCategory

      return matchesSearch && matchesCategory
    })
  }, [posts, searchQuery, selectedCategory])

  const clearFilters = () => {
    setSearchQuery('')
    setSelectedCategory('All')
  }

  // Error state
  if (error && posts.length === 0) {
    return (
      <div className="text-center py-12">
        <div className="text-gray-400 mb-4">
          <Search className="w-16 h-16 mx-auto" />
        </div>
        <h3 className="text-xl font-medium text-gray-900 mb-2">Failed to load blog posts</h3>
        <p className="text-gray-600 mb-4">
          There was an error loading the blog posts. Please try again.
        </p>
        <button
          onClick={() => refetch()}
          className="inline-flex items-center px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 transition-colors"
        >
          <RefreshCw className="w-4 h-4 mr-2" />
          Try Again
        </button>
      </div>
    )
  }

  return (
    <div className="space-y-8">
      {/* Header */}
      <div className="text-center">
        <motion.h1 
          className="text-4xl md:text-5xl font-bold text-gray-900 mb-4"
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6 }}
        >
          Travel Stories & Insights
        </motion.h1>
        <motion.p 
          className="text-xl text-gray-600 max-w-3xl mx-auto"
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6, delay: 0.1 }}
        >
          Discover amazing destinations, travel tips, and educational insights from our adventures
        </motion.p>
      </div>

      {/* Search and Filters */}
      <div className="card-modern p-6">
        <div className="flex flex-col lg:flex-row gap-4 items-center justify-between">
          {/* Search */}
          <div className="relative flex-1 max-w-md">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-5 h-5" />
            <input
              type="text"
              placeholder="Search posts..."
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
              className="w-full pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
            />
          </div>

          {/* Category Filter */}
          <div className="flex items-center gap-4">
            <select
              value={selectedCategory}
              onChange={(e) => setSelectedCategory(e.target.value)}
              className="px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
            >
              {categories.map(category => (
                <option key={category} value={category}>
                  {category}
                </option>
              ))}
            </select>

            {/* View Mode Toggle */}
            <div className="flex border border-gray-300 rounded-lg overflow-hidden">
              <button
                onClick={() => setViewMode('grid')}
                className={`p-2 ${viewMode === 'grid' ? 'bg-blue-500 text-white' : 'bg-white text-gray-600 hover:bg-gray-50'}`}
              >
                <Grid3X3 className="w-4 h-4" />
              </button>
              <button
                onClick={() => setViewMode('list')}
                className={`p-2 ${viewMode === 'list' ? 'bg-blue-500 text-white' : 'bg-white text-gray-600 hover:bg-gray-50'}`}
              >
                <List className="w-4 h-4" />
              </button>
            </div>
          </div>
        </div>

        {/* Results Count */}
        <div className="mt-4 text-sm text-gray-600">
          {filteredPosts.length} {filteredPosts.length === 1 ? 'post' : 'posts'} found
        </div>
      </div>

      {/* Loading State */}
      {isLoading && posts.length === 0 && (
        <div className="flex items-center justify-center py-12">
          <LoadingSpinner size="large" />
        </div>
      )}

      {/* Posts Grid/List */}
      {filteredPosts.length > 0 ? (
        <div className={`
          ${viewMode === 'grid'
            ? 'grid grid-cols-1 md:grid-cols-2 xl:grid-cols-3 gap-6'
            : 'space-y-6'
          }
        `}>
          {filteredPosts.map((post, index) => (
            <BlogPostCard
              key={post.id}
              post={post}
              viewMode={viewMode}
              index={index}
            />
          ))}
        </div>
      ) : (
        <div className="text-center py-12">
          <div className="text-gray-400 mb-4">
            <Search className="w-16 h-16 mx-auto" />
          </div>
          <h3 className="text-xl font-medium text-gray-900 mb-2">No posts found</h3>
          <p className="text-gray-600 mb-6">
            Try adjusting your search criteria or filters
          </p>
          <button
            onClick={clearFilters}
            className="px-4 py-2 border border-gray-300 bg-white text-gray-700 hover:bg-gray-50 rounded-md transition-colors"
          >
            Clear Filters
          </button>
        </div>
      )}
    </div>
  )
}

// Blog Post Card Component
function BlogPostCard({ post, viewMode, index }: { post: BlogPost; viewMode: 'grid' | 'list'; index: number }) {
  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'long',
      day: 'numeric'
    })
  }

  const readingTime = calculateReadingTime(post.content)

  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.6, delay: index * 0.1 }}
      className="group"
    >
      <Link href={`/blog/${post.slug}`}>
        <div className={`
          card-modern overflow-hidden
          ${viewMode === 'list' ? 'flex gap-6' : ''}
        `}>
          {/* Post Image */}
          <div className={`relative overflow-hidden ${
            viewMode === 'list' ? 'w-80 h-48 flex-shrink-0' : 'h-48'
          }`}>
            <Image
              src={getImageWithCacheBusting(post.featured_image_url || '/images/fallback-blog.jpg', true)}
              alt={post.title}
              fill
              className="object-cover transition-transform duration-300 group-hover:scale-105"
            />
            {post.category && (
              <div className="absolute top-3 left-3">
                <span className="px-3 py-1 bg-blue-500 text-white text-xs font-medium rounded-full">
                  {post.category}
                </span>
              </div>
            )}
          </div>

          {/* Post Content */}
          <div className="p-6 flex-1">
            <div className="flex items-center gap-4 text-sm text-gray-500 mb-3">
              <div className="flex items-center gap-1">
                <User className="w-4 h-4" />
                {post.author}
              </div>
              <div className="flex items-center gap-1">
                <Calendar className="w-4 h-4" />
                {formatDate(post.published_at || post.created_at || new Date().toISOString())}
              </div>
              <div className="flex items-center gap-1">
                <Clock className="w-4 h-4" />
                {readingTime} min read
              </div>
            </div>

            <h3 className="text-xl font-bold text-gray-900 mb-3 group-hover:text-blue-600 transition-colors line-clamp-2">
              {post.title}
            </h3>

            {post.excerpt && (
              <p className="text-gray-600 mb-4 line-clamp-3">
                {post.excerpt}
              </p>
            )}

            {post.tags && post.tags.length > 0 && (
              <div className="flex flex-wrap gap-2">
                {post.tags.slice(0, 3).map((tag, tagIndex) => (
                  <span
                    key={tagIndex}
                    className="px-2 py-1 bg-gray-100 text-gray-600 text-xs rounded-md flex items-center gap-1"
                  >
                    <Tag className="w-3 h-3" />
                    {tag}
                  </span>
                ))}
                {post.tags.length > 3 && (
                  <span className="px-2 py-1 bg-gray-100 text-gray-600 text-xs rounded-md">
                    +{post.tags.length - 3} more
                  </span>
                )}
              </div>
            )}
          </div>
        </div>
      </Link>
    </motion.div>
  )
}
