'use client'

import React, { useState } from 'react'
import Image from 'next/image'
import Link from 'next/link'
import { motion } from 'framer-motion'
import {
  Camera,
  Search,
  MapPin,
  ImageIcon,
  ArrowRight
} from 'lucide-react'

interface Gallery {
  id: string
  name: string
  description?: string | null
  featured_image_url?: string | null
  gallery_images: Array<{
    id: string
    image_url: string
  }>
  trips?: {
    id: string
    title: string
    destination: string
  } | null
}

interface GalleryGridProps {
  galleries: Gallery[]
}

export default function GalleryGrid({ galleries }: GalleryGridProps) {
  const [searchQuery, setSearchQuery] = useState('')

  // Filter galleries based on search with null-safe filtering
  const filteredGalleries = galleries.filter(gallery => {
    if (!searchQuery) return true

    const searchLower = searchQuery.toLowerCase()
    return (
      gallery.name?.toLowerCase().includes(searchLower) ||
      gallery.description?.toLowerCase().includes(searchLower) ||
      gallery.trips?.title?.toLowerCase().includes(searchLower) ||
      gallery.trips?.destination?.toLowerCase().includes(searchLower)
    )
  })

  return (
    <div className="space-y-8">
      {/* Header */}
      <div className="text-center">
        <motion.div
          className="inline-flex items-center justify-center w-16 h-16 bg-blue-100 rounded-full mb-6"
          initial={{ opacity: 0, scale: 0.5 }}
          animate={{ opacity: 1, scale: 1 }}
          transition={{ duration: 0.6 }}
        >
          <Camera className="w-8 h-8 text-blue-600" />
        </motion.div>
        
        <motion.h1 
          className="text-4xl md:text-5xl font-bold text-gray-900 mb-4"
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6, delay: 0.1 }}
        >
          Photo Galleries
        </motion.h1>
        
        <motion.p 
          className="text-xl text-gray-600 max-w-3xl mx-auto"
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6, delay: 0.2 }}
        >
          Explore our collection of memorable moments from educational tours, adventure camps, and cultural experiences
        </motion.p>
      </div>

      {/* Search */}
      <motion.div
        className="card-modern p-6"
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.6, delay: 0.3 }}
      >
        <div className="flex flex-col sm:flex-row gap-4 items-center justify-between">
          <div className="relative flex-1 max-w-md">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-5 h-5" />
            <input
              type="text"
              placeholder="Search galleries..."
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
              className="w-full pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
            />
          </div>
          
          <div className="text-sm text-gray-600">
            {filteredGalleries.length} {filteredGalleries.length === 1 ? 'gallery' : 'galleries'} found
          </div>
        </div>
      </motion.div>

      {/* Galleries Grid */}
      {filteredGalleries.length > 0 ? (
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {filteredGalleries.map((gallery, index) => (
            <motion.div
              key={gallery.id}
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6, delay: index * 0.1 }}
            >
              <Link href={`/gallery/${gallery.id}`} className="group block">
                <div className="bg-white rounded-lg shadow-md overflow-hidden hover:shadow-lg transition-shadow duration-300">
                  {/* Gallery Image */}
                  <div className="relative h-48 bg-gray-200">
                    {gallery.featured_image_url || (gallery.gallery_images && gallery.gallery_images.length > 0) ? (
                      <Image
                        src={gallery.featured_image_url || gallery.gallery_images[0].image_url}
                        alt={gallery.name}
                        fill
                        className="object-cover group-hover:scale-105 transition-transform duration-300"
                      />
                    ) : (
                      <div className="flex items-center justify-center h-full">
                        <ImageIcon className="h-12 w-12 text-gray-400" />
                      </div>
                    )}
                    


                    {/* Hover Overlay */}
                    <div className="absolute inset-0 bg-black bg-opacity-0 group-hover:bg-opacity-20 transition-all duration-300" />
                  </div>

                  {/* Gallery Info */}
                  <div className="p-4">
                    <h3 className="text-lg font-semibold text-gray-900 mb-2 group-hover:text-blue-600 transition-colors">
                      {gallery.name}
                    </h3>
                    
                    {gallery.description && (
                      <p className="text-gray-600 text-sm mb-3 line-clamp-2">
                        {gallery.description}
                      </p>
                    )}

                    {gallery.trips && (
                      <div className="flex items-center text-sm text-gray-500 mb-3">
                        <MapPin className="h-4 w-4 mr-1" />
                        {gallery.trips.title} - {gallery.trips.destination}
                      </div>
                    )}

                    <div className="flex items-center justify-end">
                      <div className="flex items-center text-blue-600 text-sm font-medium group-hover:text-blue-700">
                        View Gallery
                        <ArrowRight className="h-4 w-4 ml-1 group-hover:translate-x-1 transition-transform" />
                      </div>
                    </div>
                  </div>
                </div>
              </Link>
            </motion.div>
          ))}
        </div>
      ) : (
        <div className="text-center py-12">
          <div className="text-gray-400 mb-4">
            <Camera className="w-16 h-16 mx-auto" />
          </div>
          <h3 className="text-xl font-medium text-gray-900 mb-2">No galleries found</h3>
          <p className="text-gray-600">
            {searchQuery ? 'Try adjusting your search criteria' : 'No galleries available at the moment'}
          </p>
        </div>
      )}
    </div>
  )
}
