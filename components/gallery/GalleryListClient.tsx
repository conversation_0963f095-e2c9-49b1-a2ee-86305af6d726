'use client'

import React, { useState } from 'react'
import Image from 'next/image'
import Link from 'next/link'
import { motion } from 'framer-motion'
import {
  Camera,
  Search,
  MapPin,
  RefreshCw,
  Grid3X3,
  Grid2X2
} from 'lucide-react'
import { usePublicGalleries } from '@/hooks/useGalleries'
import LoadingSpinner from '@/components/ui/LoadingSpinner'
import { useDebounce } from '@/hooks/useDebounce'

interface GalleryImage {
  id: string
  image_url: string
  order_index: number | null
}

import { GalleryWithImages } from '@/types/gallery'

// Using GalleryWithImages directly

interface GalleryListClientProps {
  initialGalleries?: GalleryWithImages[]
}

export default function GalleryListClient({ initialGalleries = [] }: GalleryListClientProps) {
  const [search, setSearch] = useState('')
  const [gridSize, setGridSize] = useState<'large' | 'small'>('large')
  
  // Debounced search to avoid excessive API calls
  const debouncedSearch = useDebounce(search, 300)

  // Use React Query to fetch galleries with real-time updates
  const { data: galleriesData, isLoading, error, refetch } = usePublicGalleries({
    search: debouncedSearch || undefined,
    limit: 50
  })

  // Use fetched data if available, otherwise fall back to initial data
  // Note: API returns { data: galleryImages, galleries: galleries }
  const galleries: GalleryWithImages[] = galleriesData?.galleries || initialGalleries

  const filteredGalleries = galleries.filter(gallery => {
    if (!search) return true
    const searchLower = search.toLowerCase()
    return (
      gallery.name.toLowerCase().includes(searchLower) ||
      gallery.description?.toLowerCase().includes(searchLower) ||
      gallery.trips?.title?.toLowerCase().includes(searchLower) ||
      gallery.trips?.destination?.toLowerCase().includes(searchLower)
    )
  })

  if (error && galleries.length === 0) {
    return (
      <div className="text-center py-12">
        <div className="text-gray-400 mb-4">
          <Camera className="w-16 h-16 mx-auto" />
        </div>
        <h3 className="text-xl font-medium text-gray-900 mb-2">Failed to load galleries</h3>
        <p className="text-gray-600 mb-4">
          There was an error loading the galleries. Please try again.
        </p>
        <button
          onClick={() => refetch()}
          className="inline-flex items-center px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 transition-colors"
        >
          <RefreshCw className="w-4 h-4 mr-2" />
          Try Again
        </button>
      </div>
    )
  }

  return (
    <div className="space-y-8">
      {/* Header */}
      <div className="text-center">
        <motion.div
          className="inline-flex items-center justify-center w-16 h-16 bg-blue-100 rounded-full mb-6"
          initial={{ opacity: 0, scale: 0.5 }}
          animate={{ opacity: 1, scale: 1 }}
          transition={{ duration: 0.6 }}
        >
          <Camera className="w-8 h-8 text-blue-600" />
        </motion.div>
        
        <motion.h1 
          className="text-4xl md:text-5xl font-bold text-gray-900 mb-4"
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6, delay: 0.1 }}
        >
          Photo Galleries
        </motion.h1>
        
        <motion.p 
          className="text-xl text-gray-600 max-w-3xl mx-auto"
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6, delay: 0.2 }}
        >
          Explore our collection of beautiful moments captured during our educational tours
        </motion.p>
      </div>

      {/* Search and Controls */}
      <motion.div
        className="flex flex-col sm:flex-row gap-4 items-center justify-between"
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.6, delay: 0.3 }}
      >
        {/* Search */}
        <div className="relative flex-1 max-w-md">
          <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-5 h-5" />
          <input
            type="text"
            placeholder="Search galleries..."
            value={search}
            onChange={(e) => setSearch(e.target.value)}
            className="w-full pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
          />
        </div>

        {/* Grid Size Controls */}
        <div className="flex border border-gray-300 rounded-lg overflow-hidden">
          <button
            onClick={() => setGridSize('large')}
            className={`p-2 ${gridSize === 'large' ? 'bg-blue-500 text-white' : 'bg-white text-gray-600 hover:bg-gray-50'}`}
          >
            <Grid2X2 className="w-4 h-4" />
          </button>
          <button
            onClick={() => setGridSize('small')}
            className={`p-2 ${gridSize === 'small' ? 'bg-blue-500 text-white' : 'bg-white text-gray-600 hover:bg-gray-50'}`}
          >
            <Grid3X3 className="w-4 h-4" />
          </button>
        </div>
      </motion.div>

      {/* Loading State */}
      {isLoading && galleries.length === 0 && (
        <div className="flex items-center justify-center py-12">
          <LoadingSpinner size="large" />
        </div>
      )}

      {/* Galleries Grid */}
      {filteredGalleries.length > 0 ? (
        <div className={`
          grid gap-6
          ${gridSize === 'large' 
            ? 'grid-cols-1 md:grid-cols-2 xl:grid-cols-3' 
            : 'grid-cols-2 md:grid-cols-3 xl:grid-cols-4'
          }
        `}>
          {filteredGalleries.map((gallery, index) => (
            <GalleryCard
              key={gallery.id}
              gallery={gallery}
              index={index}
              gridSize={gridSize}
            />
          ))}
        </div>
      ) : !isLoading ? (
        <div className="text-center py-12">
          <div className="text-gray-400 mb-4">
            <Search className="w-16 h-16 mx-auto" />
          </div>
          <h3 className="text-xl font-medium text-gray-900 mb-2">No galleries found</h3>
          <p className="text-gray-600 mb-6">
            {search ? 'Try adjusting your search criteria' : 'No galleries are available at the moment'}
          </p>
          {search && (
            <button
              onClick={() => setSearch('')}
              className="px-4 py-2 border border-gray-300 bg-white text-gray-700 hover:bg-gray-50 rounded-md transition-colors"
            >
              Clear Search
            </button>
          )}
        </div>
      ) : null}
    </div>
  )
}

interface GalleryCardProps {
  gallery: GalleryWithImages
  index: number
  gridSize: 'large' | 'small'
}

function GalleryCard({ gallery, index, gridSize }: GalleryCardProps) {
  const imageCount = gallery.gallery_images?.length || 0
  const featuredImage = gallery.featured_image_url || gallery.gallery_images?.[0]?.image_url

  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.6, delay: index * 0.1 }}
      className="group"
    >
      <Link href={`/gallery/${gallery.id}`}>
        <div className="card-modern overflow-hidden hover:shadow-2xl transition-all duration-700 transform hover:-translate-y-2">
          {/* Image */}
          <div className={`relative overflow-hidden ${gridSize === 'large' ? 'h-64' : 'h-48'}`}>
            <Image
              src={featuredImage || '/images/fallback-gallery.jpg'}
              alt={gallery.name || 'Gallery image'}
              fill
              className="object-cover transition-transform duration-700 group-hover:scale-110"
            />
            <div className="absolute inset-0 bg-gradient-to-t from-black/60 via-transparent to-transparent" />
            
            {/* Image Count Badge */}
            <div className="absolute top-3 right-3 bg-black bg-opacity-70 text-white px-2 py-1 rounded-full text-sm">
              {imageCount} {imageCount === 1 ? 'photo' : 'photos'}
            </div>
          </div>

          {/* Content */}
          <div className="p-6">
            <h3 className="text-xl font-bold text-gray-900 mb-2 group-hover:text-blue-600 transition-colors">
              {gallery.name}
            </h3>
            
            {gallery.description && (
              <p className="text-gray-600 mb-3 line-clamp-2">
                {gallery.description}
              </p>
            )}

            {gallery.trips && (
              <div className="flex items-center text-gray-500 text-sm">
                <MapPin className="h-4 w-4 mr-1" />
                {gallery.trips.title} - {gallery.trips.destination}
              </div>
            )}
          </div>
        </div>
      </Link>
    </motion.div>
  )
}
