'use client'

import React, { useState } from 'react'
import Image from 'next/image'
import Link from 'next/link'
import { motion, AnimatePresence } from 'framer-motion'
import {
  ArrowLeft,
  Camera,
  MapPin,
  X,
  ChevronLeft,
  ChevronRight,
  Grid3X3,
  Grid2X2,
  Refresh<PERSON><PERSON>
} from 'lucide-react'
import { usePublicGallery } from '@/hooks/useGalleries'
import LoadingSpinner from '@/components/ui/LoadingSpinner'

import { GalleryWithImages, GalleryImage } from '@/types/gallery'

// Using GalleryWithImages directly

interface GalleryViewProps {
  galleryId: string
  initialGallery?: GalleryWithImages // Optional initial data for SSR
}

export default function GalleryView({ galleryId, initialGallery }: GalleryViewProps) {
  const [selectedImage, setSelectedImage] = useState<GalleryImage | null>(null)
  const [gridSize, setGridSize] = useState<'large' | 'small'>('large')

  // Use React Query to fetch gallery data with real-time updates
  const { data: gallery, isLoading, error, refetch } = usePublicGallery(galleryId, true)

  // Enhanced logic to handle real-time deletions/deactivations
  const currentGallery = gallery || initialGallery

  // Check if item was deleted/deactivated during real-time updates
  const isItemDeleted = error && error.message.includes('404') && initialGallery
  const isItemUnavailable = error && (
    error.message.includes('not found') ||
    error.message.includes('404') ||
    error.message.includes('deactivated')
  )

  if (isLoading && !currentGallery) {
    return (
      <div className="flex items-center justify-center min-h-[400px]">
        <LoadingSpinner size="large" />
      </div>
    )
  }

  // Handle case where gallery was deleted/deactivated during real-time updates
  if (isItemDeleted) {
    return (
      <div className="text-center py-12">
        <div className="text-orange-400 mb-4">
          <Camera className="w-16 h-16 mx-auto" />
        </div>
        <h3 className="text-xl font-medium text-gray-900 mb-2">Gallery No Longer Available</h3>
        <p className="text-gray-600 mb-6">
          This gallery has been removed or is no longer active. It may have been deleted by an administrator or temporarily deactivated.
        </p>
        <div className="flex flex-col sm:flex-row gap-4 justify-center">
          <Link href="/gallery">
            <button className="inline-flex items-center px-6 py-3 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors">
              <ArrowLeft className="w-4 h-4 mr-2" />
              Browse All Galleries
            </button>
          </Link>
          <button
            onClick={() => refetch()}
            className="inline-flex items-center px-6 py-3 border border-gray-300 text-gray-700 rounded-lg hover:bg-gray-50 transition-colors"
          >
            <RefreshCw className="w-4 h-4 mr-2" />
            Check Again
          </button>
        </div>
      </div>
    )
  }

  if (error && !currentGallery) {
    return (
      <div className="text-center py-12">
        <div className="text-gray-400 mb-4">
          <Camera className="w-16 h-16 mx-auto" />
        </div>
        <h3 className="text-xl font-medium text-gray-900 mb-2">Failed to load gallery</h3>
        <p className="text-gray-600 mb-4">
          There was an error loading this gallery. Please try again.
        </p>
        <button
          onClick={() => refetch()}
          className="inline-flex items-center px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 transition-colors"
        >
          <RefreshCw className="w-4 h-4 mr-2" />
          Try Again
        </button>
      </div>
    )
  }

  if (!currentGallery) {
    return (
      <div className="text-center py-12">
        <div className="text-gray-400 mb-4">
          <Camera className="w-16 h-16 mx-auto" />
        </div>
        <h3 className="text-xl font-medium text-gray-900 mb-2">Gallery not found</h3>
        <p className="text-gray-600">
          This gallery could not be found or may have been removed.
        </p>
      </div>
    )
  }

  // Sort images by order_index (create a copy to avoid mutating props)
  const sortedImages = [...(currentGallery.gallery_images || [])].sort((a, b) => (a.order_index || 0) - (b.order_index || 0))

  const openLightbox = (image: GalleryImage) => {
    setSelectedImage(image)
  }

  const closeLightbox = () => {
    setSelectedImage(null)
  }

  const navigateImage = (direction: 'prev' | 'next') => {
    if (!selectedImage) return
    
    const currentIndex = sortedImages.findIndex(img => img.id === selectedImage.id)
    let newIndex
    
    if (direction === 'prev') {
      newIndex = currentIndex > 0 ? currentIndex - 1 : sortedImages.length - 1
    } else {
      newIndex = currentIndex < sortedImages.length - 1 ? currentIndex + 1 : 0
    }
    
    setSelectedImage(sortedImages[newIndex])
  }

  return (
    <div className="space-y-8">
      {/* Header */}
      <div>
        <Link
          href="/gallery"
          className="inline-flex items-center text-blue-600 hover:text-blue-700 mb-4"
        >
          <ArrowLeft className="h-4 w-4 mr-1" />
          Back to Galleries
        </Link>
        
        <div className="text-center">
          <motion.div
            className="inline-flex items-center justify-center w-16 h-16 bg-blue-100 rounded-full mb-6"
            initial={{ opacity: 0, scale: 0.5 }}
            animate={{ opacity: 1, scale: 1 }}
            transition={{ duration: 0.6 }}
          >
            <Camera className="w-8 h-8 text-blue-600" />
          </motion.div>
          
          <motion.h1 
            className="text-4xl md:text-5xl font-bold text-gray-900 mb-4"
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6, delay: 0.1 }}
          >
            {currentGallery.name}
          </motion.h1>

          {currentGallery.description && (
            <motion.p
              className="text-xl text-gray-600 max-w-3xl mx-auto mb-4"
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6, delay: 0.2 }}
            >
              {currentGallery.description}
            </motion.p>
          )}

          {currentGallery.trips && (
            <motion.div
              className="flex items-center justify-center text-gray-500 mb-4"
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6, delay: 0.3 }}
            >
              <MapPin className="h-5 w-5 mr-2" />
              {currentGallery.trips.title} - {currentGallery.trips.destination}
            </motion.div>
          )}

          <motion.p
            className="text-gray-500"
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6, delay: 0.4 }}
          >
            {sortedImages.length} {sortedImages.length === 1 ? 'photo' : 'photos'}
          </motion.p>
        </div>
      </div>

      {/* Controls */}
      <motion.div
        className="card-modern p-4"
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.6, delay: 0.5 }}
      >
        <div className="flex justify-center">
          <div className="flex border border-gray-300 rounded-lg overflow-hidden">
            <button
              onClick={() => setGridSize('large')}
              className={`p-2 ${gridSize === 'large' ? 'bg-blue-500 text-white' : 'bg-white text-gray-600 hover:bg-gray-50'}`}
            >
              <Grid2X2 className="w-4 h-4" />
            </button>
            <button
              onClick={() => setGridSize('small')}
              className={`p-2 ${gridSize === 'small' ? 'bg-blue-500 text-white' : 'bg-white text-gray-600 hover:bg-gray-50'}`}
            >
              <Grid3X3 className="w-4 h-4" />
            </button>
          </div>
        </div>
      </motion.div>

      {/* Photo Grid */}
      {sortedImages.length > 0 ? (
        <div className={`
          grid gap-4
          ${gridSize === 'large' 
            ? 'grid-cols-1 md:grid-cols-2 xl:grid-cols-3' 
            : 'grid-cols-2 md:grid-cols-3 xl:grid-cols-4'
          }
        `}>
          {sortedImages.map((image, index) => (
            <motion.div
              key={image.id}
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6, delay: index * 0.1 }}
              className="group cursor-pointer"
              onClick={() => openLightbox(image)}
            >
              <div className="relative overflow-hidden rounded-lg bg-gray-200 aspect-square">
                <Image
                  src={image.image_url}
                  alt={currentGallery.name}
                  fill
                  className="object-cover transition-transform duration-300 group-hover:scale-110"
                />
                <div className="absolute inset-0 bg-black bg-opacity-0 group-hover:bg-opacity-40 transition-all duration-300" />
              </div>
            </motion.div>
          ))}
        </div>
      ) : (
        <div className="text-center py-12">
          <div className="text-gray-400 mb-4">
            <Camera className="w-16 h-16 mx-auto" />
          </div>
          <h3 className="text-xl font-medium text-gray-900 mb-2">No photos in this gallery</h3>
          <p className="text-gray-600">
            Photos will appear here once they are added to this gallery.
          </p>
        </div>
      )}

      {/* Lightbox */}
      <AnimatePresence>
        {selectedImage && (
          <motion.div
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            exit={{ opacity: 0 }}
            className="fixed inset-0 z-50 bg-black bg-opacity-90 flex items-center justify-center p-4"
            onClick={closeLightbox}
          >
            <motion.div
              initial={{ scale: 0.8, opacity: 0 }}
              animate={{ scale: 1, opacity: 1 }}
              exit={{ scale: 0.8, opacity: 0 }}
              className="relative max-w-4xl max-h-full"
              onClick={(e) => e.stopPropagation()}
            >
              {/* Close Button */}
              <button
                onClick={closeLightbox}
                className="absolute top-4 right-4 z-10 p-2 bg-black bg-opacity-50 text-white rounded-full hover:bg-opacity-70 transition-colors"
              >
                <X className="w-6 h-6" />
              </button>

              {/* Navigation Buttons */}
              {sortedImages.length > 1 && (
                <>
                  <button
                    onClick={() => navigateImage('prev')}
                    className="absolute left-4 top-1/2 transform -translate-y-1/2 z-10 p-2 bg-black bg-opacity-50 text-white rounded-full hover:bg-opacity-70 transition-colors"
                  >
                    <ChevronLeft className="w-6 h-6" />
                  </button>
                  
                  <button
                    onClick={() => navigateImage('next')}
                    className="absolute right-4 top-1/2 transform -translate-y-1/2 z-10 p-2 bg-black bg-opacity-50 text-white rounded-full hover:bg-opacity-70 transition-colors"
                  >
                    <ChevronRight className="w-6 h-6" />
                  </button>
                </>
              )}

              {/* Image */}
              <div className="relative">
                <Image
                  src={selectedImage.image_url}
                  alt={currentGallery.name}
                  width={800}
                  height={600}
                  className="max-w-full max-h-[80vh] object-contain"
                />
              </div>
            </motion.div>
          </motion.div>
        )}
      </AnimatePresence>
    </div>
  )
}
