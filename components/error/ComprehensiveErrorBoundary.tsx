/**
 * Comprehensive Error Boundary System
 * 
 * This provides a complete error boundary solution with different fallback
 * components for different types of errors and contexts.
 */

'use client';

import React, { Component, ErrorInfo, ReactNode } from 'react';
import { <PERSON>ert<PERSON>riangle, RefreshCw, Home, Mail, Bug, Wifi, Shield } from 'lucide-react';
import { motion, AnimatePresence } from 'framer-motion';
import Button from '@/components/ui/Button';
import { standardAnimations } from '@/lib/component-patterns';

// Error types for different scenarios
export type ErrorType = 'network' | 'auth' | 'validation' | 'chunk' | 'generic';

// Error boundary props
interface ErrorBoundaryProps {
  children: ReactNode;
  fallback?: ReactNode;
  onError?: (error: Error, errorInfo: ErrorInfo) => void;
  level?: 'page' | 'section' | 'component';
  context?: string;
}

// Error boundary state
interface ErrorBoundaryState {
  hasError: boolean;
  error?: Error;
  errorInfo?: ErrorInfo;
  errorType: ErrorType;
  errorId: string;
  retryCount: number;
}

// Error classification function
function classifyError(error: Error): ErrorType {
  const message = error.message.toLowerCase();
  
  if (message.includes('network') || message.includes('fetch') || message.includes('connection')) {
    return 'network';
  }
  
  if (message.includes('unauthorized') || message.includes('forbidden') || message.includes('auth')) {
    return 'auth';
  }
  
  if (message.includes('validation') || message.includes('invalid')) {
    return 'validation';
  }
  
  if (message.includes('chunk') || message.includes('loading')) {
    return 'chunk';
  }
  
  return 'generic';
}

// Main error boundary component
export class ComprehensiveErrorBoundary extends Component<ErrorBoundaryProps, ErrorBoundaryState> {
  private retryTimeoutId: NodeJS.Timeout | null = null;

  constructor(props: ErrorBoundaryProps) {
    super(props);
    this.state = {
      hasError: false,
      errorType: 'generic',
      errorId: '',
      retryCount: 0,
    };
  }

  static getDerivedStateFromError(error: Error): Partial<ErrorBoundaryState> {
    return {
      hasError: true,
      error,
      errorType: classifyError(error),
      errorId: Math.random().toString(36).substring(2, 15),
    };
  }

  componentDidCatch(error: Error, errorInfo: ErrorInfo) {
    this.setState({
      error,
      errorInfo,
    });

    // Log error to monitoring service
    console.error('Error caught by boundary:', error, errorInfo);
    
    // Call custom error handler
    this.props.onError?.(error, errorInfo);
    
    // Send to error tracking service in production
    if (process.env.NODE_ENV === 'production') {
      this.reportError(error, errorInfo);
    }
  }

  componentWillUnmount() {
    if (this.retryTimeoutId) {
      clearTimeout(this.retryTimeoutId);
    }
  }

  private reportError = async (error: Error, errorInfo: ErrorInfo) => {
    try {
      await fetch('/api/errors', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          message: error.message,
          stack: error.stack,
          componentStack: errorInfo.componentStack,
          errorId: this.state.errorId,
          timestamp: new Date().toISOString(),
          userAgent: navigator.userAgent,
          url: window.location.href,
          context: this.props.context,
          level: this.props.level,
        }),
      });
    } catch (reportingError) {
      console.error('Failed to report error:', reportingError);
    }
  };

  private handleRetry = () => {
    const { retryCount } = this.state;
    
    if (retryCount >= 3) {
      // Max retries reached, show different message
      return;
    }

    this.setState(prevState => ({
      hasError: false,
      error: undefined,
      errorInfo: undefined,
      retryCount: prevState.retryCount + 1,
    }));

    // Auto-retry for certain error types
    if (this.state.errorType === 'network' || this.state.errorType === 'chunk') {
      this.retryTimeoutId = setTimeout(() => {
        window.location.reload();
      }, 1000);
    }
  };

  private handleGoHome = () => {
    window.location.href = '/';
  };

  private handleReportBug = () => {
    const subject = encodeURIComponent(`Bug Report: ${this.state.error?.message || 'Unknown Error'}`);
    const body = encodeURIComponent(`
Error ID: ${this.state.errorId}
Error Type: ${this.state.errorType}
Context: ${this.props.context || 'Unknown'}
Level: ${this.props.level || 'Unknown'}
URL: ${window.location.href}
User Agent: ${navigator.userAgent}
Timestamp: ${new Date().toISOString()}

Error Details:
${this.state.error?.stack || 'No stack trace available'}

Component Stack:
${this.state.errorInfo?.componentStack || 'No component stack available'}
    `);
    
    window.open(`mailto:<EMAIL>?subject=${subject}&body=${body}`);
  };

  render() {
    if (this.state.hasError) {
      // Use custom fallback if provided
      if (this.props.fallback) {
        return this.props.fallback;
      }

      // Render appropriate error UI based on error type and level
      return (
        <AnimatePresence>
          <motion.div
            {...standardAnimations.fadeIn}
            transition={{ duration: 0.3 }}
            className="min-h-[400px] flex items-center justify-center p-6"
          >
            <div className="max-w-md w-full text-center">
              <ErrorIcon errorType={this.state.errorType} />
              <ErrorMessage 
                errorType={this.state.errorType}
                level={this.props.level}
                retryCount={this.state.retryCount}
              />
              <ErrorActions
                errorType={this.state.errorType}
                level={this.props.level}
                retryCount={this.state.retryCount}
                onRetry={this.handleRetry}
                onGoHome={this.handleGoHome}
                onReportBug={this.handleReportBug}
              />
              
              {/* Error details for development */}
              {process.env.NODE_ENV === 'development' && (
                <details className="mt-6 text-left">
                  <summary className="cursor-pointer text-sm text-gray-500 hover:text-gray-700">
                    Error Details (Development)
                  </summary>
                  <div className="mt-2 p-3 bg-gray-100 rounded text-xs font-mono text-gray-700 overflow-auto max-h-40">
                    <div><strong>Error:</strong> {this.state.error?.message}</div>
                    <div><strong>Stack:</strong> {this.state.error?.stack}</div>
                    <div><strong>Component Stack:</strong> {this.state.errorInfo?.componentStack}</div>
                  </div>
                </details>
              )}
            </div>
          </motion.div>
        </AnimatePresence>
      );
    }

    return this.props.children;
  }
}

// Error icon component
function ErrorIcon({ errorType }: { errorType: ErrorType }) {
  const iconMap = {
    network: Wifi,
    auth: Shield,
    validation: AlertTriangle,
    chunk: RefreshCw,
    generic: Bug,
  };

  const Icon = iconMap[errorType];
  const colorMap = {
    network: 'text-blue-600 bg-blue-100',
    auth: 'text-red-600 bg-red-100',
    validation: 'text-yellow-600 bg-yellow-100',
    chunk: 'text-purple-600 bg-purple-100',
    generic: 'text-gray-600 bg-gray-100',
  };

  return (
    <div className={`w-16 h-16 rounded-full flex items-center justify-center mx-auto mb-6 ${colorMap[errorType]}`}>
      <Icon className="w-8 h-8" />
    </div>
  );
}

// Error message component
function ErrorMessage({ 
  errorType, 
  level, 
  retryCount 
}: { 
  errorType: ErrorType; 
  level?: string; 
  retryCount: number; 
}) {
  const messages = {
    network: {
      title: 'Connection Problem',
      description: 'Unable to connect to our servers. Please check your internet connection and try again.',
    },
    auth: {
      title: 'Access Denied',
      description: 'You don\'t have permission to access this resource. Please log in or contact support.',
    },
    validation: {
      title: 'Invalid Data',
      description: 'There was a problem with the data provided. Please check your input and try again.',
    },
    chunk: {
      title: 'Loading Error',
      description: 'Failed to load part of the application. This usually resolves with a refresh.',
    },
    generic: {
      title: 'Something Went Wrong',
      description: 'An unexpected error occurred. Our team has been notified and is working on a fix.',
    },
  };

  const message = messages[errorType];
  
  return (
    <div className="mb-8">
      <h3 className="text-xl font-semibold text-gray-900 mb-4">
        {message.title}
      </h3>
      <p className="text-gray-600 leading-relaxed">
        {message.description}
      </p>
      {retryCount > 0 && (
        <p className="text-sm text-gray-500 mt-2">
          Retry attempt: {retryCount}/3
        </p>
      )}
    </div>
  );
}

// Error actions component
function ErrorActions({
  errorType,
  level,
  retryCount,
  onRetry,
  onGoHome,
  onReportBug,
}: {
  errorType: ErrorType;
  level?: string;
  retryCount: number;
  onRetry: () => void;
  onGoHome: () => void;
  onReportBug: () => void;
}) {
  const showRetry = retryCount < 3 && (errorType === 'network' || errorType === 'chunk' || errorType === 'generic');
  const showHome = level === 'page';
  
  return (
    <div className="space-y-3">
      {showRetry && (
        <Button
          onClick={onRetry}
          className="w-full bg-gradient-to-r from-blue-600 to-green-600 hover:from-blue-700 hover:to-green-700"
        >
          <RefreshCw className="w-4 h-4 mr-2" />
          Try Again
        </Button>
      )}
      
      {showHome && (
        <Button
          onClick={onGoHome}
          variant="outline"
          className="w-full"
        >
          <Home className="w-4 h-4 mr-2" />
          Go Home
        </Button>
      )}
      
      <Button
        onClick={onReportBug}
        variant="ghost"
        className="w-full text-gray-600 hover:text-gray-800"
      >
        <Mail className="w-4 h-4 mr-2" />
        Report This Issue
      </Button>
    </div>
  );
}

// Higher-order component for easy wrapping
export function withErrorBoundary<P extends object>(
  WrappedComponent: React.ComponentType<P>,
  errorBoundaryProps?: Omit<ErrorBoundaryProps, 'children'>
) {
  return function ErrorBoundaryWrapper(props: P) {
    return (
      <ComprehensiveErrorBoundary {...errorBoundaryProps}>
        <WrappedComponent {...props} />
      </ComprehensiveErrorBoundary>
    );
  };
}

// Specialized error boundaries for different contexts
export const PageErrorBoundary = ({ children, ...props }: Omit<ErrorBoundaryProps, 'level'>) => (
  <ComprehensiveErrorBoundary level="page" {...props}>
    {children}
  </ComprehensiveErrorBoundary>
);

export const SectionErrorBoundary = ({ children, ...props }: Omit<ErrorBoundaryProps, 'level'>) => (
  <ComprehensiveErrorBoundary level="section" {...props}>
    {children}
  </ComprehensiveErrorBoundary>
);

export const ComponentErrorBoundary = ({ children, ...props }: Omit<ErrorBoundaryProps, 'level'>) => (
  <ComprehensiveErrorBoundary level="component" {...props}>
    {children}
  </ComprehensiveErrorBoundary>
);

export default ComprehensiveErrorBoundary;
