'use client';

import { useEffect, useState } from 'react';
import { useDevErrorLogger, createErrorLogData } from '@/hooks/useErrorLogger';
import { useHasMounted } from '@/components/common/ClientOnly';

export function WebpackErrorLogger() {
  const hasMounted = useHasMounted();
  const [isInitialized, setIsInitialized] = useState(false);
  const errorLogger = useDevErrorLogger();

  useEffect(() => {
    // Only run in development and after mounting
    if (process.env.NODE_ENV !== 'development' || !hasMounted || isInitialized) {
      return;
    }

    // Delay initialization to prevent hydration issues
    const initTimer = setTimeout(() => {
      try {
        setIsInitialized(true);
      } catch (error) {
        console.error('Failed to initialize WebpackErrorLogger:', error);
      }
    }, 500);

    return () => clearTimeout(initTimer);
  }, [hasMounted, isInitialized]);

  useEffect(() => {
    // Only run logging after initialization
    if (!isInitialized || process.env.NODE_ENV !== 'development') {
      return;
    }

    // Capture webpack errors
    const originalError = console.error;
    let isLogging = false; // Prevent recursion

    console.error = (...args) => {
      // Call original console.error first
      originalError.apply(console, args);

      // Prevent recursion during error logging
      if (isLogging) return;

      try {
        isLogging = true;

        // Properly handle non-string arguments
        const errorString = args.map(arg => {
          if (typeof arg === 'string') return arg;
          if (arg instanceof Error) return arg.message;
          if (typeof arg === 'object') {
            try {
              return JSON.stringify(arg);
            } catch {
              return '[Object]';
            }
          }
          return String(arg);
        }).join(' ');

        if (errorString.includes('webpack') ||
            errorString.includes('Module not found') ||
            errorString.includes('Failed to compile') ||
            errorString.includes('SyntaxError') ||
            errorString.includes('TypeError') ||
            errorString.includes('ReferenceError')) {

          // Log to terminal via modern error logger
          const errorData = createErrorLogData('WEBPACK_ERROR', errorString);
          errorLogger.mutate(errorData);
        }
      } catch (error) {
        // Silently fail to prevent infinite loops
      } finally {
        isLogging = false;
      }
    };

    // Capture unhandled promise rejections
    const handleUnhandledRejection = (event: PromiseRejectionEvent) => {
      const error = event.reason;
      const errorMessage = error instanceof Error ? error.message : String(error);
      const errorStack = error instanceof Error ? error.stack : undefined;
      
      const errorData = createErrorLogData('UNHANDLED_PROMISE_REJECTION', errorMessage, {
        stack: errorStack,
      });
      errorLogger.mutate(errorData);
    };

    // Capture global errors
    const handleGlobalError = (event: ErrorEvent) => {
      const errorData = createErrorLogData('GLOBAL_ERROR', event.message, {
        stack: event.error?.stack,
      });
      errorLogger.mutate(errorData);
    };

    window.addEventListener('unhandledrejection', handleUnhandledRejection);
    window.addEventListener('error', handleGlobalError);

    // Cleanup
    return () => {
      try {
        if (typeof window !== 'undefined') {
          console.error = originalError;
          window.removeEventListener('unhandledrejection', handleUnhandledRejection);
          window.removeEventListener('error', handleGlobalError);
        }
      } catch (error) {
        console.error('Error during WebpackErrorLogger cleanup:', error);
      }
    };
  }, [isInitialized, errorLogger]); // Fix: Add missing errorLogger dependency

  return null;
}
