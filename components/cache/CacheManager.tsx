'use client';

import { useEffect } from 'react';
import { addNoCacheMetaTags, clearBrowserCaches, updateServiceWorker } from '@/lib/cache-utils';

interface CacheManagerProps {
  /** Whether to clear caches on mount */
  clearOnMount?: boolean;
  /** Whether to add no-cache meta tags */
  addMetaTags?: boolean;
  /** Whether to update service worker */
  updateSW?: boolean;
}

/**
 * Client-side cache management component
 * Handles cache clearing, meta tags, and service worker updates
 */
export default function CacheManager({ 
  clearOnMount = false, 
  addMetaTags = true,
  updateSW = true 
}: CacheManagerProps) {
  
  useEffect(() => {
    // Add no-cache meta tags for dynamic pages
    if (addMetaTags) {
      addNoCacheMetaTags();
    }
    
    // Update service worker to get latest version
    if (updateSW) {
      updateServiceWorker().catch(console.error);
    }
    
    // Clear caches if requested
    if (clearOnMount) {
      clearBrowserCaches().catch(console.error);
    }
    
    // Add event listener for manual cache clearing
    const handleCacheClear = () => {
      clearBrowserCaches().then(() => {
        updateServiceWorker().then(() => {
          window.location.reload();
        });
      });
    };
    
    // Listen for custom cache clear events
    window.addEventListener('clearCaches', handleCacheClear);
    
    return () => {
      window.removeEventListener('clearCaches', handleCacheClear);
    };
  }, [clearOnMount, addMetaTags, updateSW]);
  
  return null; // This component doesn't render anything
}

/**
 * Hook for manual cache management
 */
export function useCacheManager() {
  const clearAllCaches = async () => {
    try {
      await clearBrowserCaches();
      await updateServiceWorker();
      window.location.reload();
    } catch (error) {
      console.error('Error clearing caches:', error);
    }
  };
  
  const forceRefresh = () => {
    // Dispatch custom event to trigger cache clearing
    window.dispatchEvent(new CustomEvent('clearCaches'));
  };
  
  return {
    clearAllCaches,
    forceRefresh
  };
}
