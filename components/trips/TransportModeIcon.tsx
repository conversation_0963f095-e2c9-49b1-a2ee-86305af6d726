'use client'

import { Train, Bus, Car, Plane } from 'lucide-react'
import { TransportMode } from '@/types/trip'

interface TransportModeIconProps {
  mode: TransportMode | null | undefined
  className?: string
}

export function TransportModeIcon({ mode, className = "w-5 h-5" }: TransportModeIconProps) {
  if (!mode) return null

  const iconProps = {
    className: `${className} text-gray-600`,
    'aria-label': `Transport mode: ${mode}`
  }

  switch (mode) {
    case 'train':
      return <Train {...iconProps} />
    case 'bus':
      return <Bus {...iconProps} />
    case 'car':
      return <Car {...iconProps} />
    case 'flight':
      return <Plane {...iconProps} />
    default:
      return null
  }
}

export function getTransportModeLabel(mode: TransportMode | null | undefined): string {
  if (!mode) return ''
  
  switch (mode) {
    case 'train':
      return 'Train'
    case 'bus':
      return 'Bus'
    case 'car':
      return 'Car'
    case 'flight':
      return 'Flight'
    default:
      return ''
  }
}
