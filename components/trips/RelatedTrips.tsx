'use client'

import React from 'react'
import { useRouter } from 'next/navigation'
import Image from 'next/image'
import Link from 'next/link'
import { motion } from 'framer-motion'
import { Clock, Users } from 'lucide-react'
import Button from '@/components/ui/Button'

interface RelatedTrip {
  id: string
  title: string
  slug: string
  description?: string
  days: number
  nights: number
  price_per_person: number
  featured_image_url: string
}

interface RelatedTripsProps {
  trips: RelatedTrip[]
  currentTripId?: string
}

// Sample data for development/preview
const sampleTrips: RelatedTrip[] = [
  {
    id: 'rishikesh-students-tour',
    title: 'Rishikesh Adventure Tour',
    slug: 'rishikesh-adventure-tour',
    description: 'Discover the beauty of Rishikesh',
    days: 7,
    nights: 6,
    price_per_person: 22500,
    featured_image_url: 'https://positive7.in/wp-content/uploads/2022/09/dusk-time-rishikesh-holy-town-travel-destination-india-1024x684.jpg'
  },
  {
    id: 'tirthan-valley-jibhi',
    title: 'Tirthan Valley & Jibhi',
    slug: 'tirthan-valley-jibhi',
    description: 'Explore the serene beauty of Tirthan Valley',
    days: 9,
    nights: 8,
    price_per_person: 29500,
    featured_image_url: 'https://positive7.in/wp-content/uploads/2024/11/TIRTHAN-VALLEY-JIBHI-1024x697.webp'
  },
  {
    id: 'dharamshala-tour',
    title: 'Dharamshala Cultural Tour',
    slug: 'dharamshala-cultural-tour',
    description: 'Experience the rich culture of Dharamshala',
    days: 10,
    nights: 9,
    price_per_person: 32500,
    featured_image_url: 'https://positive7.in/wp-content/uploads/2024/11/AMRITSAR-DHARAMSHALA-MCLEODGANJ-TRIUND-DALHOUSIE.webp'
  },
  {
    id: 'brigu-lake-trek',
    title: 'Brigu Lake Trek',
    slug: 'brigu-lake-trek',
    description: 'Challenge yourself with the rugged beauty of Brigu Lake',
    days: 9,
    nights: 8,
    price_per_person: 31500,
    featured_image_url: 'https://positive7.in/wp-content/uploads/2024/11/BRIGU-LAKE2.webp'
  }
]

export default function RelatedTrips({ trips = sampleTrips, currentTripId }: RelatedTripsProps) {
  const router = useRouter()
  
  // Filter out current trip if currentTripId is provided
  const filteredTrips = currentTripId 
    ? trips.filter(trip => trip.id !== currentTripId)
    : trips
  
  // Show max 3 trips
  const displayTrips = filteredTrips.slice(0, 3)
  
  const containerVariants = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        staggerChildren: 0.1
      }
    }
  }

  const itemVariants = {
    hidden: { opacity: 0, y: 20 },
    visible: { opacity: 1, y: 0 }
  }

  if (displayTrips.length === 0) return null

  return (
    <section className="py-16 bg-gray-50">
      <div className="container-custom">
        <motion.div
          variants={containerVariants}
          initial="hidden"
          animate="visible"
          className="space-y-12"
        >
          <motion.div variants={itemVariants} className="text-center">
            <h2 className="text-3xl font-bold text-gray-900 mb-4">Similar <span className="text-gradient">Trips</span></h2>
            <p className="text-gray-600 mb-8">
              Discover more educational adventures that might interest you
            </p>
          </motion.div>

          <motion.div variants={itemVariants} className="grid md:grid-cols-2 lg:grid-cols-3 gap-8">
            {displayTrips.map((trip) => (
              <Link key={trip.id} href={`/trips/${trip.slug}`} className="block group">
                <div className="bg-white rounded-xl overflow-hidden shadow-md hover:shadow-xl transition-all duration-300 h-full">
                  {/* Trip Image */}
                  <div className="relative h-48 overflow-hidden">
                    <Image
                      src={trip.featured_image_url}
                      alt={trip.title}
                      fill
                      className="object-cover transition-transform duration-300 group-hover:scale-105"
                    />
                  </div>

                  {/* Trip Content */}
                  <div className="p-4">
                    <h3 className="text-lg font-semibold text-gray-900 mb-1 hover:text-blue-600 transition-colors">
                      {trip.title}
                    </h3>
                    <div className="flex items-center gap-4 mb-2 text-sm text-gray-600">
                      <div className="flex items-center gap-1">
                        <Clock className="w-4 h-4" />
                        <span>{trip.days} Days, {trip.nights} Nights</span>
                      </div>
                      <div className="flex items-center gap-1">
                        <Users className="w-4 h-4" />
                        <span>Educational Tour</span>
                      </div>
                    </div>
                    <p className="text-gray-600 text-sm mb-2 line-clamp-2">
                      {trip.description || 'Discover amazing educational experiences.'}
                    </p>
                    <div className="flex justify-between items-center">
                      <span className="text-sm text-gray-500">{trip.days} Days, {trip.nights} Nights</span>
                      <span className="font-semibold text-primary-600">₹{trip.price_per_person.toLocaleString()}</span>
                    </div>
                  </div>
                </div>
              </Link>
            ))}
          </motion.div>
        </motion.div>
      </div>
    </section>
  )
}
