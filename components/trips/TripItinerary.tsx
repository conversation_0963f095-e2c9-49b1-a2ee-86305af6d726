'use client'

import { useState } from 'react'
import { motion, AnimatePresence } from 'framer-motion'
import { ChevronDown, ChevronUp, MapPin, Clock, Utensils } from 'lucide-react'

interface ItineraryDay {
  day: number
  title: string
  subheading?: string
  description: string
  activities: string[]
  meals: string[]
}

interface TripItineraryProps {
  itinerary: ItineraryDay[]
}

export function TripItinerary({ itinerary }: TripItineraryProps) {
  const [expandedDays, setExpandedDays] = useState<Set<number>>(new Set([1]))

  const toggleDay = (day: number) => {
    const newExpanded = new Set(expandedDays)
    if (newExpanded.has(day)) {
      newExpanded.delete(day)
    } else {
      newExpanded.add(day)
    }
    setExpandedDays(newExpanded)
  }

  const containerVariants = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        staggerChildren: 0.1
      }
    }
  }

  const itemVariants = {
    hidden: { opacity: 0, y: 20 },
    visible: { opacity: 1, y: 0 }
  }

  return (
    <div className="bg-white rounded-2xl p-8 shadow-lg">
      <h2 className="text-3xl font-bold mb-8 text-gray-900">Day-by-Day Itinerary</h2>
      
      <motion.div
        variants={containerVariants}
        initial="hidden"
        animate="visible"
        className="space-y-4"
      >
        {itinerary.map((day, index) => (
          <motion.div
            key={day.day}
            variants={itemVariants}
            className="border border-gray-200 rounded-xl overflow-hidden"
          >
            {/* Day Header */}
            <button
              onClick={() => toggleDay(day.day)}
              className="w-full p-6 text-left bg-gradient-to-r from-blue-50 to-green-50 hover:from-blue-100 hover:to-green-100 transition-colors duration-200"
            >
              <div className="flex items-center justify-between">
                <div className="flex items-center gap-4">
                  <div className="w-12 h-12 bg-gradient-to-r from-blue-600 to-green-600 rounded-full flex items-center justify-center text-white font-bold">
                    {day.day}
                  </div>
                  <div>
                    <h3 className="text-xl font-semibold text-gray-900">{day.title}</h3>
                    <p className="text-gray-600 mt-1">{day.subheading || 'Click to expand details'}</p>
                  </div>
                </div>
                <div className="flex-shrink-0">
                  {expandedDays.has(day.day) ? (
                    <ChevronUp className="w-6 h-6 text-gray-400" />
                  ) : (
                    <ChevronDown className="w-6 h-6 text-gray-400" />
                  )}
                </div>
              </div>
            </button>

            {/* Day Content */}
            <AnimatePresence>
              {expandedDays.has(day.day) && (
                <motion.div
                  initial={{ height: 0, opacity: 0 }}
                  animate={{ height: 'auto', opacity: 1 }}
                  exit={{ height: 0, opacity: 0 }}
                  transition={{ duration: 0.3 }}
                  className="overflow-hidden"
                >
                  <div className="p-6 pt-0">
                    <div className="grid md:grid-cols-2 gap-6">
                      {/* Activities */}
                      <div>
                        <h4 className="text-lg font-semibold mb-3 text-gray-900 flex items-center gap-2">
                          <MapPin className="w-5 h-5 text-blue-600" />
                          Activities
                        </h4>
                        <ul className="space-y-2">
                          {day.activities.map((activity, actIndex) => (
                            <li key={actIndex} className="flex items-start gap-3">
                              <div className="w-2 h-2 bg-blue-600 rounded-full mt-2 flex-shrink-0" />
                              <span className="text-gray-700">{activity}</span>
                            </li>
                          ))}
                        </ul>
                      </div>

                      {/* Meals */}
                      <div>
                        <h4 className="text-lg font-semibold mb-3 text-gray-900 flex items-center gap-2">
                          <Utensils className="w-5 h-5 text-green-600" />
                          Meals Included
                        </h4>
                        <div className="flex flex-wrap gap-2">
                          {day.meals.map((meal, mealIndex) => (
                            <span
                              key={mealIndex}
                              className="px-3 py-1 bg-green-100 text-green-800 rounded-full text-sm font-medium"
                            >
                              {meal}
                            </span>
                          ))}
                        </div>
                      </div>
                    </div>
                  </div>
                </motion.div>
              )}
            </AnimatePresence>
          </motion.div>
        ))}
      </motion.div>

      {/* Timeline Summary */}
      <div className="mt-8 p-6 bg-gradient-to-r from-blue-50 to-green-50 rounded-xl">
        <h3 className="text-lg font-semibold mb-4 text-gray-900 flex items-center gap-2">
          <Clock className="w-5 h-5 text-blue-600" />
          Trip Timeline
        </h3>
        <div className="grid grid-cols-2 md:grid-cols-4 gap-4 text-center">
          <div>
            <div className="text-2xl font-bold text-blue-600">{itinerary.length}</div>
            <div className="text-sm text-gray-600">Total Days</div>
          </div>
          <div>
            <div className="text-2xl font-bold text-green-600">
              {itinerary.reduce((acc, day) => acc + day.activities.length, 0)}
            </div>
            <div className="text-sm text-gray-600">Activities</div>
          </div>
          <div>
            <div className="text-2xl font-bold text-purple-600">
              {new Set(itinerary.flatMap(day => day.meals)).size}
            </div>
            <div className="text-sm text-gray-600">Meal Types</div>
          </div>
          <div>
            <div className="text-2xl font-bold text-orange-600">
              {itinerary.filter(day => day.activities.some(activity => 
                activity.toLowerCase().includes('trek') || 
                activity.toLowerCase().includes('adventure')
              )).length}
            </div>
            <div className="text-sm text-gray-600">Adventure Days</div>
          </div>
        </div>
      </div>
    </div>
  )
}
