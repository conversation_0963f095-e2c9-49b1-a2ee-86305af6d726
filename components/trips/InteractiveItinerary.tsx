'use client'

import React, { useState } from 'react'
import { motion, AnimatePresence } from 'framer-motion'
import { 
  ChevronDown, 
  ChevronUp, 
  MapPin, 
  Clock, 
  Utensils, 
  Camera,
  Mountain,
  Car,
  Plane,
  Train,
  Calendar,
  Star,
  Info
} from 'lucide-react'
import Image from 'next/image'

interface ItineraryDay {
  day: number
  title: string
  subheading?: string
  description: string
  activities: Array<{
    time: string
    title: string
    description: string
    location?: string
    type: 'travel' | 'activity' | 'meal' | 'accommodation' | 'sightseeing'
    duration?: string
    difficulty?: 'Easy' | 'Moderate' | 'Challenging'
    images?: string[]
  }>
  meals: string[]
  accommodation?: string
  travelMode?: 'train' | 'bus' | 'flight' | 'walking'
  highlights: string[]
}

interface InteractiveItineraryProps {
  itinerary: ItineraryDay[]
  tripTitle: string
}

const activityIcons = {
  travel: Car,
  activity: Mountain,
  meal: Utensils,
  accommodation: MapPin,
  sightseeing: Camera
}

const travelIcons = {
  train: Train,
  bus: Car,
  flight: Plane,
  walking: MapPin
}

export function InteractiveItinerary({ itinerary, tripTitle }: InteractiveItineraryProps) {
  const [expandedDays, setExpandedDays] = useState<Set<number>>(new Set([1]))
  const [selectedActivity, setSelectedActivity] = useState<any>(null)

  const toggleDay = (day: number) => {
    const newExpanded = new Set(expandedDays)
    if (newExpanded.has(day)) {
      newExpanded.delete(day)
    } else {
      newExpanded.add(day)
    }
    setExpandedDays(newExpanded)
  }

  const expandAll = () => {
    setExpandedDays(new Set(itinerary.map(day => day.day)))
  }

  const collapseAll = () => {
    setExpandedDays(new Set())
  }

  const getDifficultyColor = (difficulty?: string) => {
    switch (difficulty) {
      case 'Easy': return 'text-green-600 bg-green-100'
      case 'Moderate': return 'text-yellow-600 bg-yellow-100'
      case 'Challenging': return 'text-red-600 bg-red-100'
      default: return 'text-gray-600 bg-gray-100'
    }
  }

  const containerVariants = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        staggerChildren: 0.1
      }
    }
  }

  const itemVariants = {
    hidden: { opacity: 0, y: 20 },
    visible: { opacity: 1, y: 0 }
  }

  return (
    <div className="bg-white rounded-2xl p-8 shadow-lg">
      {/* Header */}
      <div className="flex items-center justify-between mb-8">
        <div>
          <h2 className="text-3xl font-bold text-gray-900 mb-2">Interactive Itinerary</h2>
          <p className="text-gray-600">{tripTitle} - Day by Day Journey</p>
        </div>
        <div className="flex gap-2">
          <button
            onClick={expandAll}
            className="px-4 py-2 text-sm bg-blue-100 text-blue-700 rounded-lg hover:bg-blue-200 transition-colors"
          >
            Expand All
          </button>
          <button
            onClick={collapseAll}
            className="px-4 py-2 text-sm bg-gray-100 text-gray-700 rounded-lg hover:bg-gray-200 transition-colors"
          >
            Collapse All
          </button>
        </div>
      </div>

      {/* Timeline */}
      <motion.div
        variants={containerVariants}
        initial="hidden"
        animate="visible"
        className="relative"
      >
        {/* Timeline Line */}
        <div className="absolute left-8 top-0 bottom-0 w-0.5 bg-gradient-to-b from-blue-500 to-green-500" />

        {itinerary.map((day, index) => (
          <motion.div
            key={day.day}
            variants={itemVariants}
            className="relative mb-8 last:mb-0"
          >
            {/* Day Marker */}
            <div className="absolute left-6 w-4 h-4 bg-gradient-to-r from-blue-600 to-green-600 rounded-full border-4 border-white shadow-lg z-10" />

            {/* Day Card */}
            <div className="ml-16 bg-white border border-gray-200 rounded-xl overflow-hidden shadow-sm hover:shadow-md transition-shadow">
              {/* Day Header */}
              <button
                onClick={() => toggleDay(day.day)}
                className="w-full p-6 text-left bg-gradient-to-r from-blue-50 to-green-50 hover:from-blue-100 hover:to-green-100 transition-colors"
              >
                <div className="flex items-center justify-between">
                  <div className="flex items-center gap-4">
                    <div className="w-12 h-12 bg-gradient-to-r from-blue-600 to-green-600 rounded-full flex items-center justify-center text-white font-bold text-lg">
                      {day.day}
                    </div>
                    <div>
                      <h3 className="text-xl font-semibold text-gray-900">{day.title}</h3>
                      <p className="text-gray-600 mt-1">{day.subheading || 'Click to expand details'}</p>
                      <div className="flex items-center gap-4 mt-2 text-sm text-gray-500">
                        <span className="flex items-center gap-1">
                          <Clock className="w-4 h-4" />
                          {day.activities.length} activities
                        </span>
                        {day.travelMode && (
                          <span className="flex items-center gap-1">
                            {travelIcons[day.travelMode] && 
                              React.createElement(travelIcons[day.travelMode], { className: "w-4 h-4" })
                            }
                            Travel day
                          </span>
                        )}
                        <span className="flex items-center gap-1">
                          <Utensils className="w-4 h-4" />
                          {day.meals.length} meals
                        </span>
                      </div>
                    </div>
                  </div>
                  <div className="flex items-center gap-4">
                    {/* Highlights Preview */}
                    <div className="hidden md:flex gap-2">
                      {day.highlights.slice(0, 2).map((highlight, idx) => (
                        <span
                          key={idx}
                          className="px-2 py-1 bg-blue-100 text-blue-700 text-xs rounded-full"
                        >
                          {highlight}
                        </span>
                      ))}
                      {day.highlights.length > 2 && (
                        <span className="px-2 py-1 bg-gray-100 text-gray-600 text-xs rounded-full">
                          +{day.highlights.length - 2} more
                        </span>
                      )}
                    </div>
                    <div className="flex-shrink-0">
                      {expandedDays.has(day.day) ? (
                        <ChevronUp className="w-6 h-6 text-gray-400" />
                      ) : (
                        <ChevronDown className="w-6 h-6 text-gray-400" />
                      )}
                    </div>
                  </div>
                </div>
              </button>

              {/* Day Content */}
              <AnimatePresence>
                {expandedDays.has(day.day) && (
                  <motion.div
                    initial={{ height: 0, opacity: 0 }}
                    animate={{ height: 'auto', opacity: 1 }}
                    exit={{ height: 0, opacity: 0 }}
                    transition={{ duration: 0.3 }}
                    className="overflow-hidden"
                  >
                    <div className="p-6 pt-0 space-y-6">
                      {/* Activities Timeline */}
                      <div>
                        <h4 className="text-lg font-semibold mb-4 text-gray-900 flex items-center gap-2">
                          <Calendar className="w-5 h-5 text-blue-600" />
                          Daily Schedule
                        </h4>
                        <div className="space-y-4">
                          {day.activities.map((activity, actIndex) => {
                            const ActivityIcon = activityIcons[activity.type]
                            return (
                              <div
                                key={actIndex}
                                className="flex gap-4 p-4 bg-gray-50 rounded-lg hover:bg-gray-100 transition-colors cursor-pointer"
                                onClick={() => setSelectedActivity(activity)}
                              >
                                <div className="flex-shrink-0">
                                  <div className="w-10 h-10 bg-white rounded-full flex items-center justify-center shadow-sm">
                                    <ActivityIcon className="w-5 h-5 text-blue-600" />
                                  </div>
                                </div>
                                <div className="flex-1">
                                  <div className="flex items-center gap-3 mb-1">
                                    <span className="text-sm font-medium text-blue-600">{activity.time}</span>
                                    {activity.duration && (
                                      <span className="text-xs text-gray-500">({activity.duration})</span>
                                    )}
                                    {activity.difficulty && (
                                      <span className={`text-xs px-2 py-1 rounded-full ${getDifficultyColor(activity.difficulty)}`}>
                                        {activity.difficulty}
                                      </span>
                                    )}
                                  </div>
                                  <h5 className="font-medium text-gray-900">{activity.title}</h5>
                                  <p className="text-sm text-gray-600 mt-1">{activity.description}</p>
                                  {activity.location && (
                                    <div className="flex items-center gap-1 mt-2 text-xs text-gray-500">
                                      <MapPin className="w-3 h-3" />
                                      <span>{activity.location}</span>
                                    </div>
                                  )}
                                </div>
                                {activity.images && activity.images.length > 0 && (
                                  <div className="flex-shrink-0">
                                    <div className="w-16 h-16 relative rounded-lg overflow-hidden">
                                      <Image
                                        src={activity.images[0]}
                                        alt={activity.title}
                                        fill
                                        className="object-cover"
                                      />
                                    </div>
                                  </div>
                                )}
                              </div>
                            )
                          })}
                        </div>
                      </div>

                      {/* Day Summary */}
                      <div className="grid md:grid-cols-3 gap-4 pt-4 border-t border-gray-200">
                        {/* Meals */}
                        <div>
                          <h5 className="font-medium text-gray-900 mb-2 flex items-center gap-2">
                            <Utensils className="w-4 h-4 text-green-600" />
                            Meals
                          </h5>
                          <div className="flex flex-wrap gap-1">
                            {day.meals.map((meal, mealIndex) => (
                              <span
                                key={mealIndex}
                                className="px-2 py-1 bg-green-100 text-green-800 rounded-full text-xs"
                              >
                                {meal}
                              </span>
                            ))}
                          </div>
                        </div>

                        {/* Accommodation */}
                        {day.accommodation && (
                          <div>
                            <h5 className="font-medium text-gray-900 mb-2 flex items-center gap-2">
                              <MapPin className="w-4 h-4 text-purple-600" />
                              Stay
                            </h5>
                            <p className="text-sm text-gray-600">{day.accommodation}</p>
                          </div>
                        )}

                        {/* Highlights */}
                        <div>
                          <h5 className="font-medium text-gray-900 mb-2 flex items-center gap-2">
                            <Star className="w-4 h-4 text-yellow-600" />
                            Highlights
                          </h5>
                          <div className="flex flex-wrap gap-1">
                            {day.highlights.map((highlight, highlightIndex) => (
                              <span
                                key={highlightIndex}
                                className="px-2 py-1 bg-yellow-100 text-yellow-800 rounded-full text-xs"
                              >
                                {highlight}
                              </span>
                            ))}
                          </div>
                        </div>
                      </div>
                    </div>
                  </motion.div>
                )}
              </AnimatePresence>
            </div>
          </motion.div>
        ))}
      </motion.div>

      {/* Activity Detail Modal */}
      {selectedActivity && (
        <motion.div
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          className="fixed inset-0 z-50 bg-black/50 flex items-center justify-center p-4"
          onClick={() => setSelectedActivity(null)}
        >
          <motion.div
            initial={{ scale: 0.9, opacity: 0 }}
            animate={{ scale: 1, opacity: 1 }}
            className="bg-white rounded-2xl p-6 max-w-2xl w-full max-h-[80vh] overflow-y-auto"
            onClick={(e) => e.stopPropagation()}
          >
            <div className="flex items-center justify-between mb-4">
              <h3 className="text-xl font-bold text-gray-900">{selectedActivity.title}</h3>
              <button
                onClick={() => setSelectedActivity(null)}
                className="text-gray-400 hover:text-gray-600"
              >
                ×
              </button>
            </div>
            
            {selectedActivity.images && selectedActivity.images.length > 0 && (
              <div className="mb-4">
                <div className="grid grid-cols-2 gap-2">
                  {selectedActivity.images.slice(0, 4).map((image: string, index: number) => (
                    <div key={index} className="relative h-32 rounded-lg overflow-hidden">
                      <Image
                        src={image}
                        alt={`${selectedActivity.title} - ${index + 1}`}
                        fill
                        className="object-cover"
                      />
                    </div>
                  ))}
                </div>
              </div>
            )}
            
            <div className="space-y-4">
              <p className="text-gray-700">{selectedActivity.description}</p>
              
              <div className="grid grid-cols-2 gap-4 text-sm">
                <div>
                  <span className="font-medium text-gray-900">Time:</span>
                  <span className="ml-2 text-gray-600">{selectedActivity.time}</span>
                </div>
                {selectedActivity.duration && (
                  <div>
                    <span className="font-medium text-gray-900">Duration:</span>
                    <span className="ml-2 text-gray-600">{selectedActivity.duration}</span>
                  </div>
                )}
                {selectedActivity.location && (
                  <div className="col-span-2">
                    <span className="font-medium text-gray-900">Location:</span>
                    <span className="ml-2 text-gray-600">{selectedActivity.location}</span>
                  </div>
                )}
                {selectedActivity.difficulty && (
                  <div>
                    <span className="font-medium text-gray-900">Difficulty:</span>
                    <span className={`ml-2 px-2 py-1 rounded-full text-xs ${getDifficultyColor(selectedActivity.difficulty)}`}>
                      {selectedActivity.difficulty}
                    </span>
                  </div>
                )}
              </div>
            </div>
          </motion.div>
        </motion.div>
      )}
    </div>
  )
}
