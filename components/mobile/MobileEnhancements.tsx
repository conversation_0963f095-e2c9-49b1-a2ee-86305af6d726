'use client';

import React, { useState, useEffect, useRef } from 'react';
import { motion, PanInfo, useMotionValue, useTransform } from 'framer-motion';
import { RefreshCw, Vibrate } from 'lucide-react';

// Pull-to-refresh component
interface PullToRefreshProps {
  onRefresh: () => Promise<void>;
  children: React.ReactNode;
  threshold?: number;
  disabled?: boolean;
}

export function PullToRefresh({ 
  onRefresh, 
  children, 
  threshold = 80,
  disabled = false 
}: PullToRefreshProps) {
  const [isRefreshing, setIsRefreshing] = useState(false);
  const [isPulling, setIsPulling] = useState(false);
  const containerRef = useRef<HTMLDivElement>(null);
  const y = useMotionValue(0);
  const rotate = useTransform(y, [0, threshold], [0, 180]);
  const opacity = useTransform(y, [0, threshold], [0.6, 1]);

  const handlePanStart = () => {
    if (disabled || isRefreshing) return;
    
    // Only allow pull-to-refresh when at the top of the page
    if (window.scrollY === 0) {
      setIsPulling(true);
    }
  };

  const handlePan = (event: any, info: PanInfo) => {
    if (disabled || isRefreshing || !isPulling) return;
    
    if (info.offset.y > 0) {
      y.set(Math.min(info.offset.y, threshold * 1.5));
    }
  };

  const handlePanEnd = async (event: any, info: PanInfo) => {
    if (disabled || isRefreshing || !isPulling) return;
    
    setIsPulling(false);
    
    if (info.offset.y >= threshold) {
      setIsRefreshing(true);
      
      // Haptic feedback on supported devices
      if ('vibrate' in navigator) {
        navigator.vibrate(50);
      }
      
      try {
        await onRefresh();
      } finally {
        setIsRefreshing(false);
        y.set(0);
      }
    } else {
      y.set(0);
    }
  };

  return (
    <div ref={containerRef} className="relative overflow-hidden">
      {/* Pull indicator */}
      <motion.div
        className="absolute top-0 left-0 right-0 flex items-center justify-center py-4 bg-gradient-to-b from-blue-50 to-transparent z-10"
        style={{ 
          y: useTransform(y, [0, threshold], [-60, 0]),
          opacity 
        }}
      >
        <motion.div
          className="flex items-center gap-2 text-blue-600"
          style={{ rotate }}
        >
          <RefreshCw className={`w-5 h-5 ${isRefreshing ? 'animate-spin' : ''}`} />
          <span className="text-sm font-medium">
            {isRefreshing ? 'Refreshing...' : isPulling ? 'Release to refresh' : 'Pull to refresh'}
          </span>
        </motion.div>
      </motion.div>

      {/* Content */}
      <motion.div
        style={{ y }}
        onPanStart={handlePanStart}
        onPan={handlePan}
        onPanEnd={handlePanEnd}
        drag="y"
        dragConstraints={{ top: 0, bottom: 0 }}
        dragElastic={{ top: 0.3, bottom: 0 }}
      >
        {children}
      </motion.div>
    </div>
  );
}

// Swipe gesture component for image carousels
interface SwipeableProps {
  children: React.ReactNode;
  onSwipeLeft?: () => void;
  onSwipeRight?: () => void;
  threshold?: number;
  className?: string;
}

export function Swipeable({ 
  children, 
  onSwipeLeft, 
  onSwipeRight, 
  threshold = 50,
  className = '' 
}: SwipeableProps) {
  const handlePanEnd = (event: any, info: PanInfo) => {
    const { offset, velocity } = info;
    
    // Determine swipe direction based on offset and velocity
    if (Math.abs(offset.x) > threshold || Math.abs(velocity.x) > 500) {
      if (offset.x > 0 && onSwipeRight) {
        // Haptic feedback
        if ('vibrate' in navigator) {
          navigator.vibrate(30);
        }
        onSwipeRight();
      } else if (offset.x < 0 && onSwipeLeft) {
        // Haptic feedback
        if ('vibrate' in navigator) {
          navigator.vibrate(30);
        }
        onSwipeLeft();
      }
    }
  };

  return (
    <motion.div
      className={className}
      drag="x"
      dragConstraints={{ left: 0, right: 0 }}
      dragElastic={0.2}
      onPanEnd={handlePanEnd}
      whileDrag={{ scale: 0.95 }}
    >
      {children}
    </motion.div>
  );
}

// Haptic feedback utilities
export const hapticFeedback = {
  light: () => {
    if ('vibrate' in navigator) {
      navigator.vibrate(10);
    }
  },
  
  medium: () => {
    if ('vibrate' in navigator) {
      navigator.vibrate(50);
    }
  },
  
  heavy: () => {
    if ('vibrate' in navigator) {
      navigator.vibrate([100, 30, 100]);
    }
  },
  
  success: () => {
    if ('vibrate' in navigator) {
      navigator.vibrate([50, 25, 50]);
    }
  },
  
  error: () => {
    if ('vibrate' in navigator) {
      navigator.vibrate([100, 50, 100, 50, 100]);
    }
  }
};



// Mobile-optimized touch target component
interface TouchTargetProps {
  children: React.ReactNode;
  onClick?: () => void;
  className?: string;
  haptic?: 'light' | 'medium' | 'heavy';
}

export function TouchTarget({ 
  children, 
  onClick, 
  className = '',
  haptic = 'light' 
}: TouchTargetProps) {
  const handleClick = () => {
    hapticFeedback[haptic]();
    onClick?.();
  };

  return (
    <motion.button
      className={`mobile-touch-target ${className}`}
      onClick={handleClick}
      whileTap={{ scale: 0.95 }}
      transition={{ type: 'spring', stiffness: 400, damping: 25 }}
    >
      {children}
    </motion.button>
  );
}
