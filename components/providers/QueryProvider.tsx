'use client';

import { QueryClient, QueryClientProvider, QueryCache, MutationCache } from '@tanstack/react-query';
import { useState, ReactNode } from 'react';

// Utility function to sanitize data for logging
function sanitizeForLogging(data: unknown): unknown {
  if (typeof data !== 'object' || data === null) {
    return data;
  }

  if (Array.isArray(data)) {
    return data.map(item => sanitizeForLogging(item));
  }

  const sanitized: Record<string, unknown> = {};
  const sensitiveKeys = ['password', 'token', 'key', 'secret', 'auth', 'credential', 'email', 'phone'];

  for (const [key, value] of Object.entries(data as Record<string, unknown>)) {
    const lowerKey = key.toLowerCase();
    const isSensitive = sensitiveKeys.some(sensitiveKey => lowerKey.includes(sensitiveKey));

    if (isSensitive) {
      sanitized[key] = '[REDACTED]';
    } else if (typeof value === 'object' && value !== null) {
      sanitized[key] = sanitizeForLogging(value);
    } else {
      sanitized[key] = value;
    }
  }

  return sanitized;
}

interface QueryProviderProps {
  children: ReactNode;
}

export default function QueryProvider({ children }: QueryProviderProps) {
  const [queryClient] = useState(
    () => {
      try {
        return new QueryClient({
          defaultOptions: {
            queries: {
              // Conservative stale time for admin data freshness
              staleTime: 2 * 60 * 1000, // 2 minutes - balance between freshness and performance
              gcTime: 15 * 60 * 1000, // 15 minutes - longer cache retention for better UX
              retry: (failureCount, error: unknown) => {
                // Don't retry on 4xx errors (client errors)
                const errorWithStatus = error as { status?: number };
                if (errorWithStatus?.status && errorWithStatus.status >= 400 && errorWithStatus.status < 500) {
                  return false;
                }
                // Retry up to 2 times for server errors and network issues
                return failureCount < 2;
              },
              refetchOnWindowFocus: false, // Disable for admin panels - use manual refresh instead
              refetchOnMount: true,
              refetchInterval: false, // Disable automatic polling - use manual refresh for admin data
              refetchOnReconnect: true, // Refetch when network reconnects
              // Disable background refetching for admin panels
              refetchIntervalInBackground: false,
              // Exponential backoff for retry delays
              retryDelay: (attemptIndex) => Math.min(1000 * 2 ** attemptIndex, 30000),
            },
            mutations: {
              retry: false,
              // Add global mutation settings for better UX
              onError: (error) => {
                if (process.env.NODE_ENV === 'development') {
                  // Only log error message and type, not full error object which may contain sensitive data
                  console.error('Mutation error:', {
                    message: error instanceof Error ? error.message : 'Unknown error',
                    name: error instanceof Error ? error.name : 'Error'
                  });
                }
              },
            },
          },
          // Add query cache configuration for better memory management
          queryCache: new QueryCache({
            onError: (error, query) => {
              if (process.env.NODE_ENV === 'development') {
                // Sanitize query key to avoid logging sensitive data
                const sanitizedQueryKey = sanitizeForLogging(query.queryKey);
                console.error('Query error:', {
                  message: error instanceof Error ? error.message : 'Unknown error',
                  name: error instanceof Error ? error.name : 'Error',
                  queryKey: sanitizedQueryKey
                });
              }
            },
          }),
          // Add mutation cache for better error handling
          mutationCache: new MutationCache({
            onError: (error, variables, _context, _mutation) => {
              if (process.env.NODE_ENV === 'development') {
                // Sanitize variables to avoid logging sensitive data
                const sanitizedVariables = sanitizeForLogging(variables);
                console.error('Mutation cache error:', {
                  message: error instanceof Error ? error.message : 'Unknown error',
                  name: error instanceof Error ? error.name : 'Error',
                  variables: sanitizedVariables
                });
              }
            },
          }),
        });
      } catch (error) {
        if (process.env.NODE_ENV === 'development') {
          console.error('Failed to create QueryClient:', error);
        }
        // Return a basic QueryClient as fallback
        return new QueryClient();
      }
    }
  );

  if (!queryClient) {
    if (process.env.NODE_ENV === 'development') {
      console.error('QueryClient is undefined');
    }
    return <>{children}</>;
  }

  return (
    <QueryClientProvider client={queryClient}>
      {children}
    </QueryClientProvider>
  );
}
