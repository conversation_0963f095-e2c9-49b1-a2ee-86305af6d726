'use client';

import { createContext, useContext, useEffect, useState } from 'react';
import { User } from '@supabase/supabase-js';
import {
  createClient,
  hasPermission as checkPermission,
  hasRole as checkRole,
  isSuper<PERSON>dmin as checkSuperAdmin,
  isOwner as checkOwner,
  type AdminUser
} from '@/lib/auth';

interface AuthContextType {
  user: User | null;
  adminUser: AdminUser | null;
  loading: boolean;
  signIn: (email: string, password: string) => Promise<{ error?: string }>;
  signOut: () => Promise<void>;
  hasPermission: (resource: string, action: string) => boolean;
  hasRole: (roleName: string) => boolean;
  isSuperAdmin: () => boolean;
  isOwner: () => boolean;
}

const AuthContext = createContext<AuthContextType | undefined>(undefined);

export function useAuth() {
  const context = useContext(AuthContext);
  if (context === undefined) {
    throw new Error('useAuth must be used within an AuthProvider');
  }
  return context;
}

function useAuthState(): AuthContextType {
  const [user, setUser] = useState<User | null>(null);
  const [adminUser, setAdminUser] = useState<AdminUser | null>(null);
  const [loading, setLoading] = useState(true);
  const supabase = createClient();

  // Fetch admin user data via API
  const fetchAdminUser = async (userId: string) => {
    try {
      const response = await fetch('/api/admin/user');
      if (response.ok) {
        const data = await response.json();
        setAdminUser(data.user);
      } else {
        setAdminUser(null);
      }
    } catch (error) {
      console.error('Error fetching admin user:', error);
      setAdminUser(null);
    }
  };

  useEffect(() => {
    // Get initial session
    const getInitialSession = async () => {
      try {
        const { data: { session } } = await supabase.auth.getSession();
        setUser(session?.user ?? null);

        if (session?.user) {
          await fetchAdminUser(session.user.id);
        }

        setLoading(false);
      } catch (error) {
        console.error('Error getting initial session:', error);
        setLoading(false);
      }
    };

    getInitialSession();

    // Listen for auth changes
    const { data: { subscription } } = supabase.auth.onAuthStateChange(
      async (event, session) => {
        setUser(session?.user ?? null);

        if (session?.user) {
          await fetchAdminUser(session.user.id);
        } else {
          setAdminUser(null);
        }

        setLoading(false);
      }
    );

    return () => subscription.unsubscribe();
  }, []);

  const signIn = async (email: string, password: string) => {
    try {
      const { error } = await supabase.auth.signInWithPassword({
        email,
        password,
      });

      if (error) {
        return { error: error.message };
      }

      return {};
    } catch (error) {
      return { error: error instanceof Error ? error.message : 'An unexpected error occurred' };
    }
  };

  const signOut = async () => {
    await supabase.auth.signOut();
  };

  const hasPermission = (resource: string, action: string): boolean => {
    if (!adminUser) return false;
    return checkPermission(adminUser.roles, resource, action);
  };

  const hasRole = (roleName: string): boolean => {
    if (!adminUser) return false;
    return checkRole(adminUser.roles, roleName);
  };

  const isSuperAdmin = (): boolean => {
    if (!adminUser) return false;
    return checkSuperAdmin(adminUser.roles);
  };

  const isOwner = (): boolean => {
    if (!adminUser) return false;
    return checkOwner(adminUser.roles);
  };

  return {
    user,
    adminUser,
    loading,
    signIn,
    signOut,
    hasPermission,
    hasRole,
    isSuperAdmin,
    isOwner,
  };
}

export function AuthProvider({ children }: { children: React.ReactNode }) {
  const authState = useAuthState();

  return (
    <AuthContext.Provider value={authState}>
      {children}
    </AuthContext.Provider>
  );
}

export default function AuthProviderWrapper({ children }: { children: React.ReactNode }) {
  return <AuthProvider>{children}</AuthProvider>;
}
