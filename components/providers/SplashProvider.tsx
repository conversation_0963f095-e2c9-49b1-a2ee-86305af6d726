'use client';

import { createContext, useContext, useState, useEffect } from 'react';
import SplashScreen from '@/components/ui/SplashScreen';
import { usePathname } from 'next/navigation';

interface SplashContextType {
  showSplash: boolean;
  setShowSplash: (show: boolean) => void;
}

const SplashContext = createContext<SplashContextType | undefined>(undefined);

export function SplashProvider({ children }: { children: React.ReactNode }) {
  const [showSplash, setShowSplash] = useState(false);
  const [isFirstVisit, setIsFirstVisit] = useState(false);
  const [contentVisible, setContentVisible] = useState(false);
  const pathname = usePathname();
  
  useEffect(() => {
    // Only show splash screen on the home page
    const isHomePage = pathname === '/';
    
    if (isHomePage) {
      const hasVisitedBefore = localStorage.getItem('has_visited_home');
      
      if (!hasVisitedBefore) {
        setShowSplash(true);
        setIsFirstVisit(true);
        setContentVisible(false);
      } else {
        setShowSplash(false);
        setIsFirstVisit(false);
        setContentVisible(true);
      }
    } else {
      setShowSplash(false);
      setIsFirstVisit(false);
      setContentVisible(true);
    }
  }, [pathname]);

  // Function to handle splash completion
  const handleSplashComplete = () => {
    // Only update localStorage after splash has been shown
    localStorage.setItem('has_visited_home', 'true');
    setShowSplash(false);
    
    // Add a small delay before showing content for smoother transition
    setTimeout(() => {
      setContentVisible(true);
    }, 100);
  };

  return (
    <SplashContext.Provider value={{ showSplash, setShowSplash }}>
      {isFirstVisit && showSplash ? (
        <SplashScreen onComplete={handleSplashComplete} />
      ) : null}
      <div 
        className={`transition-opacity duration-1000 ${contentVisible ? "opacity-100" : "opacity-0"} ${isFirstVisit && showSplash ? "invisible" : "visible"}`}
      >
        {children}
      </div>
    </SplashContext.Provider>
  );
}

export function useSplash() {
  const context = useContext(SplashContext);
  if (context === undefined) {
    throw new Error('useSplash must be used within a SplashProvider');
  }
  return context;
} 