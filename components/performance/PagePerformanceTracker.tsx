'use client';

import { usePagePerformance } from '@/hooks/usePagePerformance';
import { usePathname } from 'next/navigation';

interface PagePerformanceTrackerProps {
  children: React.ReactNode;
}

export function PagePerformanceTracker({ children }: PagePerformanceTrackerProps) {
  const pathname = usePathname();

  // Track performance for all pages except admin pages (they have their own tracking)
  const shouldTrack = !pathname.startsWith('/admin');

  // Always call the hook, but pass null when we shouldn't track
  usePagePerformance(shouldTrack ? pathname : null);

  return <>{children}</>;
}
