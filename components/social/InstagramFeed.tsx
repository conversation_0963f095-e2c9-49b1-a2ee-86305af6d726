'use client';

import React from 'react';
import Image from 'next/image';
import { motion } from 'framer-motion';
import { Instagram, ExternalLink, Heart, MessageCircle, Share } from 'lucide-react';
import { useStandardQuery } from '@/hooks/useStandardQuery';

interface InstagramPost {
  id: string;
  caption: string;
  media_url: string;
  media_type: 'IMAGE' | 'VIDEO' | 'CAROUSEL_ALBUM';
  permalink: string;
  timestamp: string;
  like_count?: number;
  comments_count?: number;
}

interface InstagramFeedProps {
  className?: string;
  maxPosts?: number;
  showHeader?: boolean;
  compact?: boolean;
}

export default function InstagramFeed({ 
  className = '', 
  maxPosts = 6, 
  showHeader = true,
  compact = false
}: InstagramFeedProps) {
  // API function for fetching Instagram posts
  const fetchInstagramPosts = async (): Promise<InstagramPost[]> => {
    const response = await fetch('/api/instagram');

    if (!response.ok) {
      const errorData = await response.json().catch(() => ({}));
      if (errorData.disabled) {
        throw new Error('INSTAGRAM_DISABLED');
      }
      throw new Error('Instagram API error');
    }

    return response.json();
  };

  // Use modern React Query pattern
  const { data: allPosts = [], isLoading, error } = useStandardQuery(
    ['instagram-posts'],
    fetchInstagramPosts,
    {
      staleTime: 30 * 60 * 1000, // 30 minutes
      gcTime: 60 * 60 * 1000, // 1 hour
      showErrorToast: false, // Don't show error toast
      retry: false, // Don't retry
    }
  );

  // Check if Instagram is disabled
  const isInstagramDisabled = error?.message === 'INSTAGRAM_DISABLED';

  // If Instagram is disabled, don't render anything
  if (isInstagramDisabled) {
    return null;
  }

  // Slice posts to maxPosts
  const posts = allPosts.slice(0, maxPosts);




  const formatTimeAgo = (timestamp: string) => {
    const now = new Date();
    const postTime = new Date(timestamp);
    const diffInHours = Math.floor((now.getTime() - postTime.getTime()) / (1000 * 60 * 60));
    
    if (diffInHours < 24) {
      return `${diffInHours}h ago`;
    } else {
      const diffInDays = Math.floor(diffInHours / 24);
      return `${diffInDays}d ago`;
    }
  };

  const truncateCaption = (caption: string, maxLength: number = 100) => {
    if (caption.length <= maxLength) return caption;
    return caption.substring(0, maxLength) + '...';
  };

  if (isLoading) {
    return (
      <div className={`${className}`}>
        {showHeader && (
          <div className="flex items-center gap-3 mb-6">
            <div className="w-8 h-8 bg-gradient-to-r from-purple-500 to-pink-500 rounded-lg animate-pulse"></div>
            <div className="h-6 bg-gray-200 rounded w-48 animate-pulse"></div>
          </div>
        )}
        <div className={`grid ${compact ? 'grid-cols-3' : 'grid-cols-1 sm:grid-cols-2 lg:grid-cols-3'} gap-4`}>
          {Array.from({ length: maxPosts }).map((_, index) => (
            <div key={index} className="bg-gray-200 aspect-square rounded-xl animate-pulse"></div>
          ))}
        </div>
      </div>
    );
  }

  // If there's an error and no posts, don't render anything
  if (error && posts.length === 0) {
    return null;
  }

  return (
    <div className={className}>
      {showHeader && (
        <div className="flex items-center justify-between mb-6">
          <div className="flex items-center gap-3">
            <div className="p-2 bg-gradient-to-r from-purple-500 to-pink-500 rounded-lg">
              <Instagram className="w-6 h-6 text-white" />
            </div>
            <div>
              <h3 className="text-xl font-bold text-gray-900">Follow Our Journey</h3>
              <p className="text-gray-600">@positive.seven</p>
            </div>
          </div>
          <a
            href="https://instagram.com/positive.seven"
            target="_blank"
            rel="noopener noreferrer"
            className="flex items-center gap-2 px-4 py-2 bg-gradient-to-r from-purple-500 to-pink-500 text-white rounded-lg hover:from-purple-600 hover:to-pink-600 transition-all duration-300 text-sm font-medium"
          >
            Follow Us
            <ExternalLink className="w-4 h-4" />
          </a>
        </div>
      )}

      <div className={`grid gap-4 ${
        compact 
          ? 'grid-cols-3' 
          : 'grid-cols-1 sm:grid-cols-2 lg:grid-cols-3'
      }`}>
        {posts.map((post, index) => (
          <motion.div
            key={post.id}
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: index * 0.1 }}
            className="group"
          >
            <a
              href={post.permalink}
              target="_blank"
              rel="noopener noreferrer"
              className="block"
            >
              <div className="relative aspect-square rounded-xl overflow-hidden bg-gray-100">
                <Image
                  src={post.media_url}
                  alt={post.caption}
                  fill
                  className="object-cover transition-transform duration-500 group-hover:scale-110"
                  sizes="(max-width: 768px) 100vw, (max-width: 1200px) 50vw, 33vw"
                />
                
                {/* Overlay */}
                <div className="absolute inset-0 bg-black/0 group-hover:bg-black/40 transition-all duration-300">
                  <div className="absolute inset-0 flex items-center justify-center opacity-0 group-hover:opacity-100 transition-opacity duration-300">
                    <div className="text-white text-center p-4">
                      {!compact && (
                        <>
                          <div className="flex items-center justify-center gap-4 mb-2">
                            {post.like_count && (
                              <div className="flex items-center gap-1">
                                <Heart className="w-4 h-4" />
                                <span className="text-sm">{post.like_count}</span>
                              </div>
                            )}
                            {post.comments_count && (
                              <div className="flex items-center gap-1">
                                <MessageCircle className="w-4 h-4" />
                                <span className="text-sm">{post.comments_count}</span>
                              </div>
                            )}
                          </div>
                          <p className="text-sm">{truncateCaption(post.caption, 80)}</p>
                        </>
                      )}
                      <div className="mt-2">
                        <ExternalLink className="w-5 h-5 mx-auto" />
                      </div>
                    </div>
                  </div>
                </div>

                {/* Media type indicator */}
                {post.media_type === 'CAROUSEL_ALBUM' && (
                  <div className="absolute top-2 right-2 bg-black/50 rounded-full p-1">
                    <div className="w-2 h-2 bg-white rounded-full"></div>
                  </div>
                )}
              </div>

              {!compact && (
                <div className="mt-3">
                  <p className="text-sm text-gray-600 line-clamp-2">
                    {truncateCaption(post.caption)}
                  </p>
                  <p className="text-xs text-gray-400 mt-1">
                    {formatTimeAgo(post.timestamp)}
                  </p>
                </div>
              )}
            </a>
          </motion.div>
        ))}
      </div>

      {!compact && (
        <div className="text-center mt-8">
          <a
            href="https://instagram.com/positive.seven"
            target="_blank"
            rel="noopener noreferrer"
            className="inline-flex items-center gap-2 px-6 py-3 bg-gradient-to-r from-purple-500 to-pink-500 text-white rounded-xl hover:from-purple-600 hover:to-pink-600 transition-all duration-300 font-medium"
          >
            View More on Instagram
            <Instagram className="w-5 h-5" />
          </a>
        </div>
      )}
    </div>
  );
}
