'use client'

import { motion } from 'framer-motion'
import {
  Phone,
  Mail,
  MessageCircle,
  Users,
  Award,
  Shield,
  Star
} from 'lucide-react'

const CONTACT_METHODS = [
  {
    icon: Phone,
    action: 'tel:+917878005500',
    color: 'from-blue-500 to-blue-600',
    hoverColor: 'hover:from-blue-600 hover:to-blue-700'
  },
  {
    icon: Mail,
    action: 'mailto:<EMAIL>',
    color: 'from-green-500 to-green-600',
    hoverColor: 'hover:from-green-600 hover:to-green-700'
  },
  {
    icon: MessageCircle,
    action: 'https://wa.me/917878005500',
    color: 'from-emerald-500 to-emerald-600',
    hoverColor: 'hover:from-emerald-600 hover:to-emerald-700'
  }
]

const QUICK_STATS = [
  {
    icon: Users,
    value: '10,000+',
    label: 'Students Served',
    color: 'text-blue-600'
  },
  {
    icon: Award,
    value: '10+',
    label: 'Years Experience',
    color: 'text-green-600'
  },
  {
    icon: Shield,
    value: '100%',
    label: 'Safety Record',
    color: 'text-purple-600'
  },
  {
    icon: Star,
    value: '4.8/5',
    label: 'Average Rating',
    color: 'text-yellow-600'
  }
]

export function ContactInfo() {
  const containerVariants = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        staggerChildren: 0.1
      }
    }
  }

  const itemVariants = {
    hidden: { opacity: 0, x: 20 },
    visible: { opacity: 1, x: 0 }
  }

  return (
    <motion.div
      variants={containerVariants}
      initial="hidden"
      animate="visible"
      className="space-y-8"
    >
      {/* Header */}
      <motion.div variants={itemVariants}>
        <h2 className="text-3xl font-bold text-gray-900 mb-4">Get in Touch</h2>
        <p className="text-gray-600 leading-relaxed">
          Our experienced team is ready to help you plan the perfect educational adventure.
        </p>
      </motion.div>

      {/* Contact Methods - Icon Only */}
      <motion.div variants={itemVariants} className="flex justify-center gap-6">
        {CONTACT_METHODS.map((method, index) => (
          <a
            key={index}
            href={method.action}
            target={method.action.startsWith('http') ? '_blank' : undefined}
            rel={method.action.startsWith('http') ? 'noopener noreferrer' : undefined}
            className={`w-16 h-16 bg-gradient-to-r ${method.color} ${method.hoverColor} rounded-full flex items-center justify-center transition-all duration-300 transform hover:scale-110 shadow-lg hover:shadow-xl`}
          >
            <method.icon className="w-8 h-8 text-white" />
          </a>
        ))}
      </motion.div>

      {/* Social Proof */}
      <motion.div variants={itemVariants} className="text-center">
        <div className="inline-flex items-center gap-2 bg-yellow-50 border border-yellow-200 rounded-full px-4 py-2">
          <Star className="w-4 h-4 text-yellow-600 fill-current" />
          <span className="text-sm text-yellow-800">
            <span className="font-semibold">Trusted by 25+ schools</span> across Gujarat
          </span>
        </div>
      </motion.div>
    </motion.div>
  )
}
