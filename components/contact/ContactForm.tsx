'use client'

import { useState } from 'react'
import { motion } from 'framer-motion'
import {
  Send,
  User,
  Mail,
  Phone,
  MapPin,
  Users,
  Calendar,
  MessageSquare,
  CheckCircle,
  AlertCircle
} from 'lucide-react'
import But<PERSON> from '@/components/ui/Button'
import DatePicker from '@/components/ui/DatePicker'
import {
  validatePhoneNumber,
  validateEmailAddress,
  validateName,
  validateMessage,
  handlePhoneInput,
  ValidationResult
} from '@/lib/validation-utils'

interface FormData {
  inquiryType: string
  name: string
  email: string
  phone: string
  destination: string
  customDestination: string
  groupSize: string
  travelDates: string
  message: string
}

interface FormErrors {
  [key: string]: string
}

const INQUIRY_TYPES = [
  { value: 'general', label: 'General Inquiry' },
  { value: 'booking', label: 'Trip Booking' },
  { value: 'custom', label: 'Custom Package' },
  { value: 'group', label: 'Group Discount' },
  { value: 'support', label: 'Support' }
]

const DESTINATIONS = [
  'Manali, Himachal Pradesh',
  'Rishikesh, Uttarakhand',
  'Dharamshala, Himachal Pradesh',
  'Tirthan Valley, Himachal Pradesh',
  'Goa',
  'Rajasthan',
  'Kerala',
  'Custom Destination'
]

const GROUP_SIZES = [
  '10-20 students',
  '21-40 students',
  '41-60 students',
  '60+ students',
  'Not decided yet'
]

export function ContactForm() {
  const [formData, setFormData] = useState<FormData>({
    inquiryType: '',
    name: '',
    email: '',
    phone: '',
    destination: '',
    customDestination: '',
    groupSize: '',
    travelDates: '',
    message: ''
  })

  const [errors, setErrors] = useState<FormErrors>({})
  const [isSubmitting, setIsSubmitting] = useState(false)
  const [submitStatus, setSubmitStatus] = useState<'idle' | 'success' | 'error'>('idle')

  // Real-time field validation
  const validateField = (fieldName: keyof FormData, value: string): string => {
    let result: ValidationResult

    switch (fieldName) {
      case 'name':
        result = validateName(value)
        break
      case 'email':
        result = validateEmailAddress(value)
        break
      case 'phone':
        result = validatePhoneNumber(value)
        break
      case 'message':
        result = validateMessage(value)
        break
      case 'travelDates':
        if (value) {
          const selectedDate = new Date(value)
          const today = new Date()
          today.setHours(0, 0, 0, 0) // Reset time to start of day for comparison
          if (selectedDate < today) {
            return 'Travel date cannot be in the past'
          }
        }
        return ''
      default:
        return ''
    }

    return result.isValid ? '' : (result.error || 'Invalid input')
  }

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement | HTMLSelectElement>) => {
    const { name, value } = e.target
    let processedValue = value

    // Special handling for phone input
    if (name === 'phone') {
      processedValue = handlePhoneInput(value)
    }

    setFormData(prev => ({ ...prev, [name]: processedValue }))

    // Real-time validation for required fields
    if (['name', 'email', 'phone', 'message', 'travelDates'].includes(name) && processedValue.trim()) {
      const error = validateField(name as keyof FormData, processedValue)
      setErrors(prev => ({ ...prev, [name]: error }))
    } else if (processedValue.trim() === '') {
      // Clear error when field is empty (will be caught by form validation)
      setErrors(prev => ({ ...prev, [name]: '' }))
    }
  }

  // Helper function to format date for display
  const formatDateForEmail = (dateString: string) => {
    if (!dateString) return 'Not specified'
    try {
      const date = new Date(dateString)
      return date.toLocaleDateString('en-US', {
        year: 'numeric',
        month: 'long',
        day: 'numeric'
      })
    } catch {
      return dateString
    }
  }

  const validateForm = (): boolean => {
    const newErrors: FormErrors = {}

    // Validate all required fields
    const nameError = validateField('name', formData.name)
    if (nameError) newErrors.name = nameError

    const emailError = validateField('email', formData.email)
    if (emailError) newErrors.email = emailError

    const phoneError = validateField('phone', formData.phone)
    if (phoneError) newErrors.phone = phoneError

    const messageError = validateField('message', formData.message)
    if (messageError) newErrors.message = messageError

    setErrors(newErrors)
    return Object.keys(newErrors).length === 0
  }

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()

    if (!validateForm()) return

    // Prevent multiple submissions
    if (isSubmitting) return

    setIsSubmitting(true)

    try {
      // Determine the final destination value
      const finalDestination = formData.destination === 'Custom Destination'
        ? formData.customDestination
        : formData.destination;

      // Prepare data for API
      const inquiryData = {
        name: formData.name,
        email: formData.email,
        phone: formData.phone,
        subject: `${formData.inquiryType} - ${finalDestination || 'General Inquiry'}`,
        message: `
Destination: ${finalDestination || 'Not specified'}
Group Size: ${formData.groupSize || 'Not specified'}
Preferred Travel Date: ${formatDateForEmail(formData.travelDates)}

Message:
${formData.message}
        `.trim(),
        inquiry_type: formData.inquiryType || 'General Inquiry',
        metadata: {
          destination: finalDestination,
          groupSize: formData.groupSize,
          travelDates: formData.travelDates,
          isCustomDestination: formData.destination === 'Custom Destination'
        }
      }

      // Send to API
      const response = await fetch('/api/inquiries', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(inquiryData),
      })

      if (!response.ok) {
        throw new Error('Failed to submit inquiry')
      }

      const result = await response.json()
      console.log('Inquiry submitted successfully:', result)

      setSubmitStatus('success')

      // Reset form after successful submission
      setTimeout(() => {
        setFormData({
          inquiryType: '',
          name: '',
          email: '',
          phone: '',
          destination: '',
          customDestination: '',
          groupSize: '',
          travelDates: '',
          message: ''
        })
        setSubmitStatus('idle')
      }, 3000)

    } catch (error) {
      console.error('Error submitting inquiry:', error)
      setSubmitStatus('error')
      setTimeout(() => setSubmitStatus('idle'), 3000)
    } finally {
      setIsSubmitting(false)
    }
  }

  const isFormValid = formData.name && formData.email && formData.phone && formData.message

  return (
    <div className="card-modern p-8">
      <div className="mb-8">
        <h2 className="text-3xl font-bold text-gray-900 mb-4">Send Us a Message</h2>
        <p className="text-gray-600">
          Fill out the form below and our team will get back to you within 24 hours with a personalized response.
        </p>
      </div>

      <form onSubmit={handleSubmit} className="space-y-6">
        {/* Inquiry Type */}
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-2">
            <MessageSquare className="w-4 h-4 inline mr-2" />
            Type of Inquiry *
          </label>
          <select
            name="inquiryType"
            value={formData.inquiryType}
            onChange={handleInputChange}
            className="w-full p-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
            required
          >
            <option value="">Select inquiry type</option>
            {INQUIRY_TYPES.map((type) => (
              <option key={type.value} value={type.value}>
                {type.label}
              </option>
            ))}
          </select>
        </div>

        {/* Personal Information */}
        <div className="grid md:grid-cols-2 gap-6">
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              <User className="w-4 h-4 inline mr-2" />
              Full Name *
            </label>
            <input
              type="text"
              name="name"
              value={formData.name}
              onChange={handleInputChange}
              className={`w-full p-3 border rounded-lg focus:ring-2 focus:border-transparent ${
                errors.name
                  ? 'border-red-500 focus:ring-red-500 bg-red-50'
                  : 'border-gray-300 focus:ring-blue-500'
              }`}
              placeholder="Enter your full name"
              required
            />
            {errors.name && (
              <p className="mt-1 text-sm text-red-600">{errors.name}</p>
            )}
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              <Mail className="w-4 h-4 inline mr-2" />
              Email Address *
            </label>
            <input
              type="email"
              name="email"
              value={formData.email}
              onChange={handleInputChange}
              className={`w-full p-3 border rounded-lg focus:ring-2 focus:border-transparent ${
                errors.email
                  ? 'border-red-500 focus:ring-red-500 bg-red-50'
                  : 'border-gray-300 focus:ring-blue-500'
              }`}
              placeholder="Enter your email"
              required
            />
            {errors.email && (
              <p className="mt-1 text-sm text-red-600">{errors.email}</p>
            )}
          </div>
        </div>

        <div>
          <label className="block text-sm font-medium text-gray-700 mb-2">
            <Phone className="w-4 h-4 inline mr-2" />
            Phone Number * <span className="text-xs text-gray-500">(10 digits only)</span>
          </label>
          <input
            type="tel"
            name="phone"
            value={formData.phone}
            onChange={handleInputChange}
            maxLength={10}
            className={`w-full p-3 border rounded-lg focus:ring-2 focus:border-transparent ${
              errors.phone
                ? 'border-red-500 focus:ring-red-500 bg-red-50'
                : 'border-gray-300 focus:ring-blue-500'
            }`}
            placeholder="9876543210"
            required
          />
          {errors.phone && (
            <p className="mt-1 text-sm text-red-600">{errors.phone}</p>
          )}
        </div>

        {/* Trip Details */}
        <div className="grid md:grid-cols-2 gap-6">
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              <MapPin className="w-4 h-4 inline mr-2" />
              Preferred Destination
            </label>
            <select
              name="destination"
              value={formData.destination}
              onChange={handleInputChange}
              className="w-full p-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
            >
              <option value="">Select destination</option>
              {DESTINATIONS.map((destination) => (
                <option key={destination} value={destination}>
                  {destination}
                </option>
              ))}
            </select>

            {/* Custom Destination Text Input */}
            {formData.destination === 'Custom Destination' && (
              <motion.div
                initial={{ opacity: 0, height: 0 }}
                animate={{ opacity: 1, height: 'auto' }}
                exit={{ opacity: 0, height: 0 }}
                className="mt-3"
              >
                <input
                  type="text"
                  name="customDestination"
                  value={formData.customDestination}
                  onChange={handleInputChange}
                  className="w-full p-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                  placeholder="Please specify your custom destination..."
                  required={formData.destination === 'Custom Destination'}
                />
              </motion.div>
            )}
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              <Users className="w-4 h-4 inline mr-2" />
              Group Size
            </label>
            <select
              name="groupSize"
              value={formData.groupSize}
              onChange={handleInputChange}
              className="w-full p-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
            >
              <option value="">Select group size</option>
              {GROUP_SIZES.map((size) => (
                <option key={size} value={size}>
                  {size}
                </option>
              ))}
            </select>
          </div>
        </div>

        <div>
          <DatePicker
            label="Preferred Travel Date"
            name="travelDates"
            value={formData.travelDates}
            onChange={(value) => setFormData(prev => ({ ...prev, travelDates: value }))}
            placeholder="Select your preferred travel date"
            minDate={new Date().toISOString().split('T')[0]} // Today's date as minimum
            className="w-full"
          />
        </div>

        {/* Message */}
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-2">
            Message *
          </label>
          <textarea
            name="message"
            value={formData.message}
            onChange={handleInputChange}
            rows={5}
            className={`w-full p-3 border rounded-lg focus:ring-2 focus:border-transparent resize-none ${
              errors.message
                ? 'border-red-500 focus:ring-red-500 bg-red-50'
                : 'border-gray-300 focus:ring-blue-500'
            }`}
            placeholder="Tell us about your requirements, special needs, or any questions you have..."
            required
          />
          {errors.message && (
            <p className="mt-1 text-sm text-red-600">{errors.message}</p>
          )}
        </div>

        {/* Submit Button */}
        <div>
          <Button
            type="submit"
            disabled={!isFormValid || isSubmitting}
            className="w-full"
            size="lg"
            variant="secondary"
          >
            {isSubmitting ? (
              <>
                <div className="w-5 h-5 border-2 border-white border-t-transparent rounded-full animate-spin mr-2" />
                Sending Message...
              </>
            ) : (
              <>
                <Send className="w-5 h-5 mr-2" />
                Send Message
              </>
            )}
          </Button>
        </div>

        {/* Status Messages */}
        {submitStatus === 'success' && (
          <motion.div
            initial={{ opacity: 0, y: 10 }}
            animate={{ opacity: 1, y: 0 }}
            className="p-4 bg-green-50 border border-green-200 rounded-lg flex items-center gap-3"
          >
            <CheckCircle className="w-5 h-5 text-green-600" />
            <div>
              <div className="font-medium text-green-900">Message sent successfully!</div>
              <div className="text-sm text-green-700">We'll get back to you within 24 hours.</div>
            </div>
          </motion.div>
        )}

        {submitStatus === 'error' && (
          <motion.div
            initial={{ opacity: 0, y: 10 }}
            animate={{ opacity: 1, y: 0 }}
            className="p-4 bg-red-50 border border-red-200 rounded-lg flex items-center gap-3"
          >
            <AlertCircle className="w-5 h-5 text-red-600" />
            <div>
              <div className="font-medium text-red-900">Failed to send message</div>
              <div className="text-sm text-red-700">Please try again or contact us directly.</div>
            </div>
          </motion.div>
        )}
      </form>

      {/* Additional Info */}
      <div className="mt-8 p-4 bg-blue-50 border border-blue-200 rounded-lg">
        <h4 className="font-medium text-blue-900 mb-2">What happens next?</h4>
        <ul className="text-sm text-blue-800 space-y-1">
          <li>• Our team will review your inquiry within 24 hours</li>
          <li>• We'll contact you to discuss your requirements in detail</li>
          <li>• You'll receive a customized itinerary and quote</li>
          <li>• We'll help you plan every aspect of your educational trip</li>
        </ul>
      </div>
    </div>
  )
}
