'use client';

import Image from 'next/image'
import Link from 'next/link'
import { motion } from 'framer-motion'
import {
  Users,
  Lock,
  Award,
  MapPin,
  Crosshair,
  CircleUserRound,
  Heart,
  Star,
  Target,
  Eye,
  Shield,
  Globe,
  BookOpen,
  TrendingUp,
  CheckCircle
} from 'lucide-react'
import <PERSON><PERSON> from '@/components/ui/Button'
import { TeamSection } from '@/components/about/TeamSection'
import ScrollReveal from '@/components/animations/ScrollReveal'
import { StatCard } from '@/components/animations/AnimatedCounter'
import { SectionErrorBoundary } from '@/components/error/ComprehensiveErrorBoundary'

export default function AboutClient() {
  return (
    <>
      {/* Hero Section */}
      <SectionErrorBoundary context="about-hero">
        <section className="relative py-20 overflow-hidden">
          <div className="absolute inset-0 bg-gradient-to-r from-blue-600/10 to-green-600/10" />
          
          {/* Floating Background Elements */}
          <div className="absolute inset-0 overflow-hidden pointer-events-none">
            <div className="absolute top-20 left-10 w-32 h-32 bg-blue-200 rounded-full mix-blend-multiply filter blur-xl opacity-70 animate-float"></div>
            <div className="absolute top-40 right-20 w-24 h-24 bg-green-200 rounded-full mix-blend-multiply filter blur-xl opacity-70 animate-float" style={{ animationDelay: '2s' }}></div>
            <div className="absolute bottom-20 left-20 w-28 h-28 bg-emerald-200 rounded-full mix-blend-multiply filter blur-xl opacity-70 animate-float" style={{ animationDelay: '4s' }}></div>
          </div>

          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 relative">
            <div className="grid lg:grid-cols-2 gap-12 items-center">
              <ScrollReveal direction="left" duration={0.8}>
                <div>
                  <h1 className="text-4xl sm:text-5xl md:text-6xl font-display font-black text-gray-900 mb-6 leading-tight">
                    Transforming Education Through
                    <span className="text-transparent bg-clip-text bg-gradient-to-r from-blue-600 to-green-600"> Travel</span>
                  </h1>
                  <p className="text-xl text-gray-700 mb-8 leading-relaxed font-light">
                    Since 2016, Positive7 has been pioneering experiential learning through carefully crafted educational tours.
                    We believe that the world is the greatest classroom, and every journey is an opportunity to learn, grow, and discover.
                  </p>
                  <div className="flex flex-col sm:flex-row gap-4 sm:gap-6">
                    <Link href="/contact">
                      <Button size="lg" className="w-full sm:w-auto">
                        Contact Us
                      </Button>
                    </Link>
                    <Link href="/trips">
                      <Button variant="outline" size="lg" className="w-full sm:w-auto">
                        View Our Trips
                      </Button>
                    </Link>
                  </div>
                </div>
              </ScrollReveal>

              <ScrollReveal direction="right" duration={0.8} delay={0.2}>
                <div className="relative">
                  <div className="relative h-80 sm:h-96 card-modern overflow-hidden shadow-2xl">
                    <Image
                      src="/images/about-hero.jpeg"
                      alt="Positive7 Educational Tours - Students on adventure"
                      fill
                      className="object-cover"
                      priority
                    />
                    <div className="absolute inset-0 bg-gradient-to-t from-black/30 to-transparent" />
                  </div>
                  {/* Floating Stats Cards */}
                  <div className="absolute -bottom-4 sm:-bottom-6 -left-4 sm:-left-6 bg-white rounded-xl p-3 sm:p-4 shadow-lg">
                    <div className="text-xl sm:text-2xl font-bold text-blue-600">10+</div>
                    <div className="text-xs text-gray-600">Years Experience</div>
                  </div>
                  <div className="absolute -top-4 sm:-top-6 -right-4 sm:-right-6 bg-white rounded-xl p-3 sm:p-4 shadow-lg">
                    <div className="text-xl sm:text-2xl font-bold text-green-600">40K+</div>
                    <div className="text-xs text-gray-600">Happy Students</div>
                  </div>
                </div>
              </ScrollReveal>
            </div>
          </div>
        </section>
      </SectionErrorBoundary>

      {/* Stats Section */}
      <SectionErrorBoundary context="about-stats">
        <section className="py-20 bg-gray-50">
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <ScrollReveal direction="up" duration={0.6}>
              <div className="text-center mb-16">
                <h2 className="text-3xl sm:text-4xl font-bold text-gray-900 mb-4">Our Impact in Numbers</h2>
                <p className="text-lg text-gray-600 max-w-2xl mx-auto">
                  Over 10 years of creating transformative educational experiences
                </p>
              </div>
            </ScrollReveal>

            <div className="grid grid-cols-2 md:grid-cols-4 gap-8">
              <ScrollReveal direction="up" duration={0.6} delay={0.1}>
                <StatCard
                  icon={<Users className="w-6 h-6" />}
                  value={40000}
                  label="Students Traveled"
                  suffix="+"
                  color="blue"
                />
              </ScrollReveal>
              <ScrollReveal direction="up" duration={0.6} delay={0.2}>
                <StatCard
                  icon={<MapPin className="w-6 h-6" />}
                  value={100}
                  label="Destinations Covered"
                  suffix="+"
                  color="green"
                />
              </ScrollReveal>
              <ScrollReveal direction="up" duration={0.6} delay={0.3}>
                <StatCard
                  icon={<Award className="w-6 h-6" />}
                  value={10}
                  label="Years of Excellence"
                  suffix="+"
                  color="purple"
                />
              </ScrollReveal>
              <ScrollReveal direction="up" duration={0.6} delay={0.4}>
                <StatCard
                  icon={<Star className="w-6 h-6" />}
                  value={5}
                  label="Average Rating"
                  suffix="/5"
                  color="orange"
                />
              </ScrollReveal>
            </div>
          </div>
        </section>
      </SectionErrorBoundary>

      {/* Mission & Vision */}
      <SectionErrorBoundary context="about-mission">
        <section className="py-20">
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div className="grid lg:grid-cols-2 gap-16 items-center">
              <ScrollReveal direction="left" duration={0.8}>
                <div>
                  <div className="flex items-center gap-3 mb-6">
                    <div className="w-12 h-12 bg-gradient-to-r from-blue-500 to-green-500 rounded-full flex items-center justify-center">
                      <Crosshair className="w-6 h-6 text-white" />
                    </div>
                    <h2 className="text-2xl font-bold text-gray-900">Our Aim</h2>
                  </div>
                  <p className="text-md text-gray-700 mb-6 leading-relaxed">
                    To bridge the gap between classroom learning and real-world understanding through safe, immersive, and educational travel experiences that ignite curiosity, build confidence, and develop responsible global citizens.
                  </p>
                  <div className="flex items-center gap-3 mb-6">
                    <div className="w-12 h-12 bg-gradient-to-r from-blue-500 to-green-500 rounded-full flex items-center justify-center">
                      <Target className="w-6 h-6 text-white" />
                    </div>
                    <h2 className="text-2xl font-bold text-gray-900">Our Mission</h2>
                  </div>
                  <p className="text-md text-gray-700 mb-6 leading-relaxed">
                    We are committed to empowering young minds by offering experiences that are safe, structured, and socially impactful — turning every trip into a journey of growth.
                  </p>
                  <div className="flex items-center gap-3 mb-6">
                    <div className="w-12 h-12 bg-gradient-to-r from-green-500 to-blue-500 rounded-full flex items-center justify-center">
                      <Eye className="w-6 h-6 text-white" />
                    </div>
                    <h2 className="text-2xl font-bold text-gray-900">Our Vision</h2>
                  </div>
                  <p className="text-md text-gray-700 leading-relaxed">
                    To be a leading force in experiential education, inspiring the next generation to learn by doing, explore with purpose, and grow into empathetic, aware, and future-ready individuals.                 
                    We envision a world where every student experiences learning beyond books — in the real world, with real people, and real impact.
                  </p>
                </div>
              </ScrollReveal>

              <ScrollReveal direction="right" duration={0.8} delay={0.2}>
                <div className="relative">
                  <div className="card-modern p-8 bg-gradient-to-br from-blue-50 via-white to-green-50">
                    <h3 className="text-5xl font-bold text-gray-900 mb-6">Core Values</h3>
                    <div className="space-y-4">
                      {[
                        { icon: Shield, title: 'Safety First', desc: 'We uphold the highest standards of safety, supervision, and preparedness — because meaningful learning can only happen in a secure environment.' },
                        { icon: BookOpen, title: 'Experiential Learning', desc: 'We believe the world is the best classroom. Every destination and activity is designed to deepen understanding through hands-on experiences.' },
                        { icon: Heart, title: 'Student-Centric Approach', desc: 'Every decision we make is guided by the emotional, intellectual, and physical well-being of the students we serve.' },
                        { icon: Globe, title: 'Cultural Diversity', desc: 'We promote respect for diversity, environmental responsibility, and cultural understanding in every journey.' },
                        { icon: Lock, title: 'Integrity and Trust', desc: 'We build lasting relationships with schools, parents, and partners through honesty, professionalism, and consistency.' }
                      ].map((value, index) => (
                        <div key={index} className="flex items-start gap-4">
                          <div className="w-10 h-10 bg-gradient-to-r from-blue-500 to-green-500 rounded-full flex items-center justify-center flex-shrink-0">
                            <value.icon className="w-5 h-5 text-white" />
                          </div>
                          <div>
                            <h4 className="font-semibold text-gray-900 mb-1">{value.title}</h4>
                            <p className="text-sm text-gray-600">{value.desc}</p>
                          </div>
                        </div>
                      ))}
                    </div>
                  </div>
                </div>
              </ScrollReveal>
            </div>
          </div>
        </section>
      </SectionErrorBoundary>

      {/* What Makes Us Different */}
      <SectionErrorBoundary context="about-differentiators">
        <section className="py-20 bg-gray-50">
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <ScrollReveal direction="up" duration={0.6}>
              <div className="text-center mb-16">
                <h2 className="text-3xl sm:text-4xl font-bold text-gray-900 mb-4">What Makes Us Different</h2>
                <p className="text-lg text-gray-600 max-w-3xl mx-auto">
                  At Positive7, we go beyond being a travel organizer — we are education partners. What makes us different is our unwavering focus on purpose-driven travel that enriches minds, builds character, and delivers lasting impact.
                </p>
              </div>
            </ScrollReveal>

            <div className="grid md:grid-cols-3 gap-8">
              {[
                {
                  icon: BookOpen,
                  title: 'Learning with Purpose',
                  description: 'Every tour is designed with clear academic, emotional, and social learning outcomes. We align our experiences with curriculum goals, NEP 2020, and school values — making every trip an extension of the classroom.'
                },
                {
                  icon: Shield,
                  title: 'Safety Beyond Standards',
                  description: 'Our team is trained in first aid, fire safety, critical response, and evacuation. With vetted accommodations, well-maintained fleet, experienced, background-verified drivers, and 24/7 staff supervision, we maintain a zero-compromise safety culture.'
                },
                {
                  icon: CircleUserRound,
                  title: 'Expert Team of Educators & Explorers',
                  description: 'Led by educators, travel professionals, and subject experts, our team ensures every journey is informative, inspiring, and smooth. We’re as passionate about student growth as we are about travel logistics.'
                },
                {
                  icon: CheckCircle,
                  title: 'Customization & Care',
                  description: 'No two schools — or students — are alike. We tailor each itinerary to your group’s age, syllabus, travel goals, and budget, ensuring relevance and personal connection.'
                },
                {
                  icon: Heart,
                  title: 'Trusted by Schools, Loved by Students',
                  description: 'We have a strong track record of working with leading schools and institutions across Gujarat and beyond. Our trips earn positive feedback from students, parents, and educators alike.'
                },
                {
                  icon: TrendingUp,
                  title: 'Focus on Reflection & Growth',
                  description: 'Our programs include guided reflection sessions, journaling, and project components that encourage students to process what they’ve learned — making travel a tool for deeper thinking.'
                }
              ].map((item, index) => (
                <ScrollReveal key={index} direction="up" duration={0.6} delay={index * 0.1}>
                  <div className="card-modern p-6 text-center hover:shadow-2xl transition-all duration-500 hover:-translate-y-2">
                    <div className="w-16 h-16 bg-gradient-to-r from-blue-500 to-green-500 rounded-full flex items-center justify-center mx-auto mb-4">
                      <item.icon className="w-8 h-8 text-white" />
                    </div>
                    <h3 className="text-xl font-bold text-gray-900 mb-3">{item.title}</h3>
                    <p className="text-gray-600">{item.description}</p>
                  </div>
                </ScrollReveal>
              ))}
            </div>
          </div>
        </section>
      </SectionErrorBoundary>

      {/* Team Section */}
      <SectionErrorBoundary context="about-team">
        <TeamSection />
      </SectionErrorBoundary>

      {/* Call to Action */}
      <SectionErrorBoundary context="about-cta">
        <section className="py-20">
          <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
            <ScrollReveal direction="up" duration={0.6}>
              <h2 className="text-3xl sm:text-4xl font-bold text-gray-900 mb-6">
                Ready to Transform Your Students' Learning Experience?
              </h2>
              <p className="text-lg sm:text-xl text-gray-600 mb-8">
                Join hundreds of educators who have chosen Positive7 for unforgettable educational adventures
              </p>
              <div className="flex flex-col sm:flex-row gap-4 justify-center">
                <Link href="/contact">
                  <Button size="lg" className="bg-gradient-to-r from-blue-600 to-green-600 hover:from-blue-700 hover:to-green-700">
                    Plan Your Trip
                  </Button>
                </Link>
                <Link href="/trips">
                  <Button variant="outline" size="lg">
                    Browse Destinations
                  </Button>
                </Link>
              </div>
            </ScrollReveal>
          </div>
        </section>
      </SectionErrorBoundary>
    </>
  );
}
