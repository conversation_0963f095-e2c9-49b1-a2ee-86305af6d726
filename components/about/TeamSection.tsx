'use client'

import { motion } from 'framer-motion'
import Image from 'next/image'
import {
  Book<PERSON><PERSON>,
  Users,
  MapPin,
  Award,
  User,
  RefreshCw
} from 'lucide-react'
import { getImageWithFallback } from '@/lib/image-fallbacks'
import { usePublicTeamMembers } from '@/hooks/useTeamMembers'
import LoadingSpinner from '@/components/ui/LoadingSpinner'

interface TeamMember {
  id: string
  name: string
  position: string
  bio: string
  image_url: string | null
  sort_order: number | null
  is_active: boolean | null
}

export function TeamSection() {
  // Use React Query to fetch team members with real-time updates
  const { data: teamMembers = [], isLoading: loading, error, refetch } = usePublicTeamMembers()

  const containerVariants = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        staggerChildren: 0.1
      }
    }
  }

  const itemVariants = {
    hidden: { opacity: 0, y: 20 },
    visible: { opacity: 1, y: 0 }
  }

  // Generate a random color for avatar backgrounds
  const getRandomGradient = (seed: string) => {
    // Use the seed to generate consistent colors for the same team member
    const hash = seed.split('').reduce((a, b) => {
      a = ((a << 5) - a) + b.charCodeAt(0);
      return a & a;
    }, 0);
    
    // Generate colors based on the hash
    const hue1 = Math.abs(hash % 360);
    const hue2 = (hue1 + 40) % 360;
    
    return `linear-gradient(135deg, hsl(${hue1}, 70%, 80%), hsl(${hue2}, 70%, 60%))`;
  };

  return (
    <section className="py-20">
      <div className="max-w-7xl mx-auto px-4">
        <div className="text-center mb-16">
          <h2 className="text-4xl font-bold text-gray-900 mb-4">Meet Our Team</h2>
          <p className="text-xl text-gray-600 max-w-3xl mx-auto">
            Our passionate team of educators, adventure specialists, and travel experts work together
            to create unforgettable learning experiences for every student
          </p>
        </div>

        {loading ? (
          <div className="flex justify-center py-12">
            <LoadingSpinner size="large" />
          </div>
        ) : error ? (
          <div className="text-center py-12">
            <div className="text-gray-400 mb-4">
              <Users className="w-16 h-16 mx-auto" />
            </div>
            <h3 className="text-xl font-medium text-gray-900 mb-2">Failed to load team members</h3>
            <p className="text-gray-600 mb-4">
              There was an error loading the team members. Please try again.
            </p>
            <button
              onClick={() => refetch()}
              className="inline-flex items-center px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 transition-colors"
            >
              <RefreshCw className="w-4 h-4 mr-2" />
              Try Again
            </button>
          </div>
        ) : (
          <motion.div
            variants={containerVariants}
            initial="hidden"
            whileInView="visible"
            viewport={{ once: true }}
            className="grid md:grid-cols-2 lg:grid-cols-3 gap-8"
          >
            {teamMembers.map((member) => (
              <motion.div
                key={member.id}
                variants={itemVariants}
                className="group"
              >
                <div className="bg-white rounded-2xl p-8 shadow-lg hover:shadow-xl transition-all duration-300 h-full">
                  {/* Profile Image */}
                  <div className="relative w-40 h-40 mx-auto mb-6 overflow-hidden">
                    {member.image_url ? (
                      <Image
                        src={getImageWithFallback(member.image_url)}
                        alt={member.name}
                        fill
                        className="object-cover rounded-full"
                        sizes="(max-width: 768px) 100vw, (max-width: 1200px) 50vw, 33vw"
                        loading="lazy"
                      />
                    ) : (
                      <div 
                        className="w-full h-full rounded-full flex items-center justify-center"
                        style={{ background: getRandomGradient(member.id) }}
                      >
                        <div className="w-full h-full flex items-center justify-center">
                          <User className="w-20 h-20 text-white opacity-70" strokeWidth={1.5} />
                        </div>
                      </div>
                    )}
                  </div>

                  {/* Member Info */}
                  <div className="text-center">
                    <h3 className="text-xl font-bold text-gray-900 mb-2">{member.name}</h3>
                    <p className="text-blue-600 font-medium mb-4">{member.position}</p>
                    <p className="text-gray-600 text-sm leading-relaxed">{member.bio}</p>
                  </div>
                </div>
              </motion.div>
            ))}
          </motion.div>
        )}

        {/* Team Culture */}
        <div className="mt-16 bg-gradient-to-r from-blue-50 to-green-50 rounded-2xl p-8">
          <div className="text-center mb-8">
            <h3 className="text-2xl font-bold text-gray-900 mb-4">Our Team Culture</h3>
            <p className="text-gray-700 max-w-3xl mx-auto">
              We believe that a passionate, diverse, and dedicated team is the foundation of exceptional educational experiences.
            </p>
          </div>

          <div className="grid md:grid-cols-4 gap-6">
            <div className="text-center">
              <div className="w-12 h-12 bg-blue-600 rounded-full flex items-center justify-center mx-auto mb-3">
                <BookOpen className="w-6 h-6 text-white" />
              </div>
              <h4 className="font-semibold text-gray-900 mb-2">Continuous Learning</h4>
              <p className="text-sm text-gray-600">We invest in our team's growth and development</p>
            </div>

            <div className="text-center">
              <div className="w-12 h-12 bg-green-600 rounded-full flex items-center justify-center mx-auto mb-3">
                <Users className="w-6 h-6 text-white" />
              </div>
              <h4 className="font-semibold text-gray-900 mb-2">Collaboration</h4>
              <p className="text-sm text-gray-600">We work together to achieve common goals</p>
            </div>

            <div className="text-center">
              <div className="w-12 h-12 bg-purple-600 rounded-full flex items-center justify-center mx-auto mb-3">
                <Award className="w-6 h-6 text-white" />
              </div>
              <h4 className="font-semibold text-gray-900 mb-2">Excellence</h4>
              <p className="text-sm text-gray-600">We strive for the highest standards in everything</p>
            </div>

            <div className="text-center">
              <div className="w-12 h-12 bg-orange-600 rounded-full flex items-center justify-center mx-auto mb-3">
                <MapPin className="w-6 h-6 text-white" />
              </div>
              <h4 className="font-semibold text-gray-900 mb-2">Adventure Spirit</h4>
              <p className="text-sm text-gray-600">We embrace challenges and new experiences</p>
            </div>
          </div>
        </div>
      </div>
    </section>
  );
}
