{"$schema": "https://unpkg.com/knip@latest/schema.json", "entry": ["app/**/*.{ts,tsx}", "pages/**/*.{ts,tsx}", "components/**/*.{ts,tsx}", "lib/**/*.{ts,tsx}", "hooks/**/*.{ts,tsx}", "middleware.ts"], "project": ["**/*.{ts,tsx}", "!**/*.test.{ts,tsx}", "!**/*.spec.{ts,tsx}"], "ignore": ["next.config.js", "tailwind.config.js", "postcss.config.js", "scripts/**/*", "public/**/*", "node_modules/**/*"], "ignoreDependencies": ["next", "react", "react-dom", "typescript", "eslint", "tailwindcss", "postcss"]}