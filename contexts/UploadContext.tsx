'use client';

import React, { createContext, useContext, useState, useCallback } from 'react';

interface UploadState {
  isUploading: boolean;
  uploadCount: number;
  uploadProgress: Record<string, number>;
}

interface UploadContextType {
  uploadState: UploadState;
  startUpload: (uploadId: string) => void;
  updateUploadProgress: (uploadId: string, progress: number) => void;
  finishUpload: (uploadId: string) => void;
  cancelUpload: (uploadId: string) => void;
  isAnyUploading: boolean;
}

const UploadContext = createContext<UploadContextType | undefined>(undefined);

export function UploadProvider({ children }: { children: React.ReactNode }) {
  const [uploadState, setUploadState] = useState<UploadState>({
    isUploading: false,
    uploadCount: 0,
    uploadProgress: {},
  });

  const startUpload = useCallback((uploadId: string) => {
    setUploadState(prev => ({
      ...prev,
      isUploading: true,
      uploadCount: prev.uploadCount + 1,
      uploadProgress: {
        ...prev.uploadProgress,
        [uploadId]: 0,
      },
    }));
  }, []);

  const updateUploadProgress = useCallback((uploadId: string, progress: number) => {
    setUploadState(prev => ({
      ...prev,
      uploadProgress: {
        ...prev.uploadProgress,
        [uploadId]: progress,
      },
    }));
  }, []);

  // Helper function to remove upload from state
  const removeUpload = useCallback((uploadId: string) => {
    setUploadState(prev => {
      const newProgress = { ...prev.uploadProgress };
      delete newProgress[uploadId];
      const newUploadCount = Math.max(0, prev.uploadCount - 1);

      return {
        ...prev,
        isUploading: newUploadCount > 0,
        uploadCount: newUploadCount,
        uploadProgress: newProgress,
      };
    });
  }, []);

  const finishUpload = useCallback((uploadId: string) => {
    removeUpload(uploadId);
  }, [removeUpload]);

  const cancelUpload = useCallback((uploadId: string) => {
    removeUpload(uploadId);
  }, [removeUpload]);

  const isAnyUploading = uploadState.isUploading;

  const value: UploadContextType = {
    uploadState,
    startUpload,
    updateUploadProgress,
    finishUpload,
    cancelUpload,
    isAnyUploading,
  };

  return (
    <UploadContext.Provider value={value}>
      {children}
    </UploadContext.Provider>
  );
}

export function useUpload() {
  const context = useContext(UploadContext);
  if (context === undefined) {
    throw new Error('useUpload must be used within an UploadProvider');
  }
  return context;
}
