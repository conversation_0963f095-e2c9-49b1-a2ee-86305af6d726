import { <PERSON>adata } from 'next'
import { notFound } from 'next/navigation'
import Header from '@/components/layout/Header'
import Footer from '@/components/layout/Footer'
import GalleryView from '@/components/gallery/GalleryView'
import { createServerSupabase } from '@/lib/supabase-server'
import CacheManager from '@/components/cache/CacheManager'
import { PageErrorBoundary, SectionErrorBoundary } from '@/components/error/ComprehensiveErrorBoundary'

interface GalleryPageProps {
  params: Promise<{ id: string }>
}

// Force dynamic rendering and disable caching
export const dynamic = 'force-dynamic'
export const revalidate = 0

// Fetch individual gallery data directly from database
async function getGallery(id: string) {
  try {
    const supabase = createServerSupabase();

    // Get specific gallery with images
    const { data: gallery, error } = await supabase
      .from('galleries')
      .select(`
        *,
        trips:trip_id (
          id,
          title,
          destination,
          category
        ),
        gallery_images (
          id,
          gallery_id,
          image_url,
          cloudinary_public_id,
          order_index,
          created_at,
          updated_at
        )
      `)
      .eq('id', id)
      .eq('is_active', true)
      .single();

    if (error) {
      console.error('Error fetching gallery:', error);
      if (error.code === 'PGRST116') {
        return null;
      }
      return null;
    }

    // Sort images by order_index
    if (gallery.gallery_images) {
      gallery.gallery_images.sort((a: any, b: any) => (a.order_index || 0) - (b.order_index || 0));
    }

    return gallery;
  } catch (error) {
    console.error('Error fetching gallery:', error);
    return null;
  }
}

export async function generateMetadata({ params }: GalleryPageProps): Promise<Metadata> {
  const { id } = await params;
  const gallery = await getGallery(id);

  if (!gallery) {
    return {
      title: 'Gallery Not Found - Positive7 Educational Tours',
      description: 'The requested gallery could not be found.',
    };
  }

  return {
    title: `${gallery.name} - Photo Gallery | Positive7 Educational Tours`,
    description: gallery.description || `View photos from ${gallery.name} - memorable moments from our educational tours and adventures.`,
    keywords: `${gallery.name}, photo gallery, educational tours, student trips, ${gallery.trips?.destination || 'travel photos'}, Positive7`,
    openGraph: {
      title: `${gallery.name} - Photo Gallery`,
      description: gallery.description || `View photos from ${gallery.name}`,
      images: gallery.featured_image_url ? [gallery.featured_image_url] : [],
      type: 'website',
    },
  };
}

export default async function GalleryPage({ params }: GalleryPageProps) {
  const { id } = await params;
  const gallery = await getGallery(id);

  if (!gallery) {
    notFound();
  }

  return (
    <PageErrorBoundary context="gallery-detail-page">
      {/* Cache management for dynamic content */}
      <CacheManager addMetaTags={true} updateSW={true} />

      <Header />
      <main className="flex-1">
        <div className="min-h-screen bg-gradient-to-br from-blue-50 via-white to-green-50">
          <div className="max-w-7xl mx-auto px-4 py-8">
            <SectionErrorBoundary context="gallery-detail-view">
              <GalleryView galleryId={id} initialGallery={gallery} />
            </SectionErrorBoundary>
          </div>
        </div>
      </main>
      <Footer />
    </PageErrorBoundary>
  );
}
