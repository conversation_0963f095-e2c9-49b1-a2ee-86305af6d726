import { Metadata } from 'next'
import Header from '@/components/layout/Header'
import Footer from '@/components/layout/Footer'
import GalleryListClient from '@/components/gallery/GalleryListClient'
import { createServerSupabase } from '@/lib/supabase-server'
import <PERSON><PERSON><PERSON>anager from '@/components/cache/CacheManager'
import { PageErrorBoundary } from '@/components/error/ComprehensiveErrorBoundary'

export const metadata: Metadata = {
  title: 'Gallery - Positive7 Educational Tours',
  description: 'Explore our photo gallery showcasing memorable moments from educational tours, adventure camps, and student trips across India.',
  keywords: 'photo gallery, educational tours, student trips, adventure camps, travel photos, Positive7'
}

// Disable caching for real-time data
export const revalidate = 0

// Fetch galleries data directly from database
async function getGalleries() {
  try {
    const supabase = createServerSupabase();

    // Get all active galleries with their images for the gallery page
    const { data: galleries, error } = await supabase
      .from('galleries')
      .select(`
        id,
        name,
        description,
        trip_id,
        folder_name,
        is_active,
        featured_image_url,
        created_at,
        updated_at,
        trips:trip_id (
          id,
          title,
          destination,
          category
        ),
        gallery_images (
          id,
          gallery_id,
          image_url,
          cloudinary_public_id,
          order_index,
          created_at,
          updated_at
        )
      `)
      .eq('is_active', true)
      .order('created_at', { ascending: false });

    if (error) {
      console.error('Error fetching galleries:', error);
      return [];
    }

    return galleries || [];
  } catch (error) {
    console.error('Error fetching galleries:', error);
    return [];
  }
}

export default async function GalleryPage() {
  // Fetch galleries from the database
  const galleries = await getGalleries();

  return (
    <PageErrorBoundary context="gallery-page">
      {/* Cache management for dynamic content */}
      <CacheManager addMetaTags={true} updateSW={true} />

      <Header />
      <main className="flex-1 content-overflow-safe">
        <div className="min-h-screen bg-gradient-to-br from-blue-50 via-white to-green-50">
          <div className="max-w-7xl mx-auto px-4 py-8">
            <GalleryListClient initialGalleries={galleries} />
          </div>
        </div>
      </main>
      <Footer />
    </PageErrorBoundary>
  )
}
