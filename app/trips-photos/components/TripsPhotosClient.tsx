'use client';

import { useState } from 'react';
import Image from 'next/image';
import { Camera, Lock, ExternalLink, Eye, EyeOff, AlertCircle, X, AlertTriangle, Mail, RefreshCw } from 'lucide-react';
import { usePublicPhotoAlbums } from '@/hooks/usePhotoAlbums';
import LoadingSpinner from '@/components/ui/LoadingSpinner';

import { TripPhotosDetail } from '@/types/database'

interface Album {
  id: string;
  title: string;
  coverImage: string;
  downloadLink: string;
  description: string;
  password: string | null;
}

interface TripsPhotosClientProps {
  initialAlbums?: Album[];
}

export default function TripsPhotosClient({ initialAlbums = [] }: TripsPhotosClientProps) {
  // Use React Query to fetch albums with real-time updates
  const { data: albumsData, isLoading, error, refetch } = usePublicPhotoAlbums();

  // Use fetched data if available, otherwise fall back to initial data
  const albums: Album[] = albumsData || initialAlbums;
  const [showPasswordModal, setShowPasswordModal] = useState(false);
  const [selectedAlbum, setSelectedAlbum] = useState<Album | null>(null);
  const [password, setPassword] = useState('');
  const [showPassword, setShowPassword] = useState(false);
  const [verifyingPassword, setVerifyingPassword] = useState(false);
  const [passwordError, setPasswordError] = useState('');

  const handlePasswordChange = (value: string) => {
    setPassword(value);
    // Clear error when user starts typing
    if (passwordError) {
      setPasswordError('');
    }
  };

  const togglePasswordVisibility = () => {
    setShowPassword(prev => !prev);
  };

  const handleUnlockClick = (album: Album) => {
    setSelectedAlbum(album);
    setShowPasswordModal(true);
    setPassword('');
    setPasswordError('');
    setShowPassword(false);
  };

  const handleModalClose = () => {
    setShowPasswordModal(false);
    setSelectedAlbum(null);
    setPassword('');
    setPasswordError('');
    setShowPassword(false);
  };

  const verifyPassword = async () => {
    if (!selectedAlbum || !password) {
      setPasswordError('Password is required');
      return;
    }

    setVerifyingPassword(true);
    setPasswordError('');

    try {
      const response = await fetch('/api/trips-photos/verify-password', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          albumId: selectedAlbum.id,
          password,
        }),
      });

      if (response.ok) {
        const data = await response.json();
        if (data.success && data.downloadLink) {
          // Close modal and open the download link
          handleModalClose();
          window.open(data.downloadLink, '_blank');
        } else {
          setPasswordError('Invalid response from server');
        }
      } else {
        const errorData = await response.json();
        setPasswordError(errorData.error?.message || errorData.message || 'Incorrect password');
      }
    } catch (error) {
      console.error('Error verifying password:', error);
      setPasswordError('Failed to verify password. Please try again.');
    } finally {
      setVerifyingPassword(false);
    }
  };

  const handleAlbumAccess = (album: Album) => {
    // Check if manual shareable link is available
    if (!album.downloadLink || album.downloadLink === '#') {
      // No shareable link available - show info message
      return;
    }

    if (album.password) {
      // Password protected - show unlock modal
      handleUnlockClick(album);
    } else {
      // Public album - direct access
      window.open(album.downloadLink, '_blank');
    }
  };

  // Loading state
  if (isLoading && initialAlbums.length === 0) {
    return (
      <div className="container-custom section-padding">
        <div className="flex items-center justify-center py-20">
          <LoadingSpinner size="large" />
        </div>
      </div>
    );
  }

  // Error state
  if (error && albums.length === 0) {
    return (
      <div className="container-custom section-padding">
        <div className="text-center py-20">
          <div className="text-gray-400 mb-4">
            <Camera className="w-16 h-16 mx-auto" />
          </div>
          <h3 className="text-xl font-medium text-gray-900 mb-2">Failed to load photo albums</h3>
          <p className="text-gray-600 mb-4">
            There was an error loading the photo albums. Please try again.
          </p>
          <button
            onClick={() => refetch()}
            className="inline-flex items-center px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 transition-colors"
          >
            <RefreshCw className="w-4 h-4 mr-2" />
            Try Again
          </button>
        </div>
      </div>
    );
  }

  return (
    <div className="container-custom section-padding">
      {/* Hero Section */}
      <div className="text-center mb-16">
        <div className="inline-flex items-center justify-center w-20 h-20 bg-gradient-to-r from-coral-500 to-orange-500 rounded-3xl mb-6 shadow-lg">
          <Camera className="h-10 w-10 text-white" />
        </div>
        <h1 className="hero-text text-gradient-coral mb-6">
          Trip Photo Albums
        </h1>
        <p className="section-subtitle">
          Browse and access photos from our educational trips and adventures.
          Relive the memories and experiences from our amazing journeys with high-quality images
          hosted on Google Photos.
        </p>

        {/* 14-day availability warning */}
        <div className="mt-6 max-w-2xl mx-auto">
          <div className="bg-amber-50 border border-amber-200 rounded-xl p-4">
            <div className="flex items-start">
              <AlertTriangle className="h-5 w-5 text-amber-600 mt-0.5 mr-3 flex-shrink-0" />
              <div>
                <h4 className="text-sm font-medium text-amber-900 mb-1">
                  Limited Availability Notice
                </h4>
                <p className="text-sm text-amber-700">
                  Please note that photo albums will be made unavailable after 14 days from their creation date.
                  Make sure to download your favorite memories before they expire.
                </p>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Albums Grid */}
      {albums.length === 0 ? (
        <div className="card-modern p-16 text-center">
          <div className="inline-flex items-center justify-center w-16 h-16 bg-gray-100 rounded-full mb-6">
            <Camera className="h-8 w-8 text-gray-400" />
          </div>
          <h3 className="text-2xl font-bold text-gray-900 mb-3">No Photo Albums Yet</h3>
          <p className="text-lg text-gray-600 max-w-md mx-auto">
            Photo albums from our trips will appear here. Check back soon for amazing memories!
          </p>
        </div>
      ) : (
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
          {albums.map((album) => (
            <div
              key={album.id}
              className="card-modern overflow-hidden group hover:shadow-2xl transition-all duration-700 transform hover:-translate-y-2"
            >
              {/* Cover Image */}
              <div className="relative h-64 bg-gradient-to-br from-coral-100 to-orange-100 overflow-hidden">
                <Image
                  src={album.coverImage}
                  alt={album.title}
                  fill
                  className="object-cover transition-transform duration-700 group-hover:scale-110"
                  onError={(e) => {
                    (e.target as HTMLImageElement).src = '/images/placeholder.jpg';
                  }}
                />
                {/* Gradient Overlay */}
                <div className="absolute inset-0 bg-gradient-to-t from-black/20 via-transparent to-transparent" />

                {/* Status Icons */}
                <div className="absolute top-4 right-4 flex gap-2">
                  {/* No Shareable Link Warning */}
                  {(!album.downloadLink || album.downloadLink === '#') && (
                    <div className="bg-amber-500/90 backdrop-blur-sm rounded-full p-2.5 border border-white/20">
                      <AlertTriangle className="h-4 w-4 text-white" />
                    </div>
                  )}

                  {/* Password Lock Icon */}
                  {album.password && (
                    <div className="bg-black/50 backdrop-blur-sm rounded-full p-2.5 border border-white/20">
                      <Lock className="h-4 w-4 text-white" />
                    </div>
                  )}
                </div>
              </div>

              {/* Content */}
              <div className="p-8">
                <h3 className="text-2xl font-bold text-gray-900 mb-3 group-hover:text-coral-600 transition-colors duration-300">
                  {album.title}
                </h3>
                <p className="text-gray-600 mb-6 line-clamp-2 leading-relaxed">
                  {album.description}
                </p>

                {/* Password Protection Notice */}
                {album.password && (
                  <div className="mb-6 p-4 bg-yellow-50 border border-yellow-200 rounded-xl">
                    <div className="flex items-center space-x-2 text-yellow-800">
                      <Lock className="h-4 w-4" />
                      <span className="text-sm font-semibold">This album is password protected</span>
                    </div>
                  </div>
                )}

                {/* Check if shareable link is available */}
                {!album.downloadLink || album.downloadLink === '#' ? (
                  /* No Shareable Link Available */
                  <div className="space-y-4">
                    <div className="bg-amber-50 border border-amber-200 rounded-xl p-4">
                      <div className="flex items-start">
                        <AlertTriangle className="h-5 w-5 text-amber-600 mt-0.5 mr-3 flex-shrink-0" />
                        <div>
                          <h4 className="text-sm font-medium text-amber-900 mb-1">
                            Album Setup In Progress
                          </h4>
                          <p className="text-sm text-amber-700">
                            This photo album is currently being set up. The shareable link is not yet available.
                          </p>
                        </div>
                      </div>
                    </div>

                    <button
                      onClick={() => window.location.href = '/contact'}
                      className="w-full inline-flex items-center justify-center px-6 py-4 text-base font-bold rounded-2xl text-white bg-gradient-to-r from-amber-500 to-orange-500 hover:from-amber-600 hover:to-orange-600 focus:outline-none focus:ring-4 focus:ring-amber-500/30 transition-all duration-300 transform hover:scale-105 hover:shadow-xl"
                    >
                      <Mail className="h-5 w-5 mr-3" />
                      Contact Admin
                    </button>
                  </div>
                ) : (
                  /* Shareable Link Available - Normal Access Button */
                  <button
                    onClick={() => handleAlbumAccess(album)}
                    className="w-full inline-flex items-center justify-center px-6 py-4 text-base font-bold rounded-2xl text-white bg-gradient-to-r from-coral-500 to-orange-500 hover:from-coral-600 hover:to-orange-600 focus:outline-none focus:ring-4 focus:ring-coral-500/30 transition-all duration-300 transform hover:scale-105 hover:shadow-xl"
                  >
                    {album.password ? (
                      <>
                        <Lock className="h-5 w-5 mr-3" />
                        Unlock Album
                      </>
                    ) : (
                      <>
                        <ExternalLink className="h-5 w-5 mr-3" />
                        View Photo Album
                      </>
                    )}
                  </button>
                )}
              </div>
            </div>
          ))}
        </div>
      )}

      {/* Info Section */}
      <div className="card-modern p-12 text-center bg-gradient-to-br from-coral-50 via-orange-50 to-teal-50 border-0">
        <div className="inline-flex items-center justify-center w-16 h-16 bg-gradient-to-r from-coral-500 to-orange-500 rounded-full mb-6 shadow-lg">
          <Camera className="h-8 w-8 text-white" />
        </div>
        <h3 className="text-3xl font-bold text-gray-900 mb-4">
          About Our Photo Albums
        </h3>
        <p className="text-lg text-gray-600 max-w-3xl mx-auto leading-relaxed mb-6">
          Our photo albums are hosted on <span className="font-semibold text-coral-600">Google Photos</span>,
          providing you with high-quality images and easy access to download your favorite memories
          from our educational trips. Some albums may be password-protected for privacy and security.
          If an album is still being set up, please contact our admin team for assistance.
        </p>



        <div className="mt-8 flex flex-wrap justify-center gap-4 text-sm text-gray-500">
          <div className="flex items-center space-x-2">
            <div className="w-2 h-2 bg-green-500 rounded-full"></div>
            <span>High Quality Images</span>
          </div>
          <div className="flex items-center space-x-2">
            <div className="w-2 h-2 bg-blue-500 rounded-full"></div>
            <span>Easy Download</span>
          </div>
          <div className="flex items-center space-x-2">
            <div className="w-2 h-2 bg-purple-500 rounded-full"></div>
            <span>Google Photos Hosted</span>
          </div>
          <div className="flex items-center space-x-2">
            <div className="w-2 h-2 bg-purple-500 rounded-full"></div>
            <span>Secure Access</span>
          </div>
          <div className="flex items-center space-x-2">
            <div className="w-2 h-2 bg-amber-500 rounded-full"></div>
            <span>Setup Status Indicators</span>
          </div>
        </div>
      </div>

      {/* Password Modal */}
      {showPasswordModal && selectedAlbum && (
        <div className="fixed inset-0 bg-black/50 backdrop-blur-sm z-50 flex items-center justify-center p-4">
          <div className="bg-white rounded-2xl max-w-md w-full shadow-2xl overflow-hidden">
            {/* Modal Header */}
            <div className="bg-gradient-to-r from-coral-500 to-orange-500 p-6 text-white">
              <div className="flex items-center justify-between">
                <div className="flex items-center gap-3">
                  <div className="w-10 h-10 bg-white/20 rounded-full flex items-center justify-center">
                    <Lock className="w-5 h-5" />
                  </div>
                  <div>
                    <h3 className="text-lg font-bold">Protected Album</h3>
                    <p className="text-coral-100 text-sm">{selectedAlbum.title}</p>
                  </div>
                </div>
                <button
                  onClick={handleModalClose}
                  className="text-white/80 hover:text-white transition-colors"
                  disabled={verifyingPassword}
                >
                  <X className="w-5 h-5" />
                </button>
              </div>
            </div>

            {/* Modal Content */}
            <div className="p-6">
              <div className="mb-6">
                <label htmlFor="modal-password" className="block text-sm font-medium text-gray-700 mb-2">
                  Enter Album Password
                </label>
                <div className="relative">
                  <input
                    id="modal-password"
                    type={showPassword ? 'text' : 'password'}
                    value={password}
                    onChange={(e) => handlePasswordChange(e.target.value)}
                    onKeyDown={(e) => {
                      if (e.key === 'Enter' && !verifyingPassword) {
                        verifyPassword();
                      }
                    }}
                    placeholder="Enter password..."
                    className="w-full px-4 py-3 pr-12 border-2 border-gray-200 rounded-xl focus:outline-none focus:ring-2 focus:ring-coral-500 focus:border-coral-500 transition-all duration-200 bg-gray-50 focus:bg-white"
                    disabled={verifyingPassword}
                    autoFocus
                  />
                  <button
                    type="button"
                    onClick={togglePasswordVisibility}
                    className="absolute inset-y-0 right-0 pr-4 flex items-center text-gray-400 hover:text-gray-600 transition-colors"
                    disabled={verifyingPassword}
                  >
                    {showPassword ? (
                      <EyeOff className="h-5 w-5" />
                    ) : (
                      <Eye className="h-5 w-5" />
                    )}
                  </button>
                </div>
                {passwordError && (
                  <div className="flex items-center space-x-2 text-red-600 mt-2">
                    <AlertCircle className="h-4 w-4" />
                    <p className="text-sm font-medium">{passwordError}</p>
                  </div>
                )}
              </div>
            </div>

            {/* Modal Actions */}
            <div className="px-6 pb-6 flex gap-3 justify-end">
              <button
                onClick={handleModalClose}
                disabled={verifyingPassword}
                className="px-4 py-2 text-gray-600 hover:text-gray-800 font-medium transition-colors disabled:opacity-50"
              >
                Cancel
              </button>
              <button
                onClick={verifyPassword}
                disabled={!password || verifyingPassword}
                className="px-6 py-2 bg-gradient-to-r from-coral-500 to-orange-500 text-white font-bold rounded-xl hover:from-coral-600 hover:to-orange-600 focus:outline-none focus:ring-4 focus:ring-coral-500/30 disabled:opacity-50 disabled:cursor-not-allowed transition-all duration-300 flex items-center"
              >
                {verifyingPassword ? (
                  <>
                    <div className="animate-spin rounded-full h-4 w-4 border-2 border-white border-t-transparent mr-2"></div>
                    Verifying...
                  </>
                ) : (
                  <>
                    <Lock className="h-4 w-4 mr-2" />
                    Unlock Album
                  </>
                )}
              </button>
            </div>
          </div>
        </div>
      )}
    </div>
  );
}
