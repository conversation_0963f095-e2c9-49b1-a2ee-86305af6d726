import { Metadata } from 'next'
import Header from '@/components/layout/Header'
import Footer from '@/components/layout/Footer'
import TripsPhotosClient from './components/TripsPhotosClient'
import { createServerSupabase } from '@/lib/supabase-server'
import { PhotoAlbum } from '@/types/photo-album'
import CacheManager from '@/components/cache/CacheManager'

// Type guard to validate raw album data
function isValidRawAlbum(data: any): data is { id: string; trip_name: string; [key: string]: any } {
  return data &&
         typeof data === 'object' &&
         typeof data.id === 'string' &&
         typeof data.trip_name === 'string';
}

// Utility function to transform raw album data to client format
function transformAlbumData(rawAlbum: any) {
  if (!isValidRawAlbum(rawAlbum)) {
    console.warn('Invalid album data received:', rawAlbum);
    return null;
  }

  // Safely extract and validate fields
  const album: PhotoAlbum = {
    id: rawAlbum.id,
    trip_name: rawAlbum.trip_name,
    trip_description: rawAlbum.trip_description || undefined,
    featured_image_url: rawAlbum.featured_image_url || undefined,
    access_password_hash: rawAlbum.access_password_hash || undefined,
    storage_type: rawAlbum.storage_type || null,
    created_at: rawAlbum.created_at || new Date().toISOString(),
    updated_at: rawAlbum.updated_at || new Date().toISOString(),
    google_photos_album_id: rawAlbum.google_photos_album_id || undefined,
    oauth_user_email: rawAlbum.oauth_user_email || undefined,
    // Note: Refresh tokens are handled server-side only for security
    // Note: Encrypted refresh tokens are handled server-side only for security
    security_version: rawAlbum.security_version || undefined,
    manual_shareable_url: rawAlbum.manual_shareable_url || undefined,
  };

  // Use manual shareable URL only
  const downloadLink = album.manual_shareable_url || '#';

  return {
    id: album.id,
    title: album.trip_name,
    coverImage: album.featured_image_url || 'https://res.cloudinary.com/peebst3r/image/upload/v1748754487/positive7/trips/Manali-River.jpg',
    downloadLink: downloadLink,
    description: album.trip_description || 'Photo album from our educational trip',
    password: album.access_password_hash ? 'protected' : null
  };
}

// Disable caching for real-time data
export const revalidate = 0

export const metadata: Metadata = {
  title: 'Trips Photos - Positive7 Educational Tours',
  description: 'Browse and download photos from our recent educational trips and adventures. Relive the memories and experiences from our amazing journeys.',
  keywords: 'trip photos, educational tour photos, student trip memories, Positive7 gallery, travel photos'
}

// Fetch trip photo albums from the database
async function getTripPhotoAlbums() {
  const supabase = createServerSupabase()

  const { data: tripPhotoAlbums, error } = await supabase
    .from('trip_photos_details')
    .select('*')
    .order('created_at', { ascending: false })

  if (error) {
    console.error('Error fetching trip photo albums:', error)
    return []
  }

  // Transform the data to match the client component's expected format
  return tripPhotoAlbums
    .map(transformAlbumData)
    .filter((album): album is NonNullable<typeof album> => album !== null);
}

export default async function TripsPhotosPage() {
  const albums = await getTripPhotoAlbums()
  
  return (
    <>
      {/* Cache management for dynamic content */}
      <CacheManager addMetaTags={true} updateSW={true} />

      <Header />
      <main className="flex-1">
        <div className="min-h-screen bg-gradient-to-br from-coral-50 via-orange-50 to-teal-50">
          <div className="max-w-7xl mx-auto px-4 py-8">
            <TripsPhotosClient initialAlbums={albums} />
          </div>
        </div>
      </main>
      <Footer />
    </>
  )
}
