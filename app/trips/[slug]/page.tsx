import { notFound } from 'next/navigation'
import { Metadata } from 'next'
import { createServerSupabase } from '@/lib/supabase-server'
import TripDetailClient from '@/components/trips/TripDetailClient'
import { Suspense } from 'react'
import { PageLoading } from '@/components/ui/LoadingStates'
import { ClientTrip, toClientTrip } from '@/types/client-trip'
import Header from '@/components/layout/Header'
import Footer from '@/components/layout/Footer'
import { PageErrorBoundary } from '@/components/error/ComprehensiveErrorBoundary'

interface TripDetailPageProps {
  params: Promise<{
    slug: string
  }>
}

// Force dynamic rendering to prevent caching
export const dynamic = 'force-dynamic'

// Generate metadata for the trip page
export async function generateMetadata({ params }: TripDetailPageProps): Promise<Metadata> {
  const { slug } = await params;
  const trip = await getTripBySlug(slug);
  
  if (!trip) {
    return {
      title: 'Trip Not Found | Positive7',
      description: 'The requested trip could not be found.'
    }
  }

  const baseTitle = `${trip.title} | Educational Tour in ${trip.destination} | Positive7`
  const description = trip.description 
    ? `${trip.description.substring(0, 160)}...` 
    : `Explore ${trip.destination} with Positive7. ${trip.days} days educational journey with ${trip.is_trek ? 'trekking and adventure' : 'immersive learning'} experiences.`

  const keywords = [
    trip.destination,
    'educational tour',
    trip.is_trek ? 'trekking' : '',
    trip.is_trek ? 'adventure' : '',
    'travel',
    `${trip.days} days tour`,
    'student trip',
    'Positive7',
    trip.difficulty,
    'educational journey'
  ].filter(Boolean).join(', ')

  return {
    title: baseTitle,
    description: description,
    keywords: keywords,
    openGraph: {
      title: trip.title,
      description: description,
      type: 'article',
      url: `https://positive7.org/trips/${trip.slug}`,
      images: trip.featured_image_url ? [trip.featured_image_url] : undefined
    },
    twitter: {
      card: 'summary_large_image',
      title: trip.title,
      description: description,
      images: trip.featured_image_url ? [trip.featured_image_url] : undefined
    }
  }
}

// Get trip by slug
async function getTripBySlug(slug: string): Promise<ClientTrip | null> {
  const supabase = createServerSupabase()

  const { data: trip, error } = await supabase
    .from('trips')
    .select('*')
    .eq('slug', slug)
    .eq('is_active', true)
    .single()

  if (error) {
    console.error('Error fetching trip by slug:', error)
    return null
  }

  // Transform to client trip (excludes internal fields)
  return toClientTrip(trip)
}

// Get related trips (excluding current trip)
async function getRelatedTrips(currentTripId: string, destination: string, limit = 3): Promise<ClientTrip[]> {
  const supabase = createServerSupabase()

  // First try to get trips with the same destination
  const { data: relatedTrips, error } = await supabase
    .from('trips')
    .select('*')
    .eq('is_active', true)
    .eq('destination', destination)
    .neq('id', currentTripId)
    .limit(limit)

  if (error || !relatedTrips) {
    console.error('Error fetching related trips:', error)
    return []
  }

  // If we didn't get enough trips, fetch more regardless of destination
  let combinedTrips = [...relatedTrips];
  if (combinedTrips.length < limit) {
    const { data: moreTrips, error: moreError } = await supabase
      .from('trips')
      .select('*')
      .eq('is_active', true)
      .neq('id', currentTripId)
      .limit(limit - combinedTrips.length)

    if (!moreError && moreTrips) {
      combinedTrips = [...combinedTrips, ...moreTrips]
    }
  }

  // Transform to client trips (excludes internal fields)
  return combinedTrips.map(trip => toClientTrip(trip))
}

export default async function TripDetailPage({ params }: TripDetailPageProps) {
  const { slug } = await params;
  
  // Fetch trip data from the database
  const trip = await getTripBySlug(slug);
  
  // If trip not found, return 404
  if (!trip) {
    notFound();
  }
  
  // Fetch related trips
  const relatedTrips = await getRelatedTrips(trip.id, trip.destination);

  return (
    <PageErrorBoundary context="trip-detail-page">
      <Header />
      <main className="flex-1">
        <div className="min-h-screen bg-gradient-to-br from-coral-50 via-orange-50 to-teal-50">
          <Suspense fallback={<PageLoading message="Loading trip details..." />}>
            <TripDetailClient tripSlug={slug} initialTrip={trip} relatedTrips={relatedTrips} />
          </Suspense>
        </div>
      </main>
      <Footer />
    </PageErrorBoundary>
  );
}
