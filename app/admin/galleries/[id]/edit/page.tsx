'use client';

import { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import Link from 'next/link';
import AdminLayout from '@/components/layout/AdminLayout';
import GalleryForm from '../../components/GalleryForm';
import LoadingSpinner from '@/components/ui/LoadingSpinner';
import { GalleryFormData, Gallery } from '@/types/gallery';
import { ArrowLeft } from 'lucide-react';
import { useToast } from '@/hooks/useToast';
import { useGallery, useUpdateGallery } from '@/hooks/useGalleries';

interface EditGalleryPageProps {
  params: Promise<{ id: string }>;
}

export default function EditGalleryPage({ params }: EditGalleryPageProps) {
  const router = useRouter();
  const toast = useToast();
  const [galleryId, setGalleryId] = useState<string>('');

  useEffect(() => {
    params.then(({ id }) => setGalleryId(id));
  }, [params]);

  // Use React Query hooks
  const { data: gallery, isLoading: loading, error } = useGallery(galleryId, !!galleryId);
  const updateGalleryMutation = useUpdateGallery();

  const handleSubmit = async (data: GalleryFormData) => {
    if (!gallery) return;

    const toastId = toast.loading('Updating gallery...');

    try {
      await updateGalleryMutation.mutateAsync({ ...data, id: gallery.id });
      toast.success('Gallery updated successfully!');
      router.push('/admin/galleries');
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Failed to update gallery';
      toast.error(errorMessage);
      throw error; // Re-throw to let the form handle it
    } finally {
      toast.dismiss(toastId);
    }
  };

  if (loading || !galleryId) {
    return (
      <AdminLayout>
        <div className="flex justify-center items-center h-64">
          <LoadingSpinner size="large" />
        </div>
      </AdminLayout>
    );
  }

  if (error || !gallery) {
    return (
      <AdminLayout>
        <div className="text-center py-12">
          <h3 className="text-lg font-medium text-gray-900 mb-2">Gallery not found</h3>
          <p className="text-gray-500 mb-4">{error instanceof Error ? error.message : String(error) || 'The requested gallery could not be found.'}</p>
          <Link href="/admin/galleries">
            <button className="bg-blue-600 text-white px-4 py-2 rounded-md hover:bg-blue-700">
              Back to Galleries
            </button>
          </Link>
        </div>
      </AdminLayout>
    );
  }

  // Convert gallery to form data
  const formData: Partial<GalleryFormData> = {
    name: gallery.name,
    description: gallery.description || '',
    trip_id: gallery.trip_id || '',
    folder_name: gallery.folder_name || '',
    is_active: gallery.is_active ?? true, // Convert null to true (default active)
    featured_image_url: gallery.featured_image_url || '',
  };

  return (
    <AdminLayout>
      <div className="mb-6">
        <Link
          href="/admin/galleries"
          className="inline-flex items-center text-sm text-gray-500 hover:text-gray-700 mb-4"
        >
          <ArrowLeft className="h-4 w-4 mr-1" />
          Back to Galleries
        </Link>
        <h1 className="text-3xl font-bold text-gray-900">Edit Gallery</h1>
        <p className="text-gray-600 mt-1">Make changes to the gallery settings and information</p>
      </div>

      <div className="bg-white shadow rounded-lg p-6">
        <GalleryForm
          initialData={formData}
          onSubmit={handleSubmit}
          isLoading={updateGalleryMutation.isPending}
        />
      </div>
    </AdminLayout>
  );
}
