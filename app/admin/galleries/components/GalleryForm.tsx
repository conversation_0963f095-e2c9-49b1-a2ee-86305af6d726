'use client';

import { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import Button from '@/components/ui/Button';
import LoadingSpinner from '@/components/ui/LoadingSpinner';
import AdminLoadingSpinner from '@/components/ui/AdminLoadingSpinner';
import CloudinaryUpload from '@/components/ui/CloudinaryUpload';
import { GalleryFormData } from '@/types/gallery';
import { Camera, MapPin, FileText, ToggleLeft, ToggleRight } from 'lucide-react';
import { useTrips } from '@/hooks/useTrips';
import { useUpload } from '@/contexts/UploadContext';
import { extractCloudinaryPublicId } from '@/lib/utils/parsing';

interface Trip {
  id: string;
  title: string;
  destination: string;
}

interface GalleryFormProps {
  initialData?: Partial<GalleryFormData>;
  onSubmit: (data: GalleryFormData) => Promise<void>;
  isLoading: boolean;
}

export default function GalleryForm({ initialData, onSubmit, isLoading }: GalleryFormProps) {
  const router = useRouter();
  const { isAnyUploading } = useUpload();
  const [uploadedImagePublicId, setUploadedImagePublicId] = useState<string | null>(null);
  const [formSubmittedSuccessfully, setFormSubmittedSuccessfully] = useState(false);
  const [formData, setFormData] = useState<GalleryFormData>({
    name: '',
    description: '',
    trip_id: '',
    folder_name: '',
    is_active: true,
    featured_image_url: '',
    ...initialData,
  });

  const [error, setError] = useState<string | null>(null);

  // Use React Query to fetch trips
  const { data: tripsData, isLoading: loadingTrips } = useTrips({ limit: 100 });
  const trips = tripsData?.data || [];

  // Auto-generate folder name from gallery name
  useEffect(() => {
    if (formData.name && !initialData?.folder_name) {
      const folderName = formData.name
        .toLowerCase()
        .replace(/[^a-z0-9]+/g, '-')
        .replace(/(^-|-$)/g, '');
      setFormData(prev => ({ ...prev, folder_name: folderName }));
    }
  }, [formData.name, initialData?.folder_name]);

  const handleChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement | HTMLSelectElement>) => {
    const { name, value } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: value,
    }));
  };

  const handleToggleActive = () => {
    setFormData(prev => ({
      ...prev,
      is_active: !prev.is_active,
    }));
  };

  const handleImageUpload = (url: string) => {
    setFormData(prev => ({
      ...prev,
      featured_image_url: url,
    }));

    // Track uploaded image for potential cleanup using robust parsing
    const publicId = extractCloudinaryPublicId(url);
    setUploadedImagePublicId(publicId);
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setError(null);

    try {
      // Clean up form data
      const submitData: GalleryFormData = {
        ...formData,
        trip_id: formData.trip_id || undefined,
        description: formData.description || undefined,
        featured_image_url: formData.featured_image_url || undefined,
      };

      await onSubmit(submitData);
      setFormSubmittedSuccessfully(true);
      setUploadedImagePublicId(null); // Clear tracking since form was saved successfully
      router.push('/admin/galleries');
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to save gallery';
      setError(errorMessage);
    }
  };

  return (
    <form onSubmit={handleSubmit} className="space-y-6">
      {error && (
        <div className="bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded-md">
          {error}
        </div>
      )}

      {/* Gallery Name */}
      <div>
        <label htmlFor="name" className="block text-sm font-medium text-gray-700 mb-2">
          Gallery Name *
        </label>
        <div className="relative">
          <Camera className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
          <input
            type="text"
            id="name"
            name="name"
            value={formData.name}
            onChange={handleChange}
            required
            className="pl-10 w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500"
            placeholder="Enter gallery name"
          />
        </div>
      </div>

      {/* Description */}
      <div>
        <label htmlFor="description" className="block text-sm font-medium text-gray-700 mb-2">
          Description
        </label>
        <div className="relative">
          <FileText className="absolute left-3 top-3 text-gray-400 h-4 w-4" />
          <textarea
            id="description"
            name="description"
            value={formData.description}
            onChange={handleChange}
            rows={4}
            className="pl-10 w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500"
            placeholder="Enter gallery description"
          />
        </div>
      </div>

      {/* Trip Association */}
      <div>
        <label htmlFor="trip_id" className="block text-sm font-medium text-gray-700 mb-2">
          Associated Trip (Optional)
        </label>
        <div className="relative">
          <MapPin className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
          <select
            id="trip_id"
            name="trip_id"
            value={formData.trip_id}
            onChange={handleChange}
            className="pl-10 w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500"
            disabled={loadingTrips}
          >
            <option value="">Select a trip (optional)</option>
            {trips.map((trip) => (
              <option key={trip.id} value={trip.id}>
                {trip.title} - {trip.destination}
              </option>
            ))}
          </select>
        </div>
        {loadingTrips && (
          <div className="mt-2">
            <AdminLoadingSpinner
              size="small"
              variant="dots"
              message="Loading trips..."
              color="primary"
              showIcon={false}
            />
          </div>
        )}
      </div>

      {/* Folder Name */}
      <div>
        <label htmlFor="folder_name" className="block text-sm font-medium text-gray-700 mb-2">
          Cloudinary Folder Name
        </label>
        <input
          type="text"
          id="folder_name"
          name="folder_name"
          value={formData.folder_name}
          onChange={handleChange}
          className="w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500"
          placeholder="Auto-generated from gallery name"
        />
        <p className="text-sm text-gray-500 mt-1">
          This will be used to organize images in Cloudinary. Leave empty to auto-generate.
        </p>
      </div>

      {/* Featured Image */}
      <div>
        <label className="block text-sm font-medium text-gray-700 mb-2">
          Featured Image
        </label>
        <CloudinaryUpload
          onUpload={handleImageUpload}
          currentImage={formData.featured_image_url}
          folder={`positive7/galleries/${formData.folder_name || 'general'}`}
          uploadType="general"
          placeholder="Upload a featured image for this gallery"
        />
      </div>

      {/* Active Status */}
      <div>
        <label className="block text-sm font-medium text-gray-700 mb-2">
          Gallery Status
        </label>
        <div className="flex items-center space-x-3">
          <button
            type="button"
            onClick={handleToggleActive}
            className={`flex items-center space-x-2 px-4 py-2 rounded-md transition-colors ${
              formData.is_active
                ? 'bg-green-100 text-green-800 hover:bg-green-200'
                : 'bg-gray-100 text-gray-600 hover:bg-gray-200'
            }`}
          >
            {formData.is_active ? (
              <ToggleRight className="h-5 w-5" />
            ) : (
              <ToggleLeft className="h-5 w-5" />
            )}
            <span>{formData.is_active ? 'Active' : 'Inactive'}</span>
          </button>
        </div>
        <p className="text-sm text-gray-500 mt-1">
          {formData.is_active 
            ? 'Gallery is visible to the public' 
            : 'Gallery is hidden from the public'
          }
        </p>
      </div>

      {/* Form Actions */}
      <div className="flex justify-end space-x-4 pt-6 border-t border-gray-200">
        <Button
          type="button"
          variant="outline"
          onClick={async () => {
            // Only cleanup uploaded image if form is being cancelled (not saved) and it's a new gallery
            if (uploadedImagePublicId && !formSubmittedSuccessfully && formData.featured_image_url?.includes('cloudinary.com')) {
              try {
                await fetch('/api/admin/cloudinary/cleanup-single', {
                  method: 'POST',
                  headers: { 'Content-Type': 'application/json' },
                  body: JSON.stringify({
                    publicId: uploadedImagePublicId,
                    context: 'form-cancelled',
                    shouldCleanupFolder: true
                  })
                });
                console.log(`✅ Cleaned up cancelled gallery image: ${uploadedImagePublicId}`);
              } catch (error) {
                console.error(`❌ Failed to cleanup cancelled gallery image: ${uploadedImagePublicId}`, error);
              }
            }
            router.push('/admin/galleries');
          }}
          disabled={isLoading}
        >
          Cancel
        </Button>
        <Button
          type="submit"
          disabled={isLoading || !formData.name.trim() || isAnyUploading}
        >
          {isLoading ? (
            <>
              <AdminLoadingSpinner size="small" variant="spinner" showIcon={false} className="mr-2" />
              Saving...
            </>
          ) : isAnyUploading ? (
            <>
              <AdminLoadingSpinner size="small" variant="dots" showIcon={false} className="mr-2" />
              Uploading images...
            </>
          ) : (
            'Save Gallery'
          )}
        </Button>
      </div>
    </form>
  );
}
