'use client';

import { useRouter } from 'next/navigation';
import Link from 'next/link';
import AdminLayout from '@/components/layout/AdminLayout';
import GalleryForm from '../components/GalleryForm';
import { GalleryFormData } from '@/types/gallery';
import { ArrowLeft } from 'lucide-react';
import { useToast } from '@/hooks/useToast';
import { useCreateGallery } from '@/hooks/useGalleries';

export default function NewGalleryPage() {
  const router = useRouter();
  const toast = useToast();
  const createGalleryMutation = useCreateGallery();

  const handleSubmit = async (data: GalleryFormData) => {
    const toastId = toast.loading('Creating gallery...');

    try {
      await createGalleryMutation.mutateAsync(data);
      toast.success('Gallery created successfully!');
      router.push('/admin/galleries');
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Failed to create gallery';
      toast.error(errorMessage);
      throw error; // Re-throw to let the form handle it
    } finally {
      toast.dismiss(toastId);
    }
  };

  return (
    <AdminLayout>
      <div className="mb-6">
        <Link
          href="/admin/galleries"
          className="inline-flex items-center text-sm text-gray-500 hover:text-gray-700 mb-4"
        >
          <ArrowLeft className="h-4 w-4 mr-1" />
          Back to Galleries
        </Link>
        <h1 className="text-3xl font-bold text-gray-900">Create New Gallery</h1>
        <p className="text-gray-600 mt-1">Create a new photo gallery for your trips and experiences</p>
      </div>

      <div className="bg-white shadow rounded-lg p-6">
        <GalleryForm onSubmit={handleSubmit} isLoading={createGalleryMutation.isPending} />
      </div>
    </AdminLayout>
  );
}
