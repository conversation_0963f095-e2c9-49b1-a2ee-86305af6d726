'use client';

import {
  <PERSON><PERSON><PERSON>Up,
  <PERSON>,
  Refresh<PERSON>w,
  Trash2,
  Image as ImageIcon,
  FileText,
  MessageSquare
} from 'lucide-react';
import { useAuth } from '@/components/providers/AuthProvider';
import { useToast } from '@/hooks/useToast';
import AdminLayout from '@/components/layout/AdminLayout';
import { PageErrorBoundary, SectionErrorBoundary } from '@/components/error/ComprehensiveErrorBoundary';
import AutoDeactivationMonitor from '@/components/admin/AutoDeactivationMonitor';
import AdminLoadingSpinner, { AdminLoadingPresets } from '@/components/ui/AdminLoadingSpinner';

export default function AdminDashboardPage() {
  const { adminUser, loading, isSuperAdmin, hasPermission } = useAuth();
  const toast = useToast();

  const clearAllCaches = async () => {
    try {
      toast.loading('Clearing caches...');

      const clearedCaches = [];

      // Clear browser caches
      if (typeof window !== 'undefined' && 'caches' in window) {
        const cacheNames = await caches.keys();
        await Promise.all(
          cacheNames.map(cacheName => {
            clearedCaches.push(cacheName);
            return caches.delete(cacheName);
          })
        );
      }

      // Update service worker
      if (typeof window !== 'undefined' && 'serviceWorker' in navigator) {
        const registration = await navigator.serviceWorker.getRegistration();
        if (registration) {
          await registration.update();
          clearedCaches.push('Service Worker updated');
        }
      }

      toast.success(`Cleared ${clearedCaches.length} cache(s). Page will refresh in 2 seconds.`);

      // Refresh page after 2 seconds
      setTimeout(() => {
        window.location.reload();
      }, 2000);

    } catch (error) {
      console.error('Error clearing caches:', error);
      toast.error('Failed to clear some caches. Try refreshing manually (Ctrl+F5).');
    }
  };

  // Show loading while auth is loading
  if (loading) {
    return (
      <AdminLayout>
        <div className="min-h-screen flex items-center justify-center">
          <AdminLoadingSpinner
            {...AdminLoadingPresets.pageLoading}
          />
        </div>
      </AdminLayout>
    );
  }



  return (
    <PageErrorBoundary context="admin-dashboard">
      <AdminLayout>
        {/* Modern Header with Gradient */}
        <SectionErrorBoundary context="admin-dashboard-header">
          <div className="mb-8 relative">
            <div className="bg-gradient-to-r from-purple-600 via-blue-600 to-indigo-600 rounded-2xl p-8 text-white shadow-xl">
              <div className="flex items-center justify-between">
                <div>
                  <h1 className="text-4xl font-bold mb-2">
                    Welcome back, {adminUser?.full_name || adminUser?.username || 'Admin'}! 👋
                  </h1>
                  <p className="text-purple-100 text-lg">
                    Monitor your auto deactivation system
                  </p>
                  {isSuperAdmin() && (
                    <div className="flex gap-2 mt-3">
                      <button
                        onClick={clearAllCaches}
                        className="px-4 py-2 bg-white/20 hover:bg-white/30 rounded-lg text-sm font-medium transition-colors duration-200 backdrop-blur-sm border border-white/20 flex items-center gap-2"
                      >
                        <Trash2 className="w-4 h-4" />
                        Clear Cache
                      </button>
                    </div>
                  )}
                </div>
                <div className="hidden md:block">
                  <div className="w-24 h-24 bg-white/20 rounded-full flex items-center justify-center backdrop-blur-sm">
                    <TrendingUp className="w-12 h-12 text-white" />
                  </div>
                </div>
              </div>
              <div className="flex flex-wrap gap-2 mt-4">
                {adminUser?.roles.map(role => (
                  <span
                    key={role.name}
                    className="inline-flex items-center px-3 py-1 rounded-full text-sm font-medium bg-white/20 text-white backdrop-blur-sm"
                  >
                    {role.name.replace('_', ' ').toUpperCase()}
                  </span>
                ))}
              </div>
            </div>
          </div>
        </SectionErrorBoundary>

        {/* Conditional Dashboard Content */}
        <SectionErrorBoundary context="admin-dashboard-content">
          {hasPermission('trips', 'read') ? (
            // Show Auto Deactivation Monitor for users with trip access
            <AutoDeactivationMonitor />
          ) : (
            // Show generic welcome content for users without trip access
            <div className="space-y-6">
              {/* Welcome Card */}
              <div className="bg-white rounded-2xl shadow-lg p-8">
                <div className="text-center">
                  <div className="w-16 h-16 bg-gradient-to-r from-blue-500 to-purple-600 rounded-full flex items-center justify-center mx-auto mb-4">
                    <span className="text-2xl">👋</span>
                  </div>
                  <h2 className="text-2xl font-bold text-gray-900 mb-2">
                    {new Date().getHours() < 12 ? 'Good Morning' :
                     new Date().getHours() < 18 ? 'Good Afternoon' : 'Good Evening'}, {adminUser?.full_name || adminUser?.username || 'Admin'}!
                  </h2>
                  <p className="text-gray-600 mb-6">
                    Welcome to your admin dashboard. Use the sidebar to navigate to the sections you have access to.
                  </p>
                  <div className="text-sm text-gray-500">
                    Current time: {new Date().toLocaleString()}
                  </div>
                </div>
              </div>

              {/* Quick Actions Card */}
              <div className="bg-white rounded-2xl shadow-lg p-8">
                <h3 className="text-xl font-semibold text-gray-900 mb-4">Quick Actions</h3>
                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                  {hasPermission('trip_photos', 'read') && (
                    <div className="p-4 border border-gray-200 rounded-lg hover:bg-gray-50 transition-colors">
                      <div className="flex items-center gap-3">
                        <div className="w-10 h-10 bg-blue-100 rounded-lg flex items-center justify-center">
                          <ImageIcon className="w-5 h-5 text-blue-600" />
                        </div>
                        <div>
                          <h4 className="font-medium text-gray-900">Trip Photos</h4>
                          <p className="text-sm text-gray-600">Manage photo albums</p>
                        </div>
                      </div>
                    </div>
                  )}

                  {hasPermission('blog', 'read') && (
                    <div className="p-4 border border-gray-200 rounded-lg hover:bg-gray-50 transition-colors">
                      <div className="flex items-center gap-3">
                        <div className="w-10 h-10 bg-green-100 rounded-lg flex items-center justify-center">
                          <FileText className="w-5 h-5 text-green-600" />
                        </div>
                        <div>
                          <h4 className="font-medium text-gray-900">Blogs</h4>
                          <p className="text-sm text-gray-600">Create and edit blog posts</p>
                        </div>
                      </div>
                    </div>
                  )}

                  {hasPermission('inquiries', 'read') && (
                    <div className="p-4 border border-gray-200 rounded-lg hover:bg-gray-50 transition-colors">
                      <div className="flex items-center gap-3">
                        <div className="w-10 h-10 bg-purple-100 rounded-lg flex items-center justify-center">
                          <MessageSquare className="w-5 h-5 text-purple-600" />
                        </div>
                        <div>
                          <h4 className="font-medium text-gray-900">Inquiries</h4>
                          <p className="text-sm text-gray-600">View customer inquiries</p>
                        </div>
                      </div>
                    </div>
                  )}
                </div>
              </div>
            </div>
          )}
        </SectionErrorBoundary>
      </AdminLayout>
    </PageErrorBoundary>
  );
}