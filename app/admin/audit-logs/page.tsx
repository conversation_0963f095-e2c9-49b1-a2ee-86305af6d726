'use client';

import React, { useState } from 'react';
import AdminLayout from '@/components/layout/AdminLayout';
import Button from '@/components/ui/Button';
import LoadingSpinner from '@/components/ui/LoadingSpinner';
import { useToast } from '@/hooks/useToast';
import {
  useAuditLogs,
  useAuditLogStatistics,
  exportAuditLogs,
  type AuditLogQueryParams,
  type AuditLogEntry,
  type AuditStatistics
} from '@/hooks/useAuditLogs';
import { 
  Search, 
  Filter, 
  Download, 
  RefreshCw,
  AlertTriangle,
  Shield,
  User,
  Clock,
  Eye,
  ChevronDown,
  ChevronUp
} from 'lucide-react';
import { PageErrorBoundary, SectionErrorBoundary } from '@/components/error/ComprehensiveErrorBoundary';

// Types are now imported from the hook

export default function AuditLogsPage() {
  const toast = useToast();
  const [expandedLog, setExpandedLog] = useState<string | null>(null);

  // Filters
  const [filters, setFilters] = useState<AuditLogQueryParams>({
    event_type: '',
    severity: '',
    success: undefined, // Fix: should be boolean or undefined, not string
    days: '7',
    page: 1,
    limit: 50,
  });

  // Use React Query hooks
  const {
    data: auditLogsData,
    isLoading: logsLoading,
    error: logsError,
    refetch: refetchLogs
  } = useAuditLogs(filters);

  const {
    data: statistics,
    isLoading: statsLoading,
    refetch: refetchStats
  } = useAuditLogStatistics(filters.days || '7');

  // Derived data
  const logs = auditLogsData?.logs || [];
  const pagination = auditLogsData?.pagination || { page: 1, limit: 50, total: 0, totalPages: 0 };
  const loading = logsLoading || statsLoading;
  const error = logsError;

  const handleFilterChange = (key: string, value: string) => {
    let processedValue: any = value;

    // Convert success filter to boolean
    if (key === 'success') {
      if (value === 'true') processedValue = true;
      else if (value === 'false') processedValue = false;
      else processedValue = undefined;
    }

    setFilters(prev => ({ ...prev, [key]: processedValue, page: 1 })); // Reset to first page
  };

  const handleRefresh = async () => {
    try {
      await Promise.all([refetchLogs(), refetchStats()]);
      toast.success('Audit logs refreshed successfully');
    } catch (error) {
      toast.error('Failed to refresh audit logs');
    }
  };

  const handleExport = async () => {
    try {
      await exportAuditLogs(filters);
      toast.success('Audit logs exported successfully');
    } catch (error) {
      toast.error('Failed to export audit logs');
    }
  };

  const getSeverityColor = (severity: string) => {
    switch (severity) {
      case 'critical': return 'text-red-600 bg-red-50';
      case 'high': return 'text-orange-600 bg-orange-50';
      case 'medium': return 'text-yellow-600 bg-yellow-50';
      case 'low': return 'text-green-600 bg-green-50';
      default: return 'text-gray-600 bg-gray-50';
    }
  };

  const getEventTypeIcon = (eventType: string) => {
    if (eventType.includes('login')) return <User className="h-4 w-4" />;
    if (eventType.includes('security')) return <Shield className="h-4 w-4" />;
    if (eventType.includes('admin')) return <AlertTriangle className="h-4 w-4" />;
    return <Eye className="h-4 w-4" />;
  };

  const formatTimestamp = (timestamp: string) => {
    return new Date(timestamp).toLocaleString();
  };

  const formatExplicitAction = (log: any) => {
    const user = log.user_email || 'Unknown User';
    const action = log.action || log.event_type;

    // Handle CRUD operations
    if (action.includes('create_')) {
      const resource = action.replace('create_', '').replace('_', ' ');
      return `${user} created a new ${resource}`;
    }

    if (action.includes('update_')) {
      const resource = action.replace('update_', '').replace('_', ' ');
      return `${user} updated ${resource}`;
    }

    if (action.includes('delete_')) {
      const resource = action.replace('delete_', '').replace('_', ' ');
      return `${user} deleted ${resource}`;
    }

    if (action.includes('read_') || action.includes('_read')) {
      const resource = action.replace('read_', '').replace('_read', '').replace('_', ' ');
      return `${user} accessed ${resource}`;
    }

    // Handle bulk operations
    if (action.includes('bulk_')) {
      const operation = action.replace('bulk_', '');
      const count = log.metadata?.total_items || 'multiple';
      return `${user} performed bulk ${operation} on ${count} items`;
    }

    // Handle specific event types
    switch (log.event_type) {
      case 'user_login':
        return `${user} logged in successfully`;
      case 'user_login_failed':
        return `Failed login attempt for ${user}`;
      case 'admin_access':
        return `${user} accessed admin area`;
      case 'security_event':
        return `Security event triggered by ${user}`;
      case 'rate_limit_exceeded':
        return `Rate limit exceeded for ${user}`;
      default:
        return `${user} performed ${action.replace(/_/g, ' ')}`;
    }
  };

  const formatExplicitDescription = (log: any) => {
    const metadata = log.metadata || {};

    // Handle CRUD operations with changes
    if (metadata.changes && Object.keys(metadata.changes).length > 0) {
      const changes = Object.keys(metadata.changes);
      return `Modified: ${changes.join(', ')}`;
    }

    // Handle creation with details
    if (metadata.created_data || metadata.created_at) {
      const details = [];
      if (metadata.created_user_email) details.push(`Email: ${metadata.created_user_email}`);
      if (metadata.assigned_roles) details.push(`Roles: ${metadata.assigned_roles.join(', ')}`);
      if (metadata.created_data?.name) details.push(`Name: ${metadata.created_data.name}`);
      return details.length > 0 ? details.join(' • ') : 'New item created';
    }

    // Handle access operations
    if (metadata.accessed_at) {
      const details = [];
      if (metadata.total_count) details.push(`${metadata.total_count} items`);
      if (metadata.page) details.push(`Page ${metadata.page}`);
      if (metadata.search_query) details.push(`Search: "${metadata.search_query}"`);
      return details.length > 0 ? details.join(' • ') : 'Data accessed';
    }

    // Handle bulk operations
    if (metadata.total_items) {
      const details = [];
      details.push(`${metadata.total_items} items processed`);
      if (metadata.success_count) details.push(`${metadata.success_count} successful`);
      if (metadata.error_count) details.push(`${metadata.error_count} failed`);
      return details.join(' • ');
    }

    // Handle errors
    if (log.error_message) {
      return `Error: ${log.error_message}`;
    }

    // Handle login events
    if (log.event_type === 'user_login' || log.event_type === 'user_login_failed') {
      const details = [];
      if (log.ip_address) details.push(`IP: ${log.ip_address}`);
      if (metadata.user_agent) details.push(`Browser: ${metadata.user_agent.split(' ')[0]}`);
      return details.join(' • ');
    }

    // Default description
    return log.description || 'No additional details available';
  };



  return (
    <PageErrorBoundary context="admin-audit-logs">
      <AdminLayout>
        <div className="space-y-6">
          {/* Header */}
          <SectionErrorBoundary context="admin-audit-logs-header">
            <div className="flex justify-between items-center">
              <div>
                <h1 className="text-2xl font-bold text-gray-900">Security Audit Logs</h1>
                <p className="text-gray-600">Monitor and review system security events</p>
              </div>
              <div className="flex space-x-3">
                <Button
                  onClick={handleExport}
                  variant="outline"
                  className="flex items-center space-x-2"
                >
                  <Download className="h-4 w-4" />
                  <span>Export</span>
                </Button>
                <Button
                  onClick={handleRefresh}
                  variant="outline"
                  className="flex items-center space-x-2"
                >
                  <RefreshCw className="h-4 w-4" />
                  <span>Refresh</span>
                </Button>
              </div>
            </div>
          </SectionErrorBoundary>

        {/* Statistics Cards */}
        {statistics && (
          <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
            <div className="bg-white p-4 rounded-lg border">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm text-gray-600">Total Events</p>
                  <p className="text-2xl font-bold">{statistics.totalEvents}</p>
                </div>
                <Eye className="h-8 w-8 text-blue-500" />
              </div>
            </div>
            <div className="bg-white p-4 rounded-lg border">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm text-gray-600">Failed Events</p>
                  <p className="text-2xl font-bold text-red-600">{statistics.failedEvents}</p>
                </div>
                <AlertTriangle className="h-8 w-8 text-red-500" />
              </div>
            </div>
            <div className="bg-white p-4 rounded-lg border">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm text-gray-600">Critical Events</p>
                  <p className="text-2xl font-bold text-orange-600">
                    {statistics.eventsBySeverity.critical || 0}
                  </p>
                </div>
                <Shield className="h-8 w-8 text-orange-500" />
              </div>
            </div>
            <div className="bg-white p-4 rounded-lg border">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm text-gray-600">Active Users</p>
                  <p className="text-2xl font-bold">{statistics.topUsers.length}</p>
                </div>
                <User className="h-8 w-8 text-green-500" />
              </div>
            </div>
          </div>
        )}

        {/* Filters */}
        <div className="bg-white p-4 rounded-lg border">
          <div className="grid grid-cols-1 md:grid-cols-5 gap-4">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Time Period
              </label>
              <select
                value={filters.days}
                onChange={(e) => handleFilterChange('days', e.target.value)}
                className="w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500"
              >
                <option value="1">Last 24 hours</option>
                <option value="7">Last 7 days</option>
                <option value="30">Last 30 days</option>
                <option value="90">Last 90 days</option>
              </select>
            </div>
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Event Type
              </label>
              <select
                value={filters.event_type}
                onChange={(e) => handleFilterChange('event_type', e.target.value)}
                className="w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500"
              >
                <option value="">All Types</option>
                <option value="user_login">User Login</option>
                <option value="user_login_failed">Login Failed</option>
                <option value="admin_access">Admin Access</option>
                <option value="security_event">Security Event</option>
                <option value="rate_limit_exceeded">Rate Limit</option>
              </select>
            </div>
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Severity
              </label>
              <select
                value={filters.severity}
                onChange={(e) => handleFilterChange('severity', e.target.value)}
                className="w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500"
              >
                <option value="">All Severities</option>
                <option value="low">Low</option>
                <option value="medium">Medium</option>
                <option value="high">High</option>
                <option value="critical">Critical</option>
              </select>
            </div>
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Status
              </label>
              <select
                value={filters.success === true ? 'true' : filters.success === false ? 'false' : ''}
                onChange={(e) => handleFilterChange('success', e.target.value)}
                className="w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500"
              >
                <option value="">All</option>
                <option value="true">Success</option>
                <option value="false">Failed</option>
              </select>
            </div>
            <div className="flex items-end">
              <Button
                onClick={() => {
                  setFilters({
                    event_type: '',
                    severity: '',
                    success: undefined, // Fix: should be undefined, not string
                    days: '7',
                    page: 1,
                    limit: 50,
                  });
                }}
                variant="outline"
                className="w-full"
              >
                Clear Filters
              </Button>
            </div>
          </div>
        </div>

        {/* Error Message */}
        {error && (
          <div className="bg-red-50 border border-red-200 rounded-md p-4">
            <div className="flex">
              <AlertTriangle className="h-5 w-5 text-red-400" />
              <div className="ml-3">
                <h3 className="text-sm font-medium text-red-800">Error</h3>
                <p className="text-sm text-red-700 mt-1">{error?.message || 'An error occurred'}</p>
              </div>
            </div>
          </div>
        )}

        {/* Loading State */}
        {loading && (
          <div className="flex justify-center py-8">
            <LoadingSpinner />
          </div>
        )}

        {/* Audit Logs Table */}
        {!loading && (
          <div className="bg-white rounded-lg border overflow-auto">
              <table className="min-w-full divide-y divide-gray-200">
                <thead className="bg-gray-50">
                  <tr>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Event
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Severity
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      User
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      IP Address
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Timestamp
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Status
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Actions
                    </th>
                  </tr>
                </thead>
                <tbody className="bg-white divide-y divide-gray-200">
                  {logs.map((log) => (
                    <React.Fragment key={log.id}>
                      <tr className="hover:bg-gray-50">
                        <td className="px-6 py-4">
                          <div className="flex items-start">
                            {getEventTypeIcon(log.event_type)}
                            <div className="ml-3 flex-1">
                              <div className="text-sm font-medium text-gray-900">
                                {formatExplicitAction(log)}
                              </div>
                              <div className="text-sm text-gray-500 mt-1">
                                {formatExplicitDescription(log)}
                              </div>
                              {log.metadata?.resource_title && (
                                <div className="text-xs text-blue-600 mt-1">
                                  📄 {log.metadata.resource_title}
                                </div>
                              )}
                            </div>
                          </div>
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap">
                          <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${getSeverityColor(log.severity)}`}>
                            {log.severity.toUpperCase()}
                          </span>
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                          {log.user_email || 'Anonymous'}
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                          {log.ip_address || 'N/A'}
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                          <div className="flex items-center">
                            <Clock className="h-4 w-4 text-gray-400 mr-1" />
                            {formatTimestamp(log.timestamp)}
                          </div>
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap">
                          <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${
                            log.success 
                              ? 'text-green-800 bg-green-100' 
                              : 'text-red-800 bg-red-100'
                          }`}>
                            {log.success ? 'SUCCESS' : 'FAILED'}
                          </span>
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap text-sm font-medium">
                          <button
                            onClick={() => setExpandedLog(expandedLog === log.id ? null : log.id)}
                            className="text-blue-600 hover:text-blue-900 flex items-center"
                          >
                            {expandedLog === log.id ? (
                              <ChevronUp className="h-4 w-4" />
                            ) : (
                              <ChevronDown className="h-4 w-4" />
                            )}
                            <span className="ml-1">Details</span>
                          </button>
                        </td>
                      </tr>
                      {expandedLog === log.id && (
                        <tr>
                          <td colSpan={7} className="px-6 py-4 bg-gray-50">
                            <div className="space-y-4">
                              {/* Action and Resource Info */}
                              <div className="grid grid-cols-2 gap-4">
                                <div>
                                  <h4 className="font-medium text-gray-900 flex items-center">
                                    🎯 Action Details
                                  </h4>
                                  <p className="text-sm text-gray-600 mt-1">{log.action || log.event_type}</p>
                                  {log.resource_type && (
                                    <p className="text-xs text-blue-600 mt-1">
                                      Resource: {log.resource_type}
                                      {log.resource_id && ` (ID: ${log.resource_id})`}
                                    </p>
                                  )}
                                </div>
                                <div>
                                  <h4 className="font-medium text-gray-900 flex items-center">
                                    ⏰ Timing
                                  </h4>
                                  <p className="text-sm text-gray-600 mt-1">
                                    {new Date(log.timestamp).toLocaleString()}
                                  </p>
                                  <p className="text-xs text-gray-500 mt-1">
                                    {new Date(log.timestamp).toISOString()}
                                  </p>
                                </div>
                              </div>

                              {/* Changes Section */}
                              {log.metadata?.changes && Object.keys(log.metadata.changes).length > 0 && (
                                <div>
                                  <h4 className="font-medium text-gray-900 flex items-center mb-2">
                                    📝 Changes Made
                                  </h4>
                                  <div className="bg-white rounded border p-3 space-y-2">
                                    {Object.entries(log.metadata.changes).map(([field, change]: [string, any]) => (
                                      <div key={field} className="flex items-center justify-between text-sm">
                                        <span className="font-medium text-gray-700 capitalize">
                                          {field.replace(/_/g, ' ')}:
                                        </span>
                                        <div className="flex items-center space-x-2">
                                          <span className="text-red-600 bg-red-50 px-2 py-1 rounded text-xs">
                                            {String(change.old || 'null')}
                                          </span>
                                          <span className="text-gray-400">→</span>
                                          <span className="text-green-600 bg-green-50 px-2 py-1 rounded text-xs">
                                            {String(change.new || 'null')}
                                          </span>
                                        </div>
                                      </div>
                                    ))}
                                  </div>
                                </div>
                              )}

                              {/* Error Message */}
                              {log.error_message && (
                                <div>
                                  <h4 className="font-medium text-gray-900 flex items-center">
                                    ❌ Error Details
                                  </h4>
                                  <div className="bg-red-50 border border-red-200 rounded p-3 mt-2">
                                    <p className="text-sm text-red-700">{log.error_message}</p>
                                  </div>
                                </div>
                              )}

                              {/* Additional Metadata */}
                              {log.metadata && Object.keys(log.metadata).length > 0 && (
                                <div>
                                  <h4 className="font-medium text-gray-900 flex items-center">
                                    📊 Additional Information
                                  </h4>
                                  <div className="bg-white rounded border p-3 mt-2">
                                    <div className="grid grid-cols-2 gap-3 text-sm">
                                      {log.metadata.created_at && (
                                        <div>
                                          <span className="font-medium text-gray-700">Created:</span>
                                          <span className="ml-2 text-gray-600">
                                            {new Date(log.metadata.created_at).toLocaleString()}
                                          </span>
                                        </div>
                                      )}
                                      {log.metadata.total_items && (
                                        <div>
                                          <span className="font-medium text-gray-700">Items Processed:</span>
                                          <span className="ml-2 text-gray-600">{log.metadata.total_items}</span>
                                        </div>
                                      )}
                                      {log.metadata.success_count !== undefined && (
                                        <div>
                                          <span className="font-medium text-gray-700">Successful:</span>
                                          <span className="ml-2 text-green-600">{log.metadata.success_count}</span>
                                        </div>
                                      )}
                                      {log.metadata.error_count !== undefined && (
                                        <div>
                                          <span className="font-medium text-gray-700">Failed:</span>
                                          <span className="ml-2 text-red-600">{log.metadata.error_count}</span>
                                        </div>
                                      )}
                                      {log.metadata.assigned_roles && (
                                        <div>
                                          <span className="font-medium text-gray-700">Roles:</span>
                                          <span className="ml-2 text-gray-600">
                                            {log.metadata.assigned_roles.join(', ')}
                                          </span>
                                        </div>
                                      )}
                                      {log.metadata.fields_changed && (
                                        <div>
                                          <span className="font-medium text-gray-700">Fields Modified:</span>
                                          <span className="ml-2 text-gray-600">
                                            {log.metadata.fields_changed.join(', ')}
                                          </span>
                                        </div>
                                      )}
                                    </div>
                                  </div>
                                </div>
                              )}

                              {/* Technical Details */}
                              <div>
                                <h4 className="font-medium text-gray-900 flex items-center">
                                  🔧 Technical Details
                                </h4>
                                <div className="grid grid-cols-3 gap-4 text-xs text-gray-500 mt-2">
                                  <div>
                                    <span className="font-medium">Session ID:</span>
                                    <br />
                                    <span className="font-mono">{log.session_id || 'N/A'}</span>
                                  </div>
                                  <div>
                                    <span className="font-medium">Request ID:</span>
                                    <br />
                                    <span className="font-mono">{log.request_id || 'N/A'}</span>
                                  </div>
                                  <div>
                                    <span className="font-medium">User Agent:</span>
                                    <br />
                                    <span className="break-all">{log.user_agent || 'N/A'}</span>
                                  </div>
                                </div>
                              </div>
                            </div>
                          </td>
                        </tr>
                      )}
                    </React.Fragment>
                  ))}
                </tbody>
              </table>

            {/* Pagination */}
            {pagination.total > pagination.limit && (
              <div className="bg-white px-4 py-3 border-t border-gray-200 sm:px-6">
                <div className="flex items-center justify-between">
                  <div className="flex-1 flex justify-between sm:hidden">
                    <Button
                      onClick={() => setFilters(prev => ({ ...prev, page: (prev.page || 1) - 1 }))}
                      disabled={pagination.page === 1}
                      variant="outline"
                    >
                      Previous
                    </Button>
                    <Button
                      onClick={() => setFilters(prev => ({ ...prev, page: (prev.page || 1) + 1 }))}
                      disabled={pagination.page >= pagination.totalPages}
                      variant="outline"
                    >
                      Next
                    </Button>
                  </div>
                  <div className="hidden sm:flex-1 sm:flex sm:items-center sm:justify-between">
                    <div>
                      <p className="text-sm text-gray-700">
                        Showing{' '}
                        <span className="font-medium">
                          {((pagination.page - 1) * pagination.limit) + 1}
                        </span>{' '}
                        to{' '}
                        <span className="font-medium">
                          {Math.min(pagination.page * pagination.limit, pagination.total)}
                        </span>{' '}
                        of{' '}
                        <span className="font-medium">{pagination.total}</span>{' '}
                        results
                      </p>
                    </div>
                    <div>
                      <nav className="relative z-0 inline-flex rounded-md shadow-sm -space-x-px">
                        <Button
                          onClick={() => setFilters(prev => ({ ...prev, page: (prev.page || 1) - 1 }))}
                          disabled={pagination.page === 1}
                          variant="outline"
                          className="rounded-r-none"
                        >
                          Previous
                        </Button>
                        <Button
                          onClick={() => setFilters(prev => ({ ...prev, page: (prev.page || 1) + 1 }))}
                          disabled={pagination.page >= pagination.totalPages}
                          variant="outline"
                          className="rounded-l-none"
                        >
                          Next
                        </Button>
                      </nav>
                    </div>
                  </div>
                </div>
              </div>
            )}
          </div>
        )}

        {/* Empty State */}
        {!loading && logs.length === 0 && (
          <div className="text-center py-12">
            <Shield className="mx-auto h-12 w-12 text-gray-400" />
            <h3 className="mt-2 text-sm font-medium text-gray-900">No audit logs found</h3>
            <p className="mt-1 text-sm text-gray-500">
              No security events match your current filters.
            </p>
          </div>
        )}
        </div>
      </AdminLayout>
    </PageErrorBoundary>
  );
}
