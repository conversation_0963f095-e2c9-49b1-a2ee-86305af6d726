'use client';

import { useRouter } from 'next/navigation';
import Link from 'next/link';
import { ArrowLeft } from 'lucide-react';
import BlogForm from '../components/BlogForm';
import { BlogFormData } from '@/types/blog';
import AdminLayout from '@/components/layout/AdminLayout';
import { useCreateBlog } from '@/hooks/useBlogs';

export default function NewBlogPage() {
  const router = useRouter();
  const createBlogMutation = useCreateBlog();

  const handleSubmit = async (data: BlogFormData) => {
    try {
      await createBlogMutation.mutateAsync(data);
      router.push('/admin/blogs');
    } catch (error) {
      console.error('Failed to create blog post:', error);
      throw error; // Re-throw to allow proper error handling
    }
  };

  return (
    <AdminLayout>
      <div className="mb-6">
        <Link
          href="/admin/blogs"
          className="inline-flex items-center text-sm text-gray-500 hover:text-gray-700 mb-4"
        >
          <ArrowLeft className="h-4 w-4 mr-1" />
          Back to Blogs
        </Link>
        <h1 className="text-3xl font-bold text-gray-900">Create New Blog Post</h1>
        <p className="text-gray-600 mt-1">Write and publish a new blog post</p>
      </div>

      <div className="bg-white shadow rounded-lg p-6">
        <BlogForm onSubmit={handleSubmit} isLoading={createBlogMutation.isPending} />
      </div>
    </AdminLayout>
  );
}