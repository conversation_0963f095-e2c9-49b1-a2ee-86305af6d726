'use client';

import { useState } from 'react';
import { useRouter } from 'next/navigation';
import { useParams } from 'next/navigation';
import AdminLayout from '@/components/layout/AdminLayout';
import LoadingSpinner from '@/components/ui/LoadingSpinner';
import { ArrowLeft, User } from 'lucide-react';
import {
  useAdminUsers,
  useAdminRoles,
  useUpdateAdminUser,
  type AdminUser,
  type Role,
  type UpdateUserData
} from '@/hooks/useAdminUsers';

export default function EditAdminUserPage() {
  const router = useRouter();
  const params = useParams();
  const userId = params.id as string;
  
  const updateUserMutation = useUpdateAdminUser();
  
  const {
    data: usersData,
    isLoading: usersLoading
  } = useAdminUsers();

  const {
    data: rolesData,
    isLoading: rolesLoading
  } = useAdminRoles();

  const users = usersData?.users || [];
  const roles = rolesData?.roles || [];
  const user = users.find(u => u.id === userId);

  const handleUpdateUser = async (updates: UpdateUserData) => {
    try {
      await updateUserMutation.mutateAsync({ userId, updates });
      router.push('/admin/users');
    } catch (error) {
      // Error is handled by the mutation hook
    }
  };

  const handleCancel = () => {
    router.push('/admin/users');
  };

  if (usersLoading || rolesLoading) {
    return (
      <AdminLayout>
        <div className="flex justify-center items-center h-64">
          <LoadingSpinner size="large" />
        </div>
      </AdminLayout>
    );
  }

  if (!user) {
    return (
      <AdminLayout>
        <div className="max-w-2xl mx-auto">
          <div className="text-center py-12">
            <User className="h-12 w-12 text-gray-400 mx-auto mb-4" />
            <h2 className="text-xl font-semibold text-gray-900 mb-2">User Not Found</h2>
            <p className="text-gray-600 mb-6">
              The user you're looking for doesn't exist or has been removed.
            </p>
            <button
              onClick={handleCancel}
              className="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700"
            >
              Back to Users
            </button>
          </div>
        </div>
      </AdminLayout>
    );
  }

  return (
    <AdminLayout>
      <div className="max-w-2xl mx-auto">
        {/* Header */}
        <div className="mb-6">
          <button
            onClick={handleCancel}
            className="flex items-center text-gray-600 hover:text-gray-900 mb-4"
          >
            <ArrowLeft className="h-4 w-4 mr-2" />
            Back to Users
          </button>
          <h1 className="text-2xl font-bold text-gray-900">Edit Admin User</h1>
          <p className="text-gray-600 mt-2">
            Update user roles and permissions for {user.full_name || user.username || 'this user'}.
          </p>
        </div>

        {/* Form */}
        <div className="bg-white shadow-sm rounded-lg border border-gray-200 p-6">
          <EditUserForm
            user={user}
            roles={roles}
            onSubmit={handleUpdateUser}
            onCancel={handleCancel}
            isLoading={updateUserMutation.isPending}
          />
        </div>
      </div>
    </AdminLayout>
  );
}

// Validation utilities for edit form
const validateRoles = (roles: string[]): string | null => {
  if (!roles || roles.length === 0) return 'At least one role must be selected';
  return null;
};

// Edit User Form Component
function EditUserForm({
  user,
  roles,
  onSubmit,
  onCancel,
  isLoading
}: {
  user: AdminUser;
  roles: Role[];
  onSubmit: (data: UpdateUserData) => void;
  onCancel: () => void;
  isLoading?: boolean;
}) {
  const [formData, setFormData] = useState({
    role_names: user.roles.map(role => role.name),
    is_active: user.is_active
  });

  const [errors, setErrors] = useState<Record<string, string>>({});
  const [touched, setTouched] = useState<Record<string, boolean>>({});

  // Validate roles field
  const validateField = (name: string, value: any) => {
    switch (name) {
      case 'role_names':
        return validateRoles(value);
      default:
        return null;
    }
  };

  // Handle field changes with validation
  const handleFieldChange = (name: string, value: any) => {
    setFormData(prev => ({ ...prev, [name]: value }));

    // Validate field if it has been touched
    if (touched[name]) {
      const error = validateField(name, value);
      setErrors(prev => ({
        ...prev,
        [name]: error || ''
      }));
    }
  };

  // Handle field blur (mark as touched and validate)
  const handleFieldBlur = (name: string) => {
    setTouched(prev => ({ ...prev, [name]: true }));
    const value = formData[name as keyof typeof formData];
    const error = validateField(name, value);
    setErrors(prev => ({
      ...prev,
      [name]: error || ''
    }));
  };

  // Validate all fields
  const validateForm = (): boolean => {
    const newErrors: Record<string, string> = {};
    let isValid = true;

    // Validate roles
    const rolesError = validateField('role_names', formData.role_names);
    if (rolesError) {
      newErrors.role_names = rolesError;
      isValid = false;
    }

    setErrors(newErrors);
    setTouched({
      role_names: true,
      is_active: true
    });

    return isValid;
  };

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();

    if (validateForm()) {
      onSubmit(formData);
    }
  };

  // Check if form has any errors
  const hasErrors = Object.values(errors).some(error => error);
  const isFormValid = !hasErrors && formData.role_names.length > 0;

  return (
    <form onSubmit={handleSubmit} className="space-y-6">
      {/* User Information */}
      <div className="bg-gray-50 rounded-lg p-4">
        <h3 className="text-sm font-medium text-gray-700 mb-3">User Information</h3>
        <div className="space-y-2">
          <div>
            <span className="text-sm font-medium text-gray-600">Name:</span>
            <span className="ml-2 text-sm text-gray-900">
              {user.full_name || user.username || 'No name provided'}
            </span>
          </div>
          <div>
            <span className="text-sm font-medium text-gray-600">User ID:</span>
            <span className="ml-2 text-sm text-gray-500 font-mono">{user.id}</span>
          </div>
          <div>
            <span className="text-sm font-medium text-gray-600">Last Login:</span>
            <span className="ml-2 text-sm text-gray-500">
              {user.last_login_at 
                ? new Date(user.last_login_at).toLocaleDateString()
                : 'Never'
              }
            </span>
          </div>
        </div>
      </div>

      {/* Roles */}
      <div>
        <label className="block text-sm font-medium text-gray-700 mb-2">
          Roles <span className="text-red-500">*</span>
        </label>
        <select
          multiple
          value={formData.role_names}
          onChange={(e) => handleFieldChange('role_names', Array.from(e.target.selectedOptions, option => option.value))}
          onBlur={() => handleFieldBlur('role_names')}
          className={`w-full border rounded-md px-3 py-2 focus:outline-none focus:ring-2 min-h-[120px] ${
            errors.role_names && touched.role_names
              ? 'border-red-300 focus:ring-red-500 focus:border-red-500'
              : 'border-gray-300 focus:ring-blue-500 focus:border-blue-500'
          }`}
          disabled={isLoading}
        >
          {roles.map(role => (
            <option key={role.id} value={role.name}>
              {role.name.replace('_', ' ').toUpperCase()}
            </option>
          ))}
        </select>
        {errors.role_names && touched.role_names && (
          <p className="text-red-500 text-sm mt-1">{errors.role_names}</p>
        )}
        <p className="text-sm text-gray-500 mt-1">
          Hold Ctrl/Cmd to select multiple roles. At least one role is required.
        </p>
      </div>

      {/* Active Status */}
      <div>
        <label className="flex items-center">
          <input
            type="checkbox"
            checked={formData.is_active}
            onChange={(e) => setFormData({ ...formData, is_active: e.target.checked })}
            className="rounded border-gray-300 text-blue-600 shadow-sm focus:border-blue-300 focus:ring focus:ring-blue-200 focus:ring-opacity-50"
            disabled={isLoading}
          />
          <span className="ml-2 text-sm text-gray-700">Active User</span>
        </label>
        <p className="text-sm text-gray-500 mt-1">
          Inactive users cannot log in to the admin panel
        </p>
      </div>

      <div className="flex justify-end space-x-3 pt-6 border-t border-gray-200">
        <button
          type="button"
          onClick={onCancel}
          className="px-4 py-2 border border-gray-300 rounded-md text-sm font-medium text-gray-700 hover:bg-gray-50 disabled:opacity-50"
          disabled={isLoading}
        >
          Cancel
        </button>
        <button
          type="submit"
          className={`px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white flex items-center ${
            isFormValid && !isLoading
              ? 'bg-blue-600 hover:bg-blue-700 cursor-pointer'
              : 'bg-gray-400 cursor-not-allowed'
          } disabled:opacity-50 disabled:cursor-not-allowed`}
          disabled={!isFormValid || isLoading}
        >
          {isLoading && (
            <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
          )}
          {isLoading ? 'Updating...' : 'Update User'}
        </button>
      </div>

      {/* Form validation summary */}
      {!isFormValid && touched.role_names && (
        <div className="bg-yellow-50 border border-yellow-200 rounded-md p-4">
          <div className="flex">
            <div className="flex-shrink-0">
              <svg className="h-5 w-5 text-yellow-400" viewBox="0 0 20 20" fill="currentColor">
                <path fillRule="evenodd" d="M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z" clipRule="evenodd" />
              </svg>
            </div>
            <div className="ml-3">
              <h3 className="text-sm font-medium text-yellow-800">
                Please complete all required fields
              </h3>
              <div className="mt-2 text-sm text-yellow-700">
                <ul className="list-disc pl-5 space-y-1">
                  {formData.role_names.length === 0 && <li>At least one role must be selected</li>}
                  {hasErrors && <li>Please fix the validation errors above</li>}
                </ul>
              </div>
            </div>
          </div>
        </div>
      )}
    </form>
  );
}
