'use client';

import { useState } from 'react';
import { useRouter } from 'next/navigation';
import AdminLayout from '@/components/layout/AdminLayout';
import LoadingSpinner from '@/components/ui/LoadingSpinner';
import { ArrowLeft } from 'lucide-react';
import {
  useAdminRoles,
  useCreateAdminUser,
  type Role,
  type CreateUserData
} from '@/hooks/useAdminUsers';

export default function NewAdminUserPage() {
  const router = useRouter();
  const createUserMutation = useCreateAdminUser();
  
  const {
    data: rolesData,
    isLoading: rolesLoading
  } = useAdminRoles();

  const roles = rolesData?.roles || [];

  const handleCreateUser = async (userData: CreateUserData) => {
    try {
      await createUserMutation.mutateAsync(userData);
      router.push('/admin/users');
    } catch (error) {
      // Error is handled by the mutation hook
    }
  };

  const handleCancel = () => {
    router.push('/admin/users');
  };

  if (rolesLoading) {
    return (
      <AdminLayout>
        <div className="flex justify-center items-center h-64">
          <LoadingSpinner size="large" />
        </div>
      </AdminLayout>
    );
  }

  return (
    <AdminLayout>
      <div className="max-w-2xl mx-auto">
        {/* Header */}
        <div className="mb-6">
          <button
            onClick={handleCancel}
            className="flex items-center text-gray-600 hover:text-gray-900 mb-4"
          >
            <ArrowLeft className="h-4 w-4 mr-2" />
            Back to Users
          </button>
          <h1 className="text-2xl font-bold text-gray-900">Create New Admin User</h1>
          <p className="text-gray-600 mt-2">
            Add a new administrator to the system with appropriate roles and permissions.
          </p>
        </div>

        {/* Form */}
        <div className="bg-white shadow-sm rounded-lg border border-gray-200 p-6">
          <CreateUserForm
            roles={roles}
            onSubmit={handleCreateUser}
            onCancel={handleCancel}
            isLoading={createUserMutation.isPending}
          />
        </div>
      </div>
    </AdminLayout>
  );
}

// Validation utilities
const validateEmail = (email: string): string | null => {
  if (!email.trim()) return 'Email is required';
  const emailRegex = /^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$/;
  if (!emailRegex.test(email)) return 'Please enter a valid email address';
  return null;
};

const validatePassword = (password: string): string | null => {
  if (!password) return 'Password is required';
  if (password.length < 8) return 'Password must be at least 8 characters long';
  if (!/(?=.*[a-z])/.test(password)) return 'Password must contain at least one lowercase letter';
  if (!/(?=.*[A-Z])/.test(password)) return 'Password must contain at least one uppercase letter';
  if (!/(?=.*\d)/.test(password)) return 'Password must contain at least one number';
  if (!/(?=.*[!@#$%^&*()_+-=[\]{};':"\\|,.<>/?])/.test(password)) return 'Password must contain at least one special character';
  return null;
};

const validateUsername = (username: string): string | null => {
  if (!username.trim()) return null; // Username is optional
  if (username.length < 3) return 'Username must be at least 3 characters long';
  if (!/^[a-zA-Z0-9_-]+$/.test(username)) return 'Username can only contain letters, numbers, hyphens, and underscores';
  return null;
};

const validateRoles = (roles: string[]): string | null => {
  if (!roles || roles.length === 0) return 'At least one role must be selected';
  return null;
};

// Create User Form Component
function CreateUserForm({
  roles,
  onSubmit,
  onCancel,
  isLoading
}: {
  roles: Role[];
  onSubmit: (data: CreateUserData) => void;
  onCancel: () => void;
  isLoading?: boolean;
}) {
  const [formData, setFormData] = useState({
    email: '',
    password: '',
    full_name: '',
    username: '',
    role_names: ['content_manager']
  });

  const [errors, setErrors] = useState<Record<string, string>>({});
  const [touched, setTouched] = useState<Record<string, boolean>>({});

  // Validate individual fields
  const validateField = (name: string, value: any) => {
    switch (name) {
      case 'email':
        return validateEmail(value);
      case 'password':
        return validatePassword(value);
      case 'username':
        return validateUsername(value);
      case 'role_names':
        return validateRoles(value);
      default:
        return null;
    }
  };

  // Handle field changes with validation
  const handleFieldChange = (name: string, value: any) => {
    setFormData(prev => ({ ...prev, [name]: value }));

    // Validate field if it has been touched
    if (touched[name]) {
      const error = validateField(name, value);
      setErrors(prev => ({
        ...prev,
        [name]: error || ''
      }));
    }
  };

  // Handle field blur (mark as touched and validate)
  const handleFieldBlur = (name: string) => {
    setTouched(prev => ({ ...prev, [name]: true }));
    const value = formData[name as keyof typeof formData];
    const error = validateField(name, value);
    setErrors(prev => ({
      ...prev,
      [name]: error || ''
    }));
  };

  // Validate all fields
  const validateForm = (): boolean => {
    const newErrors: Record<string, string> = {};
    let isValid = true;

    // Validate all required fields
    Object.keys(formData).forEach(key => {
      const error = validateField(key, formData[key as keyof typeof formData]);
      if (error) {
        newErrors[key] = error;
        isValid = false;
      }
    });

    setErrors(newErrors);
    setTouched({
      email: true,
      password: true,
      username: true,
      role_names: true,
      full_name: true
    });

    return isValid;
  };

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();

    if (validateForm()) {
      onSubmit(formData);
    }
  };

  // Check if form has any errors
  const hasErrors = Object.values(errors).some(error => error);
  const isFormValid = !hasErrors && formData.email && formData.password && formData.role_names.length > 0;

  return (
    <form onSubmit={handleSubmit} className="space-y-6">
      {/* Email Field */}
      <div>
        <label className="block text-sm font-medium text-gray-700 mb-2">
          Email <span className="text-red-500">*</span>
        </label>
        <input
          type="email"
          value={formData.email}
          onChange={(e) => handleFieldChange('email', e.target.value)}
          onBlur={() => handleFieldBlur('email')}
          className={`w-full border rounded-md px-3 py-2 focus:outline-none focus:ring-2 ${
            errors.email && touched.email
              ? 'border-red-300 focus:ring-red-500 focus:border-red-500'
              : 'border-gray-300 focus:ring-blue-500 focus:border-blue-500'
          }`}
          placeholder="<EMAIL>"
          disabled={isLoading}
        />
        {errors.email && touched.email && (
          <p className="text-red-500 text-sm mt-1">{errors.email}</p>
        )}
      </div>

      {/* Password Field */}
      <div>
        <label className="block text-sm font-medium text-gray-700 mb-2">
          Password <span className="text-red-500">*</span>
        </label>
        <input
          type="password"
          value={formData.password}
          onChange={(e) => handleFieldChange('password', e.target.value)}
          onBlur={() => handleFieldBlur('password')}
          className={`w-full border rounded-md px-3 py-2 focus:outline-none focus:ring-2 ${
            errors.password && touched.password
              ? 'border-red-300 focus:ring-red-500 focus:border-red-500'
              : 'border-gray-300 focus:ring-blue-500 focus:border-blue-500'
          }`}
          placeholder="Enter a secure password"
          disabled={isLoading}
        />
        {errors.password && touched.password && (
          <p className="text-red-500 text-sm mt-1">{errors.password}</p>
        )}
        <div className="text-xs text-gray-500 mt-1">
          Password must be at least 8 characters with uppercase, lowercase, number, and special character
        </div>
      </div>

      {/* Full Name Field */}
      <div>
        <label className="block text-sm font-medium text-gray-700 mb-2">
          Full Name
        </label>
        <input
          type="text"
          value={formData.full_name}
          onChange={(e) => handleFieldChange('full_name', e.target.value)}
          className="w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
          placeholder="John Doe"
          disabled={isLoading}
        />
      </div>

      {/* Username Field */}
      <div>
        <label className="block text-sm font-medium text-gray-700 mb-2">
          Username
        </label>
        <input
          type="text"
          value={formData.username}
          onChange={(e) => handleFieldChange('username', e.target.value)}
          onBlur={() => handleFieldBlur('username')}
          className={`w-full border rounded-md px-3 py-2 focus:outline-none focus:ring-2 ${
            errors.username && touched.username
              ? 'border-red-300 focus:ring-red-500 focus:border-red-500'
              : 'border-gray-300 focus:ring-blue-500 focus:border-blue-500'
          }`}
          placeholder="johndoe"
          disabled={isLoading}
        />
        {errors.username && touched.username && (
          <p className="text-red-500 text-sm mt-1">{errors.username}</p>
        )}
        <div className="text-xs text-gray-500 mt-1">
          Optional. Must be at least 3 characters, letters, numbers, hyphens, and underscores only
        </div>
      </div>

      {/* Roles Field */}
      <div>
        <label className="block text-sm font-medium text-gray-700 mb-2">
          Roles <span className="text-red-500">*</span>
        </label>
        <select
          multiple
          value={formData.role_names}
          onChange={(e) => handleFieldChange('role_names', Array.from(e.target.selectedOptions, option => option.value))}
          onBlur={() => handleFieldBlur('role_names')}
          className={`w-full border rounded-md px-3 py-2 focus:outline-none focus:ring-2 min-h-[120px] ${
            errors.role_names && touched.role_names
              ? 'border-red-300 focus:ring-red-500 focus:border-red-500'
              : 'border-gray-300 focus:ring-blue-500 focus:border-blue-500'
          }`}
          disabled={isLoading}
        >
          {roles.map(role => (
            <option key={role.id} value={role.name}>
              {role.name.replace('_', ' ').toUpperCase()}
            </option>
          ))}
        </select>
        {errors.role_names && touched.role_names && (
          <p className="text-red-500 text-sm mt-1">{errors.role_names}</p>
        )}
        <p className="text-sm text-gray-500 mt-1">
          Hold Ctrl/Cmd to select multiple roles. At least one role is required.
        </p>
      </div>

      <div className="flex justify-end space-x-3 pt-6 border-t border-gray-200">
        <button
          type="button"
          onClick={onCancel}
          className="px-4 py-2 border border-gray-300 rounded-md text-sm font-medium text-gray-700 hover:bg-gray-50 disabled:opacity-50"
          disabled={isLoading}
        >
          Cancel
        </button>
        <button
          type="submit"
          className={`px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white flex items-center ${
            isFormValid && !isLoading
              ? 'bg-blue-600 hover:bg-blue-700 cursor-pointer'
              : 'bg-gray-400 cursor-not-allowed'
          } disabled:opacity-50 disabled:cursor-not-allowed`}
          disabled={!isFormValid || isLoading}
        >
          {isLoading && (
            <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
          )}
          {isLoading ? 'Creating...' : 'Create User'}
        </button>
      </div>

      {/* Form validation summary */}
      {!isFormValid && (touched.email || touched.password || touched.role_names) && (
        <div className="bg-yellow-50 border border-yellow-200 rounded-md p-4">
          <div className="flex">
            <div className="flex-shrink-0">
              <svg className="h-5 w-5 text-yellow-400" viewBox="0 0 20 20" fill="currentColor">
                <path fillRule="evenodd" d="M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z" clipRule="evenodd" />
              </svg>
            </div>
            <div className="ml-3">
              <h3 className="text-sm font-medium text-yellow-800">
                Please complete all required fields
              </h3>
              <div className="mt-2 text-sm text-yellow-700">
                <ul className="list-disc pl-5 space-y-1">
                  {!formData.email && <li>Email is required</li>}
                  {!formData.password && <li>Password is required</li>}
                  {formData.role_names.length === 0 && <li>At least one role must be selected</li>}
                  {hasErrors && <li>Please fix the validation errors above</li>}
                </ul>
              </div>
            </div>
          </div>
        </div>
      )}
    </form>
  );
}
