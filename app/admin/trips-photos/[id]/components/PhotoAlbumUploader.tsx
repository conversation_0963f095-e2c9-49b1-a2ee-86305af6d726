'use client';

import { useState } from 'react';
import Link from 'next/link';
import Image from 'next/image';
import { PhotoAlbum } from '@/types/photo-album';
import { useToast } from '@/hooks/useToast';
import {
  ArrowLeft,
  Camera,
  Upload,
  X,
  Check,
  Loader2,
  AlertCircle,
  CheckCircle,
  ExternalLink,
  Image as ImageIcon,
  User,
  Album,
  RefreshCw,
  Edit
} from 'lucide-react';

interface PhotoAlbumUploaderProps {
  album: PhotoAlbum;
}

interface UploadItem {
  id: string;
  file: File;
  originalUrl: string;
  watermarkedUrl?: string;
  status: 'pending' | 'watermarking' | 'uploading' | 'success' | 'error';
  progress: number;
  errorMessage?: string;
  mediaItemId?: string;
}

export default function PhotoAlbumUploader({ album }: PhotoAlbumUploaderProps) {
  const toast = useToast();
  const [uploadItems, setUploadItems] = useState<UploadItem[]>([]);
  const [isUploading, setIsUploading] = useState(false);
  const [isReauthorizing, setIsReauthorizing] = useState(false);
  const [watermarkPreviews, setWatermarkPreviews] = useState<Record<string, string>>({});

  const generateWatermarkPreview = async (itemId: string, file: File) => {
    try {
      const formData = new FormData();
      formData.append('image', file);

      const response = await fetch('/api/admin/watermark-preview', {
        method: 'POST',
        body: formData,
      });

      if (response.ok) {
        const result = await response.json();
        setWatermarkPreviews(prev => ({
          ...prev,
          [itemId]: result.watermarkedUrl
        }));
      }
    } catch (error) {
      console.error('Error generating watermark preview:', error);
    }
  };

  const handleFileChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const files = Array.from(e.target.files || []);

    const newItems: UploadItem[] = files.map(file => ({
      id: Math.random().toString(36).substring(2, 11),
      file,
      originalUrl: URL.createObjectURL(file),
      status: 'pending',
      progress: 0
    }));

    setUploadItems(prev => [...prev, ...newItems]);

    // Generate watermark previews for all new items
    newItems.forEach(item => {
      generateWatermarkPreview(item.id, item.file);
    });

    // Reset the file input so the same files can be selected again
    e.target.value = '';
  };

  const removeItem = (id: string) => {
    setUploadItems(prev => {
      const item = prev.find(item => item.id === id);
      if (item) {
        URL.revokeObjectURL(item.originalUrl);
        if (item.watermarkedUrl) {
          URL.revokeObjectURL(item.watermarkedUrl);
        }
      }
      return prev.filter(item => item.id !== id);
    });

    // Also clean up watermark preview
    setWatermarkPreviews(prev => {
      const { [id]: removed, ...rest } = prev;
      if (removed) {
        URL.revokeObjectURL(removed);
      }
      return rest;
    });
  };

  const processAndUpload = async (item: UploadItem) => {
    console.log(`[PHOTO_UPLOAD] Processing ${item.file.name}`);

    // Step 1: Watermarking
    setUploadItems(prev =>
      prev.map(i => i.id === item.id ? { ...i, status: 'watermarking', progress: 20 } : i)
    );

    try {
      const formData = new FormData();
      formData.append('image', item.file);
      formData.append('albumId', album.id);

      // Step 2: Uploading
      setUploadItems(prev =>
        prev.map(i => i.id === item.id ? { ...i, status: 'uploading', progress: 70 } : i)
      );

      const response = await fetch('/api/admin/photo-albums/upload', {
        method: 'POST',
        body: formData,
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Upload failed');
      }

      const result = await response.json();

      // Update with success status
      setUploadItems(prev =>
        prev.map(i => i.id === item.id ? {
          ...i,
          status: 'success',
          progress: 100,
          mediaItemId: result.mediaItemId
        } : i)
      );

      console.log(`[PHOTO_UPLOAD] ✅ Successfully uploaded ${item.file.name} to Google Photos`);
      toast.success(`Uploaded ${item.file.name} to Google Photos album`);

      // Auto-clear successful uploads after 5 seconds
      setTimeout(() => {
        setUploadItems(prev => prev.filter(i => i.id !== item.id));
        URL.revokeObjectURL(item.originalUrl);
        if (item.watermarkedUrl) {
          URL.revokeObjectURL(item.watermarkedUrl);
        }
      }, 5000);

    } catch (error) {
      console.error(`[PHOTO_UPLOAD] ❌ Error uploading ${item.file.name}:`, error);
      setUploadItems(prev =>
        prev.map(i => i.id === item.id ? {
          ...i,
          status: 'error',
          progress: 0,
          errorMessage: error instanceof Error ? error.message : 'Unknown error'
        } : i)
      );
      toast.error(`Failed to upload ${item.file.name}`);
    }
  };

  const handleUploadAll = async () => {
    if (uploadItems.length === 0 || isUploading || !canUpload()) return;

    setIsUploading(true);
    const toastId = toast.loading('Uploading photos to Google Photos album...');

    const pendingItems = uploadItems.filter(item => item.status === 'pending');

    for (const item of pendingItems) {
      await processAndUpload(item);
    }

    setIsUploading(false);
    toast.dismiss(toastId);
    toast.success(`Uploaded ${pendingItems.length} photos to Google Photos album`);
  };

  const handleReauthorize = () => {
    setIsReauthorizing(true);
    window.location.href = `/api/auth/google?albumId=${album.id}`;
  };

  const canUpload = () => {
    return album.oauth_user_email && album.google_photos_album_id;
  };

  const clearAll = () => {
    uploadItems.forEach(item => {
      URL.revokeObjectURL(item.originalUrl);
      if (item.watermarkedUrl) {
        URL.revokeObjectURL(item.watermarkedUrl);
      }
    });
    setUploadItems([]);
  };

  return (
    <div className="max-w-6xl mx-auto">
      {/* Header */}
      <div className="mb-8">
        <Link
          href="/admin/trips-photos"
          className="inline-flex items-center text-sm text-gray-500 hover:text-gray-700 mb-4"
        >
          <ArrowLeft className="h-4 w-4 mr-1" />
          Back to Photo Albums
        </Link>
        <div className="flex items-center justify-between">
          <div className="flex items-center">
            <div className="inline-flex items-center justify-center w-12 h-12 bg-purple-100 rounded-full mr-4">
              <Camera className="w-6 h-6 text-purple-600" />
            </div>
            <div>
              <h1 className="text-3xl font-bold text-gray-900">{album.trip_name}</h1>
              <p className="text-gray-600">Upload photos to Google Photos album</p>
            </div>
          </div>
          <Link
            href={`/admin/trips-photos/${album.id}/edit`}
            className="inline-flex items-center px-4 py-2 border border-gray-300 shadow-sm text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50"
          >
            <Edit className="h-4 w-4 mr-2" />
            Edit Album
          </Link>
        </div>
      </div>

      {/* Album Info */}
      <div className="bg-white shadow-sm rounded-lg p-6 mb-8">
        <h2 className="text-lg font-semibold text-gray-900 mb-4">Album Information</h2>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          <div>
            <label className="block text-sm font-medium text-gray-700">Trip Name</label>
            <p className="mt-1 text-sm text-gray-900">{album.trip_name}</p>
          </div>
          <div>
            <label className="block text-sm font-medium text-gray-700">Created</label>
            <p className="mt-1 text-sm text-gray-900">
              {new Date(album.created_at).toLocaleDateString('en-US', {
                year: 'numeric',
                month: 'long',
                day: 'numeric',
              })}
            </p>
          </div>
          {album.trip_description && (
            <div className="md:col-span-2">
              <label className="block text-sm font-medium text-gray-700">Description</label>
              <p className="mt-1 text-sm text-gray-900">{album.trip_description}</p>
            </div>
          )}
          <div>
            <label className="block text-sm font-medium text-gray-700">Access</label>
            <p className="mt-1">
              {album.access_password_hash ? (
                <span className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
                  Password Protected
                </span>
              ) : (
                <span className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-green-100 text-green-800">
                  Public Access
                </span>
              )}
            </p>
          </div>
        </div>
      </div>

      {/* Google Photos Status */}
      <div className="bg-white shadow-sm rounded-lg p-6 mb-8">
        <div className="flex items-center justify-between mb-4">
          <h2 className="text-lg font-semibold text-gray-900 flex items-center">
            <Album className="h-5 w-5 mr-2 text-purple-600" />
            Google Photos Status
          </h2>
          {album.manual_shareable_url && (
            <div className="flex flex-col space-y-2">
              <a
                href={album.manual_shareable_url}
                target="_blank"
                rel="noopener noreferrer"
                className="inline-flex items-center text-sm text-purple-600 hover:text-purple-800"
              >
                <ExternalLink className="h-4 w-4 mr-1" />
                Manual Shareable URL
              </a>
            </div>
          )}
        </div>

        <div className="space-y-4">
          {/* Authorization Status */}
          <div className="flex items-start space-x-3">
            <User className="h-5 w-5 text-purple-600 mt-0.5" />
            <div className="flex-1">
              <p className="text-sm font-medium text-gray-900">Authorization Status</p>
              {album.oauth_user_email ? (
                <div className="mt-1">
                  <div className="flex items-center space-x-2">
                    <CheckCircle className="h-4 w-4 text-green-500" />
                    <span className="text-sm text-green-700">Authorized</span>
                  </div>
                  <p className="text-sm text-gray-600 mt-1">
                    Account: {album.oauth_user_email}
                  </p>
                  <button
                    onClick={handleReauthorize}
                    disabled={isReauthorizing}
                    className="inline-flex items-center mt-2 text-sm text-purple-600 hover:text-purple-800 disabled:opacity-50"
                  >
                    <RefreshCw className="h-4 w-4 mr-1" />
                    {isReauthorizing ? 'Reauthorizing...' : 'Reauthorize'}
                  </button>
                </div>
              ) : (
                <div className="mt-1">
                  <div className="flex items-center space-x-2">
                    <AlertCircle className="h-4 w-4 text-orange-500" />
                    <span className="text-sm text-orange-700">Not authorized</span>
                  </div>
                  <button
                    onClick={handleReauthorize}
                    disabled={isReauthorizing}
                    className="inline-flex items-center mt-2 px-3 py-1 text-sm font-medium text-purple-700 bg-purple-100 border border-purple-300 rounded-md hover:bg-purple-200 disabled:opacity-50"
                  >
                    {isReauthorizing ? 'Authorizing...' : 'Authorize Google Photos'}
                  </button>
                </div>
              )}
            </div>
          </div>

          {/* Album Status */}
          {album.google_photos_album_id ? (
            <div className="flex items-start space-x-3">
              <CheckCircle className="h-5 w-5 text-green-500 mt-0.5" />
              <div>
                <p className="text-sm font-medium text-gray-900">Album ready for uploads</p>
                <p className="text-sm text-gray-600 mb-2">
                  Album: <span className="font-medium">{album.trip_name} - Photos</span>
                </p>
                <span className="inline-flex items-center px-3 py-1 text-xs font-medium rounded-full text-green-700 bg-green-100">
                  <CheckCircle className="h-3 w-3 mr-1" />
                  Ready for uploads
                </span>
              </div>
            </div>
          ) : (
            <div className="flex items-start space-x-3">
              <AlertCircle className="h-5 w-5 text-orange-500 mt-0.5" />
              <div>
                <p className="text-sm font-medium text-gray-900">Album not created</p>
                <p className="text-sm text-gray-600">
                  The Google Photos album will be created automatically after authorization.
                </p>
              </div>
            </div>
          )}
        </div>
      </div>

      {/* Upload Section */}
      {canUpload() ? (
        <>
          {/* File Upload */}
          <div className="bg-white shadow-sm rounded-lg p-6 mb-8">
            <div className="flex items-center justify-between mb-6">
              <h2 className="text-lg font-semibold text-gray-900 flex items-center">
                <Upload className="h-5 w-5 mr-2" />
                Upload Photos
              </h2>
              {uploadItems.length > 0 && (
                <button
                  onClick={clearAll}
                  className="text-sm text-red-600 hover:text-red-800"
                >
                  Clear All
                </button>
              )}
            </div>

            <div className="border-2 border-dashed border-gray-300 rounded-lg p-8 text-center hover:border-gray-400 transition-colors">
              <ImageIcon className="mx-auto h-12 w-12 text-gray-400 mb-4" />
              <div>
                <label
                  htmlFor="photo-file-upload"
                  className="relative cursor-pointer rounded-md bg-white font-medium text-purple-600 focus-within:outline-none focus-within:ring-2 focus-within:ring-purple-500 focus-within:ring-offset-2 hover:text-purple-500"
                >
                  <span>Select images to upload</span>
                  <input
                    id="photo-file-upload"
                    name="file-upload"
                    type="file"
                    className="sr-only"
                    accept="image/*"
                    multiple
                    onChange={handleFileChange}
                  />
                </label>
              </div>
              <p className="text-sm text-gray-500 mt-2">
                PNG, JPG, JPEG up to 10MB each. No upload limits - select 200-250+ images for batch upload.
                Images will be watermarked and uploaded to Google Photos.
              </p>
            </div>
          </div>

          {/* Upload Controls */}
          {uploadItems.length > 0 && (
            <div className="bg-white shadow-sm rounded-lg p-6 mb-8">
              <div className="flex items-center justify-between">
                <div>
                  <h3 className="text-lg font-semibold text-gray-900">Upload Queue</h3>
                  <p className="text-sm text-gray-600">
                    {uploadItems.filter(i => i.status === 'pending').length} pending, 
                    {uploadItems.filter(i => i.status === 'success').length} uploaded, 
                    {uploadItems.filter(i => i.status === 'error').length} errors
                  </p>
                </div>
                <button
                  onClick={handleUploadAll}
                  disabled={isUploading || uploadItems.filter(i => i.status === 'pending').length === 0}
                  className="inline-flex items-center px-4 py-2 text-sm font-medium text-white bg-purple-600 border border-transparent rounded-md hover:bg-purple-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-purple-500 disabled:opacity-50 disabled:cursor-not-allowed"
                >
                  {isUploading ? (
                    <>
                      <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                      Uploading...
                    </>
                  ) : (
                    <>
                      <Upload className="h-4 w-4 mr-2" />
                      Upload All to Album
                    </>
                  )}
                </button>
              </div>
            </div>
          )}

          {/* Images Grid */}
          {uploadItems.length > 0 && (
            <div className="bg-white shadow-sm rounded-lg p-6">
              <h3 className="text-lg font-semibold text-gray-900 mb-6">Images</h3>
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                {uploadItems.map((item) => (
                  <div key={item.id} className="border rounded-lg overflow-hidden relative">
                    <div className="relative aspect-video bg-gray-100">
                      <Image
                        src={watermarkPreviews[item.id] || item.originalUrl}
                        alt={`Preview of ${item.file.name}`}
                        fill
                        className="object-cover"
                        unoptimized
                      />

                      {/* Watermark indicator */}
                      {watermarkPreviews[item.id] && (
                        <div className="absolute bottom-2 left-2">
                          <span className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
                            <CheckCircle className="h-3 w-3 mr-1" />
                            Watermarked Preview
                          </span>
                        </div>
                      )}

                      {/* Loading watermark indicator */}
                      {!watermarkPreviews[item.id] && item.status === 'pending' && (
                        <div className="absolute bottom-2 left-2">
                          <span className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-gray-100 text-gray-800">
                            <Loader2 className="h-3 w-3 mr-1 animate-spin" />
                            Generating Preview...
                          </span>
                        </div>
                      )}

                      {/* Status Overlay */}
                      {(item.status === 'watermarking' || item.status === 'uploading') && (
                        <div className="absolute inset-0 bg-black bg-opacity-50 flex items-center justify-center">
                          <div className="text-white text-center">
                            <Loader2 className="h-8 w-8 animate-spin mx-auto mb-2" />
                            <p className="text-sm font-medium">
                              {item.status === 'watermarking' ? 'Adding Watermark...' : 'Uploading to Google Photos...'}
                            </p>
                            <div className="w-32 bg-gray-700 rounded-full h-2 mt-2">
                              <div
                                className="bg-purple-500 h-2 rounded-full transition-all duration-300"
                                style={{ width: `${item.progress}%` }}
                              />
                            </div>
                          </div>
                        </div>
                      )}

                      {/* Success Overlay */}
                      {item.status === 'success' && (
                        <div className="absolute inset-0 bg-green-500 bg-opacity-20 flex items-center justify-center">
                          <div className="bg-green-500 rounded-full p-3">
                            <Check className="h-6 w-6 text-white" />
                          </div>
                        </div>
                      )}

                      {/* Error Overlay */}
                      {item.status === 'error' && (
                        <div className="absolute inset-0 bg-red-500 bg-opacity-20 flex items-center justify-center">
                          <div className="bg-red-500 rounded-full p-3">
                            <AlertCircle className="h-6 w-6 text-white" />
                          </div>
                        </div>
                      )}

                      {/* Remove Button */}
                      {item.status !== 'uploading' && item.status !== 'watermarking' && (
                        <button
                          onClick={() => removeItem(item.id)}
                          className="absolute top-2 right-2 p-1 bg-red-600 text-white rounded-full hover:bg-red-700 transition-colors"
                        >
                          <X className="h-4 w-4" />
                        </button>
                      )}
                    </div>
                    
                    <div className="p-4">
                      <p className="text-sm font-medium text-gray-900 truncate mb-2">
                        {item.file.name}
                      </p>
                      
                      <div className="flex items-center justify-between">
                        <div className="flex items-center space-x-2">
                          {item.status === 'pending' && (
                            <span className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-gray-100 text-gray-800">
                              Pending
                            </span>
                          )}
                          {item.status === 'watermarking' && (
                            <span className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
                              <Loader2 className="h-3 w-3 mr-1 animate-spin" />
                              Watermarking
                            </span>
                          )}
                          {item.status === 'uploading' && (
                            <span className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-purple-100 text-purple-800">
                              <Loader2 className="h-3 w-3 mr-1 animate-spin" />
                              Uploading
                            </span>
                          )}
                          {item.status === 'success' && (
                            <span className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-green-100 text-green-800">
                              <Check className="h-3 w-3 mr-1" />
                              Uploaded
                            </span>
                          )}
                          {item.status === 'error' && (
                            <span className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-red-100 text-red-800">
                              Error
                            </span>
                          )}
                        </div>
                      </div>
                      
                      {item.status === 'error' && item.errorMessage && (
                        <p className="text-xs text-red-600 mt-2">{item.errorMessage}</p>
                      )}
                    </div>
                  </div>
                ))}
              </div>
            </div>
          )}
        </>
      ) : (
        <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-6">
          <div className="flex items-start space-x-3">
            <AlertCircle className="h-5 w-5 text-yellow-600 mt-0.5" />
            <div>
              <h3 className="text-sm font-medium text-yellow-900">Upload Not Available</h3>
              <p className="text-sm text-yellow-700 mt-1">
                Please authorize Google Photos and ensure the album is created before uploading photos.
              </p>
              <div className="mt-3">
                <button
                  onClick={handleReauthorize}
                  disabled={isReauthorizing}
                  className="inline-flex items-center px-3 py-2 text-sm font-medium text-yellow-900 bg-yellow-100 border border-yellow-300 rounded-md hover:bg-yellow-200 disabled:opacity-50"
                >
                  {isReauthorizing ? 'Authorizing...' : 'Authorize Google Photos'}
                </button>
              </div>
            </div>
          </div>
        </div>
      )}
    </div>
  );
}
