'use client';

import { useState, useEffect, useRef } from 'react';
import Image from 'next/image';
import { X, Loader2, Download, Eye, AlertCircle } from 'lucide-react';
import { useToast } from '@/hooks/useToast';

interface WatermarkPreviewProps {
  file: File;
  isOpen: boolean;
  onClose: () => void;
  onConfirm?: (watermarkedUrl: string) => void;
}

export default function WatermarkPreview({ file, isOpen, onClose, onConfirm }: WatermarkPreviewProps) {
  const [watermarkedUrl, setWatermarkedUrl] = useState<string>('');
  const [originalUrl, setOriginalUrl] = useState<string>('');
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string>('');
  const [showComparison, setShowComparison] = useState(false);
  const watermarkedUrlRef = useRef<string>('');
  const toast = useToast();

  useEffect(() => {
    if (isOpen && file) {
      // Create original image URL
      const originalBlobUrl = URL.createObjectURL(file);
      setOriginalUrl(originalBlobUrl);

      // Generate watermarked preview
      generateWatermarkPreview();

      return () => {
        URL.revokeObjectURL(originalBlobUrl);
        // Clean up watermarked URL if it exists and it's a blob URL - use ref to avoid stale closure
        if (watermarkedUrlRef.current && watermarkedUrlRef.current.startsWith('blob:')) {
          URL.revokeObjectURL(watermarkedUrlRef.current);
        }
      };
    }
  }, [isOpen, file]); // Remove watermarkedUrl from dependencies to avoid stale closure

  const generateWatermarkPreview = async () => {
    setIsLoading(true);
    setError('');
    
    try {
      const formData = new FormData();
      formData.append('image', file);

      const response = await fetch('/api/admin/watermark-preview', {
        method: 'POST',
        body: formData,
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Failed to generate watermark preview');
      }

      const result = await response.json();

      // Clean up previous watermarked URL if it exists and it's a blob URL
      if (watermarkedUrlRef.current && watermarkedUrlRef.current.startsWith('blob:')) {
        URL.revokeObjectURL(watermarkedUrlRef.current);
      }

      setWatermarkedUrl(result.watermarkedUrl);
      watermarkedUrlRef.current = result.watermarkedUrl;
      
    } catch (error) {
      console.error('Error generating watermark preview:', error);
      setError(error instanceof Error ? error.message : 'Failed to generate preview');
      toast.error('Failed to generate watermark preview');
    } finally {
      setIsLoading(false);
    }
  };

  const handleDownload = () => {
    if (!watermarkedUrl) return;
    
    // Create download link
    const link = document.createElement('a');
    link.href = watermarkedUrl;
    link.download = `watermarked_${file.name}`;
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
    
    toast.success('Watermarked image downloaded');
  };

  const handleConfirm = () => {
    if (watermarkedUrl && onConfirm) {
      onConfirm(watermarkedUrl);
    }
    onClose();
  };

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 bg-black bg-opacity-75 flex items-center justify-center z-50 p-4">
      <div className="bg-white rounded-lg max-w-6xl w-full max-h-[90vh] overflow-hidden">
        {/* Header */}
        <div className="flex items-center justify-between p-4 border-b border-gray-200">
          <div>
            <h3 className="text-lg font-semibold text-gray-900">Watermark Preview</h3>
            <p className="text-sm text-gray-500">{file.name}</p>
          </div>
          <div className="flex items-center space-x-2">
            {watermarkedUrl && (
              <>
                <button
                  onClick={() => setShowComparison(!showComparison)}
                  className="inline-flex items-center px-3 py-2 border border-gray-300 rounded-md text-sm font-medium text-gray-700 bg-white hover:bg-gray-50"
                >
                  <Eye className="h-4 w-4 mr-2" />
                  {showComparison ? 'Hide' : 'Show'} Comparison
                </button>
                <button
                  onClick={handleDownload}
                  className="inline-flex items-center px-3 py-2 border border-gray-300 rounded-md text-sm font-medium text-gray-700 bg-white hover:bg-gray-50"
                >
                  <Download className="h-4 w-4 mr-2" />
                  Download
                </button>
              </>
            )}
            <button
              onClick={onClose}
              className="p-2 text-gray-400 hover:text-gray-600"
            >
              <X className="h-5 w-5" />
            </button>
          </div>
        </div>

        {/* Content */}
        <div className="p-4 overflow-y-auto max-h-[calc(90vh-140px)]">
          {isLoading && (
            <div className="flex items-center justify-center py-12">
              <div className="text-center">
                <Loader2 className="h-12 w-12 animate-spin text-purple-600 mx-auto mb-4" />
                <p className="text-lg font-medium text-gray-900">Generating watermark preview...</p>
                <p className="text-sm text-gray-500">This may take a few seconds</p>
              </div>
            </div>
          )}

          {error && (
            <div className="flex items-center justify-center py-12">
              <div className="text-center">
                <AlertCircle className="h-12 w-12 text-red-500 mx-auto mb-4" />
                <p className="text-lg font-medium text-gray-900">Preview Generation Failed</p>
                <p className="text-sm text-red-600 mb-4">{error}</p>
                <button
                  onClick={generateWatermarkPreview}
                  className="inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-purple-600 hover:bg-purple-700"
                >
                  Try Again
                </button>
              </div>
            </div>
          )}

          {watermarkedUrl && !isLoading && !error && (
            <div className="space-y-6">
              {showComparison ? (
                <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
                  {/* Original Image */}
                  <div>
                    <h4 className="text-sm font-medium text-gray-900 mb-2">Original</h4>
                    <div className="relative aspect-video bg-gray-100 rounded-lg overflow-hidden">
                      <Image
                        src={originalUrl}
                        alt="Original image"
                        fill
                        className="object-contain"
                        unoptimized
                      />
                    </div>
                  </div>

                  {/* Watermarked Image */}
                  <div>
                    <h4 className="text-sm font-medium text-gray-900 mb-2">With Watermark</h4>
                    <div className="relative aspect-video bg-gray-100 rounded-lg overflow-hidden">
                      <Image
                        src={watermarkedUrl}
                        alt="Watermarked image"
                        fill
                        className="object-contain"
                        unoptimized
                      />
                    </div>
                  </div>
                </div>
              ) : (
                <div>
                  <h4 className="text-sm font-medium text-gray-900 mb-2">Watermarked Preview</h4>
                  <div className="relative aspect-video bg-gray-100 rounded-lg overflow-hidden">
                    <Image
                      src={watermarkedUrl}
                      alt="Watermarked image preview"
                      fill
                      className="object-contain"
                      unoptimized
                    />
                  </div>
                </div>
              )}
            </div>
          )}
        </div>

        {/* Footer */}
        {watermarkedUrl && !isLoading && !error && (
          <div className="flex justify-end space-x-3 p-4 border-t border-gray-200">
            <button
              onClick={onClose}
              className="px-4 py-2 border border-gray-300 rounded-md text-sm font-medium text-gray-700 bg-white hover:bg-gray-50"
            >
              Cancel
            </button>
            {onConfirm && (
              <button
                onClick={handleConfirm}
                className="px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-purple-600 hover:bg-purple-700"
              >
                Use This Preview
              </button>
            )}
          </div>
        )}
      </div>
    </div>
  );
}
