'use client';

import { useState } from 'react';
import Link from 'next/link';
import { TripPhotoDetails } from '@/types/trip-photos';
// TripPhotoUploader removed - using modern PhotoAlbumUploader instead
import {
  Edit,
  ExternalLink,
  Camera,
  CheckCircle,
  AlertCircle,
  User,
  RefreshCw,
  Album,
  Share
} from 'lucide-react';

interface GooglePhotosViewProps {
  tripPhotoDetails: TripPhotoDetails;
}

export default function GooglePhotosView({ tripPhotoDetails }: GooglePhotosViewProps) {
  const [isReauthorizing, setIsReauthorizing] = useState(false);

  const handleReauthorize = () => {
    setIsReauthorizing(true);
    window.location.href = `/api/auth/google?tripPhotoDetailsId=${tripPhotoDetails.id}`;
  };

  const isAuthorized = tripPhotoDetails.oauth_user_email && tripPhotoDetails.oauth_refresh_token;

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="bg-white shadow-sm rounded-lg p-6">
        <div className="flex items-start justify-between">
          <div className="flex items-start space-x-4">
            <div className="bg-primary-100 p-3 rounded-lg">
              <Camera className="h-8 w-8 text-primary-600" />
            </div>
            <div>
              <h1 className="text-2xl font-bold text-gray-900">{tripPhotoDetails.trip_name}</h1>
              <p className="text-gray-600 mt-1">Google Photos Storage</p>
              {tripPhotoDetails.trip_description && (
                <p className="text-gray-700 mt-2">{tripPhotoDetails.trip_description}</p>
              )}
            </div>
          </div>
          <div className="flex space-x-3">
            <Link
              href={`/admin/trips-photos/${tripPhotoDetails.id}/edit`}
              className="inline-flex items-center px-4 py-2 border border-gray-300 shadow-sm text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50"
            >
              <Edit className="h-4 w-4 mr-2" />
              Edit Details
            </Link>
          </div>
        </div>
      </div>

      {/* Authorization Status */}
      <div className="bg-white shadow-sm rounded-lg p-6">
        <div className="flex items-center justify-between mb-4">
          <h2 className="text-lg font-semibold text-gray-900 flex items-center">
            <User className="h-5 w-5 mr-2 text-primary-600" />
            Google Photos Authorization
          </h2>
          {isAuthorized && (
            <button
              onClick={handleReauthorize}
              disabled={isReauthorizing}
              className="inline-flex items-center text-sm text-primary-600 hover:text-primary-800 disabled:opacity-50"
            >
              <RefreshCw className={`h-4 w-4 mr-1 ${isReauthorizing ? 'animate-spin' : ''}`} />
              Reauthorize
            </button>
          )}
        </div>

        {isAuthorized ? (
          <div className="space-y-4">
            <div className="flex items-start space-x-3">
              <CheckCircle className="h-5 w-5 text-green-500 mt-0.5" />
              <div>
                <p className="text-sm font-medium text-gray-900">Authorized Google Account</p>
                <p className="text-sm text-gray-600">
                  Connected to: <span className="font-medium">{tripPhotoDetails.oauth_user_email}</span>
                </p>
              </div>
            </div>

            {tripPhotoDetails.google_photos_album_id && (
              <div className="flex items-start space-x-3">
                <Album className="h-5 w-5 text-primary-600 mt-0.5" />
                <div>
                  <p className="text-sm font-medium text-gray-900">Google Photos Album</p>
                  <p className="text-sm text-gray-600">
                    Album ID: <code className="bg-gray-100 px-2 py-1 rounded text-xs">{tripPhotoDetails.google_photos_album_id}</code>
                  </p>
                  {tripPhotoDetails.manual_shareable_url && (
                    <a
                      href={tripPhotoDetails.manual_shareable_url}
                      target="_blank"
                      rel="noopener noreferrer"
                      className="inline-flex items-center text-sm text-primary-600 hover:text-primary-800 mt-1"
                    >
                      <ExternalLink className="h-4 w-4 mr-1" />
                      View Album in Google Photos
                    </a>
                  )}
                </div>
              </div>
            )}

            <div className="bg-green-50 border border-green-200 rounded-lg p-4">
              <div className="flex items-start space-x-3">
                <CheckCircle className="h-5 w-5 text-green-600 mt-0.5" />
                <div>
                  <h4 className="text-sm font-medium text-green-900">Ready for Photo Uploads</h4>
                  <p className="text-sm text-green-700 mt-1">
                    Your Google Photos account is connected and ready. Photos will be automatically uploaded to your album.
                  </p>
                </div>
              </div>
            </div>
          </div>
        ) : (
          <div className="space-y-4">
            <div className="flex items-start space-x-3">
              <AlertCircle className="h-5 w-5 text-orange-500 mt-0.5" />
              <div>
                <p className="text-sm font-medium text-gray-900">Authorization Required</p>
                <p className="text-sm text-gray-600">
                  You need to authorize access to your Google Photos account to enable uploads.
                </p>
              </div>
            </div>

            <div className="bg-orange-50 border border-orange-200 rounded-lg p-4">
              <div className="flex items-start space-x-3">
                <AlertCircle className="h-5 w-5 text-orange-600 mt-0.5" />
                <div>
                  <h4 className="text-sm font-medium text-orange-900">Action Required</h4>
                  <p className="text-sm text-orange-700 mt-1 mb-3">
                    Click the button below to authorize access to your Google Photos account.
                  </p>
                  <button
                    onClick={handleReauthorize}
                    disabled={isReauthorizing}
                    className="inline-flex items-center px-4 py-2 border border-transparent shadow-sm text-sm font-medium rounded-md text-white bg-primary-600 hover:bg-primary-700 disabled:opacity-50"
                  >
                    {isReauthorizing ? (
                      <>
                        <RefreshCw className="h-4 w-4 mr-2 animate-spin" />
                        Redirecting...
                      </>
                    ) : (
                      <>
                        <svg className="w-4 h-4 mr-2" viewBox="0 0 24 24">
                          <path fill="currentColor" d="M22.56 12.25c0-.78-.07-1.53-.2-2.25H12v4.26h5.92c-.26 1.37-1.04 2.53-2.21 3.31v2.77h3.57c2.08-1.92 3.28-4.74 3.28-8.09z"/>
                          <path fill="currentColor" d="M12 23c2.97 0 5.46-.98 7.28-2.66l-3.57-2.77c-.98.66-2.23 1.06-3.71 1.06-2.86 0-5.29-1.93-6.16-4.53H2.18v2.84C3.99 20.53 7.7 23 12 23z"/>
                          <path fill="currentColor" d="M5.84 14.09c-.22-.66-.35-1.36-.35-2.09s.13-1.43.35-2.09V7.07H2.18C1.43 8.55 1 10.22 1 12s.43 3.45 1.18 4.93l2.85-2.22.81-.62z"/>
                          <path fill="currentColor" d="M12 5.38c1.62 0 3.06.56 4.21 1.64l3.15-3.15C17.45 2.09 14.97 1 12 1 7.7 1 3.99 3.47 2.18 7.07l3.66 2.84c.87-2.6 3.3-4.53 6.16-4.53z"/>
                        </svg>
                        Authorize Google Photos
                      </>
                    )}
                  </button>
                </div>
              </div>
            </div>
          </div>
        )}
      </div>

      {/* Upload functionality is handled by the main PhotoAlbumUploader component */}

      {/* Features */}
      <div className="bg-white shadow-sm rounded-lg p-6">
        <h3 className="text-lg font-semibold text-gray-900 mb-4">Google Photos Features</h3>
        <div className="grid md:grid-cols-2 gap-4">
          <div className="flex items-start space-x-3">
            <CheckCircle className="h-5 w-5 text-green-500 mt-0.5" />
            <div>
              <p className="text-sm font-medium text-gray-900">Automatic Watermarking</p>
              <p className="text-sm text-gray-600">All uploaded photos get the Positive7 watermark</p>
            </div>
          </div>
          <div className="flex items-start space-x-3">
            <CheckCircle className="h-5 w-5 text-green-500 mt-0.5" />
            <div>
              <p className="text-sm font-medium text-gray-900">Auto Album Creation</p>
              <p className="text-sm text-gray-600">Albums are created automatically in Google Photos</p>
            </div>
          </div>
          <div className="flex items-start space-x-3">
            <CheckCircle className="h-5 w-5 text-green-500 mt-0.5" />
            <div>
              <p className="text-sm font-medium text-gray-900">Easy Sharing</p>
              <p className="text-sm text-gray-600">Share albums directly from Google Photos</p>
            </div>
          </div>
          <div className="flex items-start space-x-3">
            <CheckCircle className="h-5 w-5 text-green-500 mt-0.5" />
            <div>
              <p className="text-sm font-medium text-gray-900">Your Google Account</p>
              <p className="text-sm text-gray-600">Photos stored in your own Google Photos</p>
            </div>
          </div>
        </div>
      </div>

      {/* Access Information */}
      {tripPhotoDetails.access_password_hash && (
        <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-4">
          <div className="flex items-start space-x-3">
            <AlertCircle className="h-5 w-5 text-yellow-600 mt-0.5" />
            <div>
              <p className="text-sm font-medium text-yellow-800">Password Protected</p>
              <p className="text-sm text-yellow-700">
                This album requires a password for public access. Share the password with authorized viewers.
              </p>
            </div>
          </div>
        </div>
      )}
    </div>
  );
}
