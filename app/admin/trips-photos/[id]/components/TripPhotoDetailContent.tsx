'use client';

import { useState, useRef, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import Link from 'next/link';
import { TripPhotoDetails } from '@/types/trip-photos';
import GooglePhotosView from './GooglePhotosView';
import ConfirmationModal from '@/components/ui/ConfirmationModal';
import { AlertCircle, ArrowLeft, Trash2 } from 'lucide-react';
import { useDeletePhotoAlbum } from '@/hooks/usePhotoAlbums';

interface TripPhotoDetailContentProps {
  tripPhotoDetails: TripPhotoDetails | null;
}

export default function TripPhotoDetailContent({ tripPhotoDetails }: TripPhotoDetailContentProps) {
  const router = useRouter();
  const [error, setError] = useState<string | null>(null);
  const [showDeleteModal, setShowDeleteModal] = useState(false);
  const isMountedRef = useRef(true);

  const deletePhotoAlbumMutation = useDeletePhotoAlbum();

  // Track component mount status to avoid state updates on unmounted component
  useEffect(() => {
    return () => {
      isMountedRef.current = false;
    };
  }, []);

  const handleDeleteClick = () => {
    setShowDeleteModal(true);
  };

  const handleDeleteConfirm = async () => {
    if (!tripPhotoDetails) return;

    if (!isMountedRef.current) return;
    setError(null);

    try {
      await deletePhotoAlbumMutation.mutateAsync(tripPhotoDetails.id);

      // Only navigate if component is still mounted
      if (isMountedRef.current) {
        router.push('/admin/trips-photos');
      }
    } catch (err) {
      // Only update state if component is still mounted
      if (isMountedRef.current) {
        setError(err instanceof Error ? err.message : 'An error occurred');
      }
    }
  };

  const handleDeleteCancel = () => {
    setShowDeleteModal(false);
  };

  if (!tripPhotoDetails) {
    return (
      <div className="max-w-4xl mx-auto">
        <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-6">
          <div className="flex items-center">
            <AlertCircle className="h-8 w-8 text-yellow-600 mr-4" />
            <div>
              <h3 className="text-lg font-medium text-yellow-800">Album Not Found</h3>
              <p className="text-yellow-700 mt-1">The requested trip photo album could not be found.</p>
              <Link
                href="/admin/trips-photos"
                className="inline-flex items-center mt-3 text-sm font-medium text-yellow-700 hover:text-yellow-600"
              >
                <ArrowLeft className="h-4 w-4 mr-1" />
                Back to Trip Photos
              </Link>
            </div>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="max-w-7xl mx-auto">
      {/* Header with navigation and actions */}
      <div className="mb-6">
        <Link
          href="/admin/trips-photos"
          className="inline-flex items-center text-gray-600 hover:text-gray-900 mb-4"
        >
          <ArrowLeft className="h-4 w-4 mr-2" />
          Back to Trip Photos
        </Link>

        <div className="flex justify-between items-start">
          <div>
            <h1 className="text-3xl font-bold text-gray-900">{tripPhotoDetails.trip_name}</h1>
            <p className="text-gray-600 mt-1">
              Created {new Date(tripPhotoDetails.created_at).toLocaleDateString('en-US', {
                year: 'numeric',
                month: 'long',
                day: 'numeric',
              })}
            </p>
          </div>

          <div className="flex space-x-3">
            <button
              onClick={handleDeleteClick}
              disabled={deletePhotoAlbumMutation.isPending}
              className="inline-flex items-center px-4 py-2 border border-red-300 shadow-sm text-sm font-medium rounded-md text-red-700 bg-white hover:bg-red-50 disabled:opacity-50 disabled:cursor-not-allowed"
            >
              <Trash2 className="h-4 w-4 mr-2" />
              {deletePhotoAlbumMutation.isPending ? 'Deleting...' : 'Delete Album'}
            </button>
          </div>
        </div>
      </div>

      {/* Error Display */}
      {error && (
        <div className="bg-red-50 border border-red-200 rounded-lg p-4 mb-6">
          <div className="flex items-center">
            <AlertCircle className="h-5 w-5 text-red-600 mr-3" />
            <p className="text-sm text-red-700">{error}</p>
          </div>
        </div>
      )}

      {/* Storage-Specific Views - Only Google Photos OAuth supported */}
      {tripPhotoDetails.storage_type === 'google_photos_oauth' ? (
        <GooglePhotosView tripPhotoDetails={tripPhotoDetails} />
      ) : (
        /* Fallback for unsupported storage types */
        <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-6">
          <div className="flex items-center">
            <AlertCircle className="h-6 w-6 text-yellow-600 mr-3" />
            <div>
              <h3 className="text-lg font-medium text-yellow-800">Unsupported Storage Type</h3>
              <p className="text-yellow-700 mt-1">
                Only Google Photos OAuth is supported. Please edit the album to use Google Photos.
              </p>
              <Link
                href={`/admin/trips-photos/${tripPhotoDetails.id}/edit`}
                className="inline-flex items-center mt-3 px-4 py-2 border border-yellow-300 shadow-sm text-sm font-medium rounded-md text-yellow-700 bg-yellow-100 hover:bg-yellow-200"
              >
                Edit Album Settings
              </Link>
            </div>
          </div>
        </div>
      )}

      {/* Delete Confirmation Modal */}
      <ConfirmationModal
        isOpen={showDeleteModal}
        onClose={handleDeleteCancel}
        onConfirm={handleDeleteConfirm}
        title="Delete Trip Photo Album"
        message={`Are you sure you want to delete "${tripPhotoDetails?.trip_name}"?\n\nThis action cannot be undone and will remove the album from the database. Note: Due to Google Photos API limitations, you will need to manually delete the album from Google Photos if it exists.`}
        confirmText="Delete Album"
        variant="danger"
        loading={deletePhotoAlbumMutation.isPending}
      />
    </div>
  );
}