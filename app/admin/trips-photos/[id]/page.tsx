import { createServerSupabase } from '@/lib/supabase-server';
import AdminLayout from '@/components/layout/AdminLayout';
import PhotoAlbumUploader from './components/PhotoAlbumUploader';
import { PhotoAlbum, StorageType } from '@/types/photo-album';

// Force dynamic rendering to prevent caching
export const dynamic = 'force-dynamic';

interface PhotoAlbumPageProps {
  params: Promise<{
    id: string;
  }>;
}

export default async function PhotoAlbumPage({ params }: PhotoAlbumPageProps) {
  const { id } = await params;
  
  // Fetch the photo album from server
  const supabase = createServerSupabase();
  const { data, error } = await supabase
    .from('trip_photos_details')
    .select('*')
    .eq('id', id)
    .single();

  if (error || !data) {
    return (
      <AdminLayout>
        <div className="max-w-4xl mx-auto">
          <div className="bg-red-50 border border-red-200 rounded-lg p-6">
            <h2 className="text-lg font-semibold text-red-900 mb-2">Album Not Found</h2>
            <p className="text-red-700">
              The requested photo album could not be found. It may have been deleted or you may not have permission to access it.
            </p>
          </div>
        </div>
      </AdminLayout>
    );
  }

  // Transform the data to match our interface
  // Note: We don't expose access_password_hash to the browser for security
  const album: PhotoAlbum = {
    id: data.id,
    trip_name: data.trip_name,
    trip_description: data.trip_description || undefined,
    featured_image_url: data.featured_image_url || undefined,
    // access_password_hash is intentionally omitted for security
    storage_type: (data.storage_type as StorageType) || 'google_photos_oauth',
    google_photos_album_id: (data as any).google_photos_album_id || undefined,
    oauth_user_email: (data as any).oauth_user_email || undefined,
    // Note: Refresh tokens are handled server-side only for security
    manual_shareable_url: (data as any).manual_shareable_url || undefined,
    created_at: data.created_at || new Date().toISOString(),
    updated_at: data.updated_at || new Date().toISOString(),
  };

  return (
    <AdminLayout>
      <PhotoAlbumUploader album={album} />
    </AdminLayout>
  );
}
