import AdminLayout from '@/components/layout/AdminLayout';
import InquiryDetail from './components/InquiryDetail';

// Force dynamic rendering to prevent caching
export const dynamic = 'force-dynamic';

interface InquiryDetailPageProps {
  params: Promise<{
    id: string;
  }>;
}

export default async function InquiryDetailPage({ params }: InquiryDetailPageProps) {
  const { id } = await params;
  
  return (
    <AdminLayout>
      <InquiryDetail id={id} />
    </AdminLayout>
  );
} 