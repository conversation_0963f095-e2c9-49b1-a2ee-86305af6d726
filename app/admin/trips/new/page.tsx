'use client';

import { useState } from 'react';
import { useRouter } from 'next/navigation';
import TripForm from '../components/Trip-FormCompleted';
import { TripFormData } from '@/types/trip';
import { useToast } from '@/hooks/useToast';
import { useCreateTrip } from '@/hooks/useTrips';
import { clearTemplateValuesCache } from '@/lib/utils/trip-templates';
import AdminLayout from '@/components/layout/AdminLayout';
import { extractApiErrorInfo, sanitizeToastMessage } from '@/lib/utils/parsing';

export default function NewTripPage() {
  const router = useRouter();
  const toast = useToast();
  const [error, setError] = useState<string | null>(null);
  const createTripMutation = useCreateTrip();

  const handleSubmit = async (data: TripFormData) => {
    const toastId = toast.loading('Creating trip...');

    try {
      // Submit the form data directly without adding extra fields
      await createTripMutation.mutateAsync(data);

      // Clear template values cache so next trip gets fresh values
      clearTemplateValuesCache();

      toast.success('Trip created successfully!');
      router.push('/admin/trips');
    } catch (error) {
      // Enhanced error handling with better user feedback
      const errorInfo = extractApiErrorInfo(error);
      const sanitizedMessage = sanitizeToastMessage(errorInfo.message);
      const sanitizedDetailedError = errorInfo.detailedError ? sanitizeToastMessage(errorInfo.detailedError) : '';

      setError(sanitizedMessage);

      // Show detailed error with longer duration
      if (sanitizedDetailedError) {
        toast.error(`${sanitizedMessage} - ${sanitizedDetailedError}`, { duration: 8000 });
      } else {
        toast.error(sanitizedMessage, { duration: 6000 });
      }

      // Form state is automatically preserved since we don't navigate away
      console.error('Trip creation failed:', error);
    } finally {
      toast.dismiss(toastId);
    }
  };

  if (error) {
    return (
      <AdminLayout>
        <div className="bg-red-50 border-l-4 border-red-400 p-4">
          <div className="flex">
            <div className="flex-shrink-0">
              <svg className="h-5 w-5 text-red-400" viewBox="0 0 20 20" fill="currentColor">
                <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clipRule="evenodd" />
              </svg>
            </div>
            <div className="ml-3">
              <p className="text-sm text-red-700">{error}</p>
            </div>
          </div>
        </div>
      </AdminLayout>
    );
  }

  return (
    <AdminLayout>
      <TripForm
        onSubmit={handleSubmit}
        isLoading={createTripMutation.isPending}
        onCancel={() => router.back()}
      />
    </AdminLayout>
  );
} 