import { NextRequest, NextResponse } from 'next/server';
import { exchangeCodeForTokens, getUserInfoWithTokens } from '@/lib/google-photos-auth';
import { createAlbum } from '@/lib/google-photos-api';
import { createServerSupabase } from '@/lib/supabase-server';

export const dynamic = 'force-dynamic';

/**
 * Handle Google Photos OAuth2 callback
 * GET /api/auth/google/callback?code=xxx&state=tripPhotoDetailsId
 */
export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const code = searchParams.get('code');
    const state = searchParams.get('state'); // This is the tripPhotoDetailsId
    const error = searchParams.get('error');

    // Handle authorization errors
    if (error) {
      console.error('[GOOGLE_PHOTOS_CALLBACK] Authorization error:', error);
      const errorMessage = error === 'access_denied' 
        ? 'Authorization was denied. Please try again and grant the necessary permissions.'
        : `Authorization failed: ${error}`;
      
      return NextResponse.redirect(
        new URL(`/admin/trips-photos?error=${encodeURIComponent(errorMessage)}`, request.url)
      );
    }

    if (!code || !state) {
      console.error('[GOOGLE_PHOTOS_CALLBACK] Missing code or state parameter');
      return NextResponse.redirect(
        new URL('/admin/trips-photos?error=Invalid authorization response', request.url)
      );
    }

    const tripPhotoDetailsId = state;
    console.log(`[GOOGLE_PHOTOS_CALLBACK] Processing callback for trip: ${tripPhotoDetailsId}`);

    // Exchange authorization code for tokens
    const tokens = await exchangeCodeForTokens(code);

    console.log('[GOOGLE_PHOTOS_CALLBACK] Tokens received:', {
      hasAccessToken: !!tokens.access_token,
      hasRefreshToken: !!tokens.refresh_token,
      expiryDate: tokens.expiry_date,
    });

    if (!tokens.refresh_token) {
      console.error('[GOOGLE_PHOTOS_CALLBACK] No refresh token received');
      return NextResponse.redirect(
        new URL('/admin/trips-photos?error=Failed to get refresh token. Please try again.', request.url)
      );
    }

    // Get user information using the access token
    console.log('[GOOGLE_PHOTOS_CALLBACK] Getting user info...');
    const userInfo = await getUserInfoWithTokens(tokens);
    
    console.log(`[GOOGLE_PHOTOS_CALLBACK] Authorization successful for user: ${userInfo.email}`);

    // Get trip details to create album
    const supabase = createServerSupabase();
    const { data: tripData, error: fetchError } = await supabase
      .from('trip_photos_details')
      .select('trip_name, trip_description')
      .eq('id', tripPhotoDetailsId)
      .single();

    if (fetchError || !tripData) {
      console.error('[GOOGLE_PHOTOS_CALLBACK] Error fetching trip data:', fetchError);
      return NextResponse.redirect(
        new URL('/admin/trips-photos?error=Failed to fetch trip details. Please try again.', request.url)
      );
    }

    console.log(`[GOOGLE_PHOTOS_CALLBACK] Creating Google Photos album for: ${tripData.trip_name}`);

    // Create Google Photos album (no shareable URL generation)
    let albumId: string;

    try {
      // Create the album
      albumId = await createAlbum(
        `${tripData.trip_name} - Photos`,
        tokens.refresh_token
      );
      console.log(`[GOOGLE_PHOTOS_CALLBACK] Album created with ID: ${albumId}`);

    } catch (albumError) {
      console.error('[GOOGLE_PHOTOS_CALLBACK] Error creating album:', albumError);
      return NextResponse.redirect(
        new URL(`/admin/trips-photos?error=Failed to create Google Photos album: ${albumError instanceof Error ? albumError.message : 'Unknown error'}`, request.url)
      );
    }

    // Update the trip_photos_details record with OAuth tokens and album info
    const { error: updateError } = await supabase
      .from('trip_photos_details')
      .update({
        oauth_refresh_token: tokens.refresh_token,
        oauth_user_email: userInfo.email,
        google_photos_album_id: albumId,
        storage_type: 'google_photos_oauth',
        updated_at: new Date().toISOString(),
      })
      .eq('id', tripPhotoDetailsId);

    if (updateError) {
      console.error('[GOOGLE_PHOTOS_CALLBACK] Error updating database:', updateError);
      return NextResponse.redirect(
        new URL('/admin/trips-photos?error=Failed to save authorization and album details. Please try again.', request.url)
      );
    }

    console.log(`[GOOGLE_PHOTOS_CALLBACK] Successfully saved OAuth tokens and album details for trip: ${tripPhotoDetailsId}`);

    // Redirect to the new album creation flow with step=shareable-link
    return NextResponse.redirect(
      new URL(`/admin/trips-photos/new?step=shareable-link&albumId=${tripPhotoDetailsId}&success=Google Photos album created successfully! Album: ${tripData.trip_name} - Photos`, request.url)
    );

  } catch (error) {
    console.error('[GOOGLE_PHOTOS_CALLBACK] Error processing callback:', error);
    
    return NextResponse.redirect(
      new URL(
        `/admin/trips-photos?error=${encodeURIComponent(
          `Authorization failed: ${error instanceof Error ? error.message : 'Unknown error'}`
        )}`,
        request.url
      )
    );
  }
}
