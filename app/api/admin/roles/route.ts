import { NextRequest, NextResponse } from 'next/server';
import { verifyAdminAccess, createAdminClient } from '@/lib/auth-server';

// GET /api/admin/roles - List all roles
export async function GET(request: NextRequest) {
  try {
    const { user, hasAccess } = await verifyAdminAccess();

    if (!hasAccess || !user) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 403 }
      );
    }

    const adminSupabase = createAdminClient();

    const { data: roles, error } = await adminSupabase
      .from('admin_roles')
      .select('*')
      .order('name');

    if (error) {
      console.error('Error fetching roles:', error);
      return NextResponse.json(
        { error: 'Failed to fetch roles' },
        { status: 500 }
      );
    }

    return NextResponse.json({ roles });

  } catch (error: any) {
    console.error('Get roles error:', error);
    return NextResponse.json(
      { error: 'Failed to fetch roles', details: error.message },
      { status: 500 }
    );
  }
}
