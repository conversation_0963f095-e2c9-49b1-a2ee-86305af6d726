import { NextRequest, NextResponse } from 'next/server';
import { performanceMonitor, measureApiCall } from '@/lib/performance-monitor';

export const dynamic = 'force-dynamic';

// POST /api/admin/performance/custom-metrics - Receive custom performance metrics
export async function POST(request: NextRequest) {
  return measureApiCall('admin_performance_custom_metrics', async () => {
    try {
    const body = await request.json();
    
    const {
      metric_name,
      value,
      unit = 'ms',
      timestamp,
      metadata = {}
    } = body;

    // Validate required fields
    if (!metric_name || typeof value !== 'number') {
      return NextResponse.json(
        { error: 'Missing required fields: metric_name and value' },
        { status: 400 }
      );
    }

    // Record the custom metric
    performanceMonitor.recordMetric({
      timestamp: timestamp || new Date().toISOString(),
      metric_type: 'custom',
      value,
      unit,
      metadata: {
        custom_metric_name: metric_name,
        source: 'client',
        ...metadata,
      },
    });

    return NextResponse.json({ success: true });

  } catch (error) {
    console.error('Error recording custom performance metric:', error);
    return NextResponse.json(
      { error: 'Failed to record custom metric' },
      { status: 500 }
    );
  }
  }, '/api/admin/performance/custom-metrics');
}
