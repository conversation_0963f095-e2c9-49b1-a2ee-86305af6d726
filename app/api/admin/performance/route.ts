import { NextRequest, NextResponse } from 'next/server';
import { verifyOwnerAccess } from '@/lib/auth-server';
import { performanceMonitor, measureApiCall } from '@/lib/performance-monitor';
import { globalCacheManager } from '@/lib/cache-manager';
import { logAdminAccess, getRequestContext } from '@/lib/audit-logger';
import { handleApiError } from '@/lib/error-handler';

// Force dynamic rendering
export const dynamic = 'force-dynamic';

// GET /api/admin/performance - Get performance metrics and statistics
export async function GET(request: NextRequest): Promise<NextResponse> {
  return measureApiCall('admin_performance_get', async () => {
    try {
    // Check admin authentication and permissions - only owners can access performance data
    const { user, hasAccess } = await verifyOwnerAccess();

    if (!hasAccess || !user) {
      return NextResponse.json(
        { error: 'Unauthorized. Owner privileges required to view performance data.' },
        { status: 403 }
      );
    }

    const { searchParams } = new URL(request.url);
    
    // Parse query parameters with validation
    const timeRangeParam = searchParams.get('timeRange') || '3600000';
    const timeRange = Math.max(60000, Math.min(86400000, parseInt(timeRangeParam, 10) || 3600000)); // 1 min to 24 hours
    const includeCache = searchParams.get('includeCache') === 'true';
    const includeDetails = searchParams.get('includeDetails') === 'true';

    // Get performance statistics
    const performanceStats = performanceMonitor.getStatistics(timeRange);
    
    // Get cache statistics if requested
    let cacheStats = null;
    if (includeCache) {
      cacheStats = globalCacheManager.getAllStats();
    }

    // Get system information
    const systemInfo = {
      timestamp: new Date().toISOString(),
      timeRange: timeRange,
      nodeVersion: process.version,
      platform: process.platform,
      uptime: process.uptime(),
      serverMemoryUsage: process.memoryUsage(), // Server-side Node.js memory
    };

    // Calculate performance summary
    const summary = {
      totalMetrics: performanceStats.total,
      averageResponseTime: performanceStats.averages.api_response_time || 0,
      averageDbQueryTime: performanceStats.averages.database_query || 0,
      averagePageLoadTime: performanceStats.averages.page_load || 0,
      healthScore: calculateHealthScore(performanceStats),
    };

    // Prepare response data
    const responseData: any = {
      success: true,
      summary,
      systemInfo,
      performance: {
        statistics: performanceStats,
        thresholds: {
          api_response_time: { good: 200, warning: 500, critical: 1000 },
          database_query: { good: 100, warning: 300, critical: 1000 },
          page_load: { good: 1000, warning: 3000, critical: 5000 },
        },
      },
    };

    if (includeCache) {
      responseData.cache = cacheStats;
    }

    if (includeDetails) {
      responseData.details = {
        slowestOperations: performanceStats.slowestOperations,
        serverMemoryBreakdown: {
          rss: `${(systemInfo.serverMemoryUsage.rss / 1024 / 1024).toFixed(2)} MB`,
          heapTotal: `${(systemInfo.serverMemoryUsage.heapTotal / 1024 / 1024).toFixed(2)} MB`,
          heapUsed: `${(systemInfo.serverMemoryUsage.heapUsed / 1024 / 1024).toFixed(2)} MB`,
          external: `${(systemInfo.serverMemoryUsage.external / 1024 / 1024).toFixed(2)} MB`,
          note: "This is Node.js server memory, not browser memory. Client memory is tracked separately.",
        },
      };
    }

    // Performance dashboard access is a read operation - no audit logging needed

    return NextResponse.json(responseData);

  } catch (error) {
    const appError = handleApiError(error);
    return NextResponse.json(
      { error: appError.message },
      { status: appError.statusCode }
    );
  }
  }, '/api/admin/performance');
}

// POST /api/admin/performance - Clear performance metrics or cache
export async function POST(request: NextRequest): Promise<NextResponse> {
  return measureApiCall('admin_performance_post', async () => {
    try {
    // Check admin authentication and permissions - only owners can modify performance settings
    const { user, hasAccess } = await verifyOwnerAccess();

    if (!hasAccess || !user) {
      return NextResponse.json(
        { error: 'Unauthorized. Owner privileges required.' },
        { status: 403 }
      );
    }

    const body = await request.json();
    const { action } = body;
    const requestContext = getRequestContext(request);

    const result: any = { success: true };

    switch (action) {
      case 'clear_metrics': {
        performanceMonitor.clearMetrics();
        result.message = 'Performance metrics cleared';
        break;
      }

      case 'clear_cache': {
        globalCacheManager.clearAll();
        result.message = 'All caches cleared';
        break;
      }

      case 'cleanup_cache': {
        const cleanupResults = globalCacheManager.cleanupAll();
        const totalCleaned = Object.values(cleanupResults).reduce((sum: number, count: number) => sum + count, 0);
        result.message = `Cleaned up ${totalCleaned} expired cache entries`;
        result.details = cleanupResults;
        break;
      }

      default:
        return NextResponse.json(
          { error: 'Invalid action. Supported actions: clear_metrics, clear_cache, cleanup_cache' },
          { status: 400 }
        );
    }

    // Log the action
    await logAdminAccess(
      user.id,
      user.username || 'Unknown',
      'performance_dashboard',
      `performance action: ${action}`,
      {
        ...requestContext,
        metadata: {
          action,
          result,
        },
      }
    );

    return NextResponse.json(result);

  } catch (error) {
    const appError = handleApiError(error);
    return NextResponse.json(
      { error: appError.message },
      { status: appError.statusCode }
    );
  }
  }, '/api/admin/performance');
}

/**
 * Calculate overall system health score based on performance metrics
 */
function calculateHealthScore(stats: any): number {
  let score = 100;

  // Deduct points for slow average response times
  if (stats.averages.api_response_time > 1000) {
    score -= 20;
  } else if (stats.averages.api_response_time > 500) {
    score -= 10;
  }

  // Deduct points for slow database queries
  if (stats.averages.database_query > 1000) {
    score -= 15;
  } else if (stats.averages.database_query > 300) {
    score -= 7;
  }

  // Deduct points for slow page loads
  if (stats.averages.page_load > 5000) {
    score -= 15;
  } else if (stats.averages.page_load > 3000) {
    score -= 7;
  }

  return Math.max(score, 0);
}
