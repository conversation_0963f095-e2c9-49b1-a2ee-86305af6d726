import { NextRequest, NextResponse } from 'next/server';
import { performanceMonitor, measureApiCall } from '@/lib/performance-monitor';

export const dynamic = 'force-dynamic';

// POST /api/admin/performance/client-metrics - Receive client-side performance metrics
export async function POST(request: NextRequest) {
  return measureApiCall('admin_performance_client_metrics', async () => {
    try {
    const body = await request.json();
    
    const {
      page_name,
      load_time,
      dom_content_loaded,
      first_paint,
      largest_contentful_paint,
      first_input_delay,
      cumulative_layout_shift
    } = body;

    // Validate required fields
    if (!page_name || typeof load_time !== 'number') {
      return NextResponse.json(
        { error: 'Missing required fields: page_name and load_time' },
        { status: 400 }
      );
    }

    // Record the main page load metric
    performanceMonitor.recordMetric({
      timestamp: new Date().toISOString(),
      metric_type: 'page_load',
      value: load_time,
      unit: 'ms',
      metadata: {
        page_name,
        dom_content_loaded,
        first_paint,
        largest_contentful_paint,
        first_input_delay,
        cumulative_layout_shift,
        source: 'client',
      },
    });

    // Record additional Web Vitals as separate metrics if available
    if (largest_contentful_paint) {
      performanceMonitor.recordMetric({
        timestamp: new Date().toISOString(),
        metric_type: 'web_vitals',
        value: largest_contentful_paint,
        unit: 'ms',
        metadata: {
          page_name,
          vital_type: 'LCP',
          source: 'client',
        },
      });
    }

    if (first_input_delay) {
      performanceMonitor.recordMetric({
        timestamp: new Date().toISOString(),
        metric_type: 'web_vitals',
        value: first_input_delay,
        unit: 'ms',
        metadata: {
          page_name,
          vital_type: 'FID',
          source: 'client',
        },
      });
    }

    if (cumulative_layout_shift) {
      performanceMonitor.recordMetric({
        timestamp: new Date().toISOString(),
        metric_type: 'web_vitals',
        value: cumulative_layout_shift,
        unit: 'score',
        metadata: {
          page_name,
          vital_type: 'CLS',
          source: 'client',
        },
      });
    }

    return NextResponse.json({ success: true });

  } catch (error) {
    // Error recording client performance metrics - fail silently
    return NextResponse.json(
      { error: 'Failed to record performance metrics' },
      { status: 500 }
    );
  }
  }, '/api/admin/performance/client-metrics');
}
