import { NextRequest, NextResponse } from 'next/server';
import { addWatermarkToImage } from '@/lib/watermark';
import { handleApiError } from '@/lib/error-handler';

export async function POST(request: NextRequest) {
  try {
    console.log('[WATERMARK_PREVIEW] Starting watermark preview generation');

    const formData = await request.formData();
    const image = formData.get('image') as File;

    // Validation
    if (!image) {
      return NextResponse.json(
        { error: 'No image file provided' },
        { status: 400 }
      );
    }

    console.log(`[WATERMARK_PREVIEW] Processing image: ${image.name} (${image.size} bytes, ${image.type})`);

    // Check file size (limit to 10MB for serverless)
    const maxSize = 10 * 1024 * 1024; // 10MB
    if (image.size > maxSize) {
      return NextResponse.json(
        { error: 'Image file too large. Maximum size is 10MB.' },
        { status: 400 }
      );
    }

    // Check file type
    const allowedTypes = ['image/jpeg', 'image/jpg', 'image/png', 'image/webp'];
    if (!allowedTypes.includes(image.type)) {
      return NextResponse.json(
        { error: 'Invalid file type. Only JPEG, PNG, and WebP are supported.' },
        { status: 400 }
      );
    }

    // Convert File to Buffer with error handling
    let buffer: Buffer;
    try {
      const arrayBuffer = await image.arrayBuffer();
      buffer = Buffer.from(arrayBuffer);
      console.log(`[WATERMARK_PREVIEW] Image buffer created: ${buffer.length} bytes`);
    } catch (bufferError) {
      console.error('[WATERMARK_PREVIEW] Error creating buffer:', bufferError);
      return NextResponse.json(
        { error: 'Failed to process image data' },
        { status: 400 }
      );
    }

    // Add watermark with detailed error handling
    let watermarkedBuffer: Buffer;
    try {
      watermarkedBuffer = await addWatermarkToImage(buffer);
      console.log(`[WATERMARK_PREVIEW] Watermark applied: ${watermarkedBuffer.length} bytes`);
    } catch (watermarkError) {
      console.error('[WATERMARK_PREVIEW] Error applying watermark:', watermarkError);
      return NextResponse.json(
        { error: 'Failed to apply watermark. Please try again.' },
        { status: 500 }
      );
    }

    // Create base64 data URL for immediate display
    const watermarkedBase64 = `data:image/jpeg;base64,${watermarkedBuffer.toString('base64')}`;

    console.log('[WATERMARK_PREVIEW] ✅ Preview generated successfully');

    return NextResponse.json({
      success: true,
      watermarkedUrl: watermarkedBase64,
      originalSize: image.size,
      watermarkedSize: watermarkedBuffer.length,
      message: 'Watermark preview generated successfully'
    });

  } catch (error) {
    console.error('[WATERMARK_PREVIEW] ❌ Unexpected error:', error);
    const appError = handleApiError(error);
    return NextResponse.json(
      { error: appError.message },
      { status: appError.statusCode }
    );
  }
}

// Set runtime to nodejs for better performance with image processing
export const runtime = 'nodejs';

// Set max duration for serverless functions (Vercel Pro: 60s, Hobby: 10s)
export const maxDuration = 30;
