import { NextRequest, NextResponse } from 'next/server';
import { verifyAdminAccess } from '@/lib/auth-server';
import { cleanupSingleImage } from '@/lib/cloudinary';
import { handleApiError } from '@/lib/error-handler';

// POST /api/admin/cloudinary/cleanup-single - Cleanup a single image immediately
export async function POST(request: NextRequest) {
  try {
    // Verify admin access
    const { user, hasAccess } = await verifyAdminAccess();

    if (!hasAccess || !user) {
      return NextResponse.json(
        { error: 'Unauthorized. Admin privileges required.' },
        { status: 403 }
      );
    }

    const body = await request.json();
    const { publicId, context, shouldCleanupFolder } = body;

    if (!publicId) {
      return NextResponse.json(
        { error: 'publicId is required' },
        { status: 400 }
      );
    }

    console.log(`🧹 Admin ${user.username || user.email} initiated cleanup for: ${publicId} (${context || 'no-context'}) ${shouldCleanupFolder ? 'with folder cleanup' : ''}`);

    const result = await cleanupSingleImage(publicId, context || 'single-cleanup', shouldCleanupFolder);

    if (result.success) {
      console.log(`✅ Successfully cleaned up image: ${publicId} via API endpoint`);
      return NextResponse.json({
        success: true,
        message: `Successfully cleaned up image: ${publicId}`
      });
    } else {
      console.error(`❌ Failed to cleanup image via API: ${publicId} - ${result.error}`);
      return NextResponse.json({
        success: false,
        error: result.error,
        message: `Failed to cleanup image: ${publicId}`
      }, { status: 500 });
    }

  } catch (error: any) {
    const appError = handleApiError(error);
    console.error('❌ Error in single cleanup endpoint:', appError);
    return NextResponse.json(
      { error: appError.message },
      { status: appError.statusCode }
    );
  }
}
