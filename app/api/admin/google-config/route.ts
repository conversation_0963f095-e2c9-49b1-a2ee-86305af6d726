import { NextRequest, NextResponse } from 'next/server';
import { getGoogleDriveServiceAccountEmail, isGoogleDriveConfigured, isGooglePhotosConfigured } from '@/lib/google-config';

/**
 * GET /api/admin/google-config
 * Returns Google configuration information for admin UI
 */
export async function GET(_request: NextRequest) {
  try {
    console.log('[API] Getting Google configuration for admin UI');
    
    const config = {
      googleDrive: {
        configured: isGoogleDriveConfigured(),
        serviceAccountEmail: getGoogleDriveServiceAccountEmail(),
      },
      googlePhotos: {
        configured: isGooglePhotosConfigured(),
      },
    };
    
    console.log('[API] Google configuration retrieved:', {
      driveConfigured: config.googleDrive.configured,
      photosConfigured: config.googlePhotos.configured,
      serviceAccountEmail: config.googleDrive.serviceAccountEmail,
    });
    
    return NextResponse.json({
      success: true,
      data: config,
    });
  } catch (error) {
    console.error('[API] Error getting Google configuration:', error);
    
    return NextResponse.json(
      {
        success: false,
        error: 'Failed to get Google configuration',
        details: error instanceof Error ? error.message : 'Unknown error',
      },
      { status: 500 }
    );
  }
}
