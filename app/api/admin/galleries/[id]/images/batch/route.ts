import { NextRequest, NextResponse } from 'next/server';
import { verifyAdminAccess, createAdminClient } from '@/lib/auth-server';

// POST /api/admin/galleries/[id]/images/batch - Add multiple images to gallery
// NOTE: No rate limiting applied - admins need to upload large batches of images (200-250 at a time)
// This endpoint is specifically designed for bulk gallery image operations
export async function POST(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const { id } = await params;
    
    // Verify admin access
    const { user, hasAccess } = await verifyAdminAccess('galleries', 'create');

    if (!hasAccess || !user) {
      return NextResponse.json(
        { error: 'Unauthorized. Admin privileges required.' },
        { status: 403 }
      );
    }

    const supabase = createAdminClient();
    const body = await request.json();

    // Validate request body
    if (!body.images || !Array.isArray(body.images) || body.images.length === 0) {
      return NextResponse.json(
        { error: 'Images array is required and must not be empty' },
        { status: 400 }
      );
    }

    // Validate each image
    const requiredFields = ['image_url', 'cloudinary_public_id'];
    for (let i = 0; i < body.images.length; i++) {
      const image = body.images[i];
      for (const field of requiredFields) {
        if (!image[field]) {
          return NextResponse.json(
            { error: `Missing required field '${field}' in image ${i + 1}` },
            { status: 400 }
          );
        }
      }
    }

    // Verify gallery exists
    const { data: gallery, error: galleryError } = await supabase
      .from('galleries')
      .select('id')
      .eq('id', id)
      .single();

    if (galleryError || !gallery) {
      return NextResponse.json(
        { error: 'Gallery not found' },
        { status: 404 }
      );
    }

    // Get the current max order index with better race condition handling
    const { data: lastImage, error: lastImageError } = await supabase
      .from('gallery_images')
      .select('order_index')
      .eq('gallery_id', id)
      .order('order_index', { ascending: false })
      .limit(1)
      .maybeSingle(); // Use maybeSingle to handle empty results gracefully

    // Handle potential errors from maybeSingle
    if (lastImageError) {
      console.error('Error fetching last image order index:', lastImageError);
      return NextResponse.json(
        { error: 'Failed to determine image order' },
        { status: 500 }
      );
    }

    // Use timestamp-based ordering to avoid race conditions
    const baseOrderIndex = (lastImage?.order_index ?? 0) + 1;
    const timestamp = Date.now();

    // Prepare images for batch insert with unique order indices to prevent race conditions
    const imagesToInsert = body.images.map((image: any, index: number) => ({
      gallery_id: id,
      image_url: image.image_url,
      cloudinary_public_id: image.cloudinary_public_id,
      order_index: image.order_index || (baseOrderIndex + index + timestamp % 1000), // Add timestamp component
    }));

    // Insert all images in a single transaction
    const { data: insertedImages, error: insertError } = await supabase
      .from('gallery_images')
      .insert(imagesToInsert)
      .select();

    if (insertError) {
      console.error('Error inserting gallery images:', insertError);
      return NextResponse.json(
        { error: 'Failed to save images to gallery' },
        { status: 500 }
      );
    }

    return NextResponse.json({
      data: insertedImages,
      message: `Successfully added ${insertedImages.length} images to gallery`
    });

  } catch (error) {
    console.error('Error in batch gallery images upload:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}
