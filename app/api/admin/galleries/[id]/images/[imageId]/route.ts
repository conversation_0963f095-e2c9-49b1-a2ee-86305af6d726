import { NextRequest, NextResponse } from 'next/server';
import { verifyAdminAccess, createAdminClient } from '@/lib/auth-server';
import { deleteFromCloudinary } from '@/lib/cloudinary';

// PUT /api/admin/galleries/[id]/images/[imageId] - Update a gallery image
export async function PUT(
  request: NextRequest,
  { params }: { params: Promise<{ id: string; imageId: string }> }
) {
  try {
    const { id, imageId } = await params;
    
    // Verify admin access
    const { user, hasAccess } = await verifyAdminAccess('galleries', 'update');

    if (!hasAccess || !user) {
      return NextResponse.json(
        { error: 'Unauthorized. Admin privileges required.' },
        { status: 403 }
      );
    }

    const supabase = createAdminClient();
    const body = await request.json();

    // Validate request body
    if (!body || typeof body !== 'object') {
      return NextResponse.json(
        { error: 'Invalid request body' },
        { status: 400 }
      );
    }

    // Validate allowed fields
    const allowedFields = ['order_index'];
    const validatedBody: Record<string, any> = {};

    for (const [key, value] of Object.entries(body)) {
      if (allowedFields.includes(key)) {
        if (key === 'order_index' && value !== null && typeof value !== 'number') {
          return NextResponse.json(
            { error: 'order_index must be a number' },
            { status: 400 }
          );
        }
        validatedBody[key] = value;
      }
    }

    // Verify image belongs to the gallery
    const { data: existingImage, error: imageError } = await supabase
      .from('gallery_images')
      .select('*')
      .eq('id', imageId)
      .eq('gallery_id', id)
      .single();

    if (imageError || !existingImage) {
      return NextResponse.json(
        { error: 'Gallery image not found' },
        { status: 404 }
      );
    }

    // Update image in database
    const { data: image, error: dbError } = await supabase
      .from('gallery_images')
      .update(validatedBody)
      .eq('id', imageId)
      .eq('gallery_id', id)
      .select()
      .single();

    if (dbError) {
      console.error('Error updating gallery image:', dbError);
      return NextResponse.json(
        { error: 'Failed to update gallery image' },
        { status: 500 }
      );
    }

    return NextResponse.json({ data: image });
  } catch (error) {
    console.error('Error in PUT /api/admin/galleries/[id]/images/[imageId]:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}

// DELETE /api/admin/galleries/[id]/images/[imageId] - Delete a gallery image
export async function DELETE(
  request: NextRequest,
  { params }: { params: Promise<{ id: string; imageId: string }> }
) {
  try {
    const { id, imageId } = await params;
    
    // Verify admin access
    const { user, hasAccess } = await verifyAdminAccess('galleries', 'delete');

    if (!hasAccess || !user) {
      return NextResponse.json(
        { error: 'Unauthorized. Admin privileges required.' },
        { status: 403 }
      );
    }

    const supabase = createAdminClient();

    // Get image details for Cloudinary deletion
    const { data: image, error: imageError } = await supabase
      .from('gallery_images')
      .select('cloudinary_public_id')
      .eq('id', imageId)
      .eq('gallery_id', id)
      .single();

    if (imageError || !image) {
      return NextResponse.json(
        { error: 'Gallery image not found' },
        { status: 404 }
      );
    }

    // Delete image from Cloudinary
    try {
      await deleteFromCloudinary(image.cloudinary_public_id);
    } catch (cloudinaryError) {
      console.error('Error deleting image from Cloudinary:', cloudinaryError);
      // Continue with database deletion even if Cloudinary deletion fails
    }

    // Delete image from database
    const { error: deleteError } = await supabase
      .from('gallery_images')
      .delete()
      .eq('id', imageId)
      .eq('gallery_id', id);

    if (deleteError) {
      console.error('Error deleting gallery image:', deleteError);
      return NextResponse.json(
        { error: 'Failed to delete gallery image' },
        { status: 500 }
      );
    }

    return NextResponse.json({ message: 'Gallery image deleted successfully' });
  } catch (error) {
    console.error('Error in DELETE /api/admin/galleries/[id]/images/[imageId]:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}
