import { NextRequest, NextResponse } from 'next/server';
import { verifyAdminAccess, createAdminClient } from '@/lib/auth-server';
import { cronToNaturalLanguage } from '@/lib/utils';

export async function GET(_request: NextRequest) {
  try {
    // Verify admin authentication
    const { user, hasAccess } = await verifyAdminAccess();

    if (!hasAccess || !user) {
      return NextResponse.json(
        { error: 'Unauthorized. Admin privileges required.' },
        { status: 403 }
      );
    }

    const supabase = createAdminClient();

    // Get actual cron job data from database
    const { data: cronJobs, error: cronError } = await supabase.rpc('get_cron_job_details');

    if (cronError) {
      console.error('Error getting cron jobs:', cronError);
    }

    // Get simplified deactivation status
    const { data: statusData, error: statusError } = await supabase.rpc('get_deactivation_status');

    if (statusError) {
      console.error('Error getting deactivation status:', statusError);
      return NextResponse.json(
        { error: 'Failed to get deactivation status' },
        { status: 500 }
      );
    }

    // Get trips with auto deactivation
    const { data: tripsData, error: tripsError } = await supabase
      .from('trips')
      .select('id, title, auto_deactivation_date, is_active')
      .not('auto_deactivation_date', 'is', null)
      .order('auto_deactivation_date', { ascending: true });
    
    if (tripsError) {
      console.error('Error getting trips with auto-deactivation:', tripsError);
      return NextResponse.json(
        { error: 'Failed to get trips' },
        { status: 500 }
      );
    }

    const status = statusData?.[0] || {
      cron_job_exists: true,
      trips_with_auto_deactivation: 0,
      active_trips_with_auto_deactivation: 0,
      expired_trips_count: 0
    };

    // Use actual cron job data or create fallback
    let jobs = cronJobs || [];

    // If no cron jobs found, create a fallback (this shouldn't happen in normal operation)
    if (jobs.length === 0) {
      jobs = [{
        job_id: 1,
        job_name: 'auto_deactivate_trips',
        schedule: '*/5 * * * *', // Default to current known schedule
        command: 'SELECT deactivate_expired_trips();',
        active: status.cron_job_exists,
        last_run_started_at: null,
        last_run_status: 'active'
      }];
    }

    // Add natural language schedule to each job
    const jobsWithNaturalSchedule = jobs.map(job => ({
      ...job,
      schedule_natural: cronToNaturalLanguage(job.schedule)
    }));

    return NextResponse.json({
      success: true,
      jobs: jobsWithNaturalSchedule,
      trips: tripsData || [],
      status: status
    });

  } catch (error) {
    console.error('Auto deactivation status API error:', error);
    return NextResponse.json(
      { error: 'Failed to fetch auto deactivation status' },
      { status: 500 }
    );
  }
}
