import { NextRequest, NextResponse } from 'next/server';
import { verifyAdminAccess, createAdminClient } from '@/lib/auth-server';

export async function POST(_request: NextRequest) {
  try {
    // Verify admin authentication
    const { user, hasAccess } = await verifyAdminAccess();

    if (!hasAccess || !user) {
      return NextResponse.json(
        { error: 'Unauthorized. Admin privileges required.' },
        { status: 403 }
      );
    }

    const supabase = createAdminClient();
    
    // Trigger the deactivation function
    const { data, error } = await supabase.rpc('deactivate_expired_trips');
    
    if (error) {
      console.error('Error triggering trip deactivation:', error);
      return NextResponse.json(
        { error: 'Failed to trigger deactivation', details: error.message },
        { status: 500 }
      );
    }

    return NextResponse.json({
      success: true,
      count: data || 0,
      message: `Successfully processed ${data || 0} trips`
    });

  } catch (error) {
    console.error('Auto deactivation trigger API error:', error);
    return NextResponse.json(
      { error: 'Failed to trigger auto deactivation' },
      { status: 500 }
    );
  }
}
