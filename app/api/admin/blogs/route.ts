import { NextRequest, NextResponse } from 'next/server';
import { verifyAdminAccess, createAdminClient } from '@/lib/auth-server';
import { createSafeSearchCondition } from '@/lib/input-sanitization';
import { logBlog<PERSON>rud, crudAuditLogger } from '@/lib/crud-audit-logger';

// GET /api/admin/blogs - Get all blogs with pagination and filters
export async function GET(request: NextRequest) {
  try {
    // Verify admin access
    const { user, hasAccess } = await verifyAdminAccess('blog', 'read');

    if (!hasAccess || !user) {
      return NextResponse.json(
        { error: 'Unauthorized. Admin privileges required.' },
        { status: 403 }
      );
    }

    const supabase = createAdminClient();
    const { searchParams } = new URL(request.url);

    // Parse query parameters with validation
    const pageParam = searchParams.get('page') || '1';
    const limitParam = searchParams.get('limit') || '10';
    const page = Math.max(1, parseInt(pageParam, 10) || 1);
    const limit = Math.max(1, Math.min(100, parseInt(limitParam, 10) || 10));
    const search = searchParams.get('search');
    const category = searchParams.get('category');
    const isPublished = searchParams.get('isPublished');

    // Build query
    let query = supabase
      .from('blog_posts')
      .select('*', { count: 'exact' });

    // Apply filters with safe search
    if (search) {
      const searchConditions = createSafeSearchCondition(search, ['title', 'content']);
      if (searchConditions) {
        query = query.or(searchConditions);
      }
    }
    if (category && category !== 'all') {
      query = query.eq('category', category);
    }
    if (isPublished !== null) {
      query = query.eq('is_published', isPublished === 'true');
    }

    // Apply pagination
    const from = (page - 1) * limit;
    const to = from + limit - 1;

    query = query
      .order('created_at', { ascending: false })
      .range(from, to);

    const { data: blogs, error, count } = await query;

    if (error) {
      console.error('Error fetching blogs:', error);
      return NextResponse.json(
        { error: 'Failed to fetch blogs' },
        { status: 500 }
      );
    }

    const totalPages = Math.ceil((count || 0) / limit);

    return NextResponse.json({
      data: blogs || [],
      pagination: {
        page,
        limit,
        total: count || 0,
        totalPages,
      },
    });
  } catch (error) {
    console.error('Error in GET /api/admin/blogs:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}

// POST /api/admin/blogs - Create a new blog
export async function POST(request: NextRequest) {
  try {
    // Verify admin access
    const { user, hasAccess } = await verifyAdminAccess('blog', 'create');

    if (!hasAccess || !user) {
      return NextResponse.json(
        { error: 'Unauthorized. Admin privileges required.' },
        { status: 403 }
      );
    }

    const supabase = createAdminClient();
    const body = await request.json();

    // Basic validation
    const requiredFields = ['title', 'content', 'author'];
    for (const field of requiredFields) {
      if (!body[field] || (typeof body[field] === 'string' && !body[field].trim())) {
        return NextResponse.json(
          { error: `Missing required field: ${field}` },
          { status: 400 }
        );
      }
    }

    // Generate slug from title
    const slug = body.title
      .toLowerCase()
      .replace(/[^a-z0-9]+/g, '-')
      .replace(/(^-|-$)/g, '');

    // Set published_at if is_published is true
    if (body.is_published) {
      body.published_at = new Date().toISOString();
    }

    // Save blog to database
    const { data: blog, error: dbError } = await supabase
      .from('blog_posts')
      .insert({
        ...body,
        slug,
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString(),
      })
      .select()
      .single();

    if (dbError) {
      console.error('Error saving blog to database:', dbError);

      // Log failed creation
      await crudAuditLogger.logFailedOperation(
        'create',
        'blog_post',
        'unknown',
        user.id,
        user.username || 'Unknown',
        dbError.message,
        request
      );

      return NextResponse.json(
        { error: 'Failed to save blog' },
        { status: 500 }
      );
    }

    // Log successful creation
    await logBlogCrud.create(
      blog.id,
      blog.title,
      user.id,
      user.username || 'Unknown',
      body,
      request
    );

    return NextResponse.json({ data: blog });
  } catch (error) {
    console.error('Error in POST /api/admin/blogs:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
} 