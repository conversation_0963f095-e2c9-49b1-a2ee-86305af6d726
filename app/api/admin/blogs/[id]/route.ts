import { NextRequest, NextResponse } from 'next/server';
import { verifyAdminAccess, createAdminClient } from '@/lib/auth-server';
import { logBlogCrud, crudAuditLogger, CrudAuditLogger } from '@/lib/crud-audit-logger';
import { handleApiError } from '@/lib/error-handler';
import { deleteFromCloudinary, extractCloudinaryPublicId, cleanupSingleImage } from '@/lib/cloudinary';
import { logBlogImageReplacement, areCloudinaryUrlsSameImage } from '@/lib/debug-cloudinary';

// Force dynamic rendering to prevent caching
export const dynamic = 'force-dynamic';

// GET /api/admin/blogs/[id] - Get a single blog
export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    // Verify admin access
    const { user, hasAccess } = await verifyAdminAccess('blog', 'read');

    if (!hasAccess || !user) {
      return NextResponse.json(
        { error: 'Unauthorized. Admin privileges required.' },
        { status: 403 }
      );
    }

    const { id } = await params;
    const supabase = createAdminClient();
    const { data: blog, error } = await supabase
      .from('blog_posts')
      .select('*')
      .eq('id', id)
      .single();

    if (error) {
      const appError = handleApiError(error);
      return NextResponse.json(
        { error: appError.message },
        { status: appError.statusCode }
      );
    }

    if (!blog) {
      return NextResponse.json(
        { error: 'Blog not found' },
        { status: 404 }
      );
    }

    return NextResponse.json({ data: blog });
  } catch (error) {
    const appError = handleApiError(error);
    return NextResponse.json(
      { error: appError.message },
      { status: appError.statusCode }
    );
  }
}

// PUT /api/admin/blogs/[id] - Update a blog
export async function PUT(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    // Verify admin access
    const { user, hasAccess } = await verifyAdminAccess('blog', 'update');

    if (!hasAccess || !user) {
      return NextResponse.json(
        { error: 'Unauthorized. Admin privileges required.' },
        { status: 403 }
      );
    }

    const { id } = await params;
    const supabase = createAdminClient();
    const body = await request.json();

    // Get original blog data for change tracking
    const { data: originalBlog } = await supabase
      .from('blog_posts')
      .select('*')
      .eq('id', id)
      .single();

    // Basic validation
    const requiredFields = ['title', 'content', 'author'];
    for (const field of requiredFields) {
      if (!body[field] || (typeof body[field] === 'string' && !body[field].trim())) {
        return NextResponse.json(
          { error: `Missing required field: ${field}` },
          { status: 400 }
        );
      }
    }

    // Store old image URL for cleanup after successful DB update
    let oldImageToDelete: string | null = null;
    if (body.featured_image_url && originalBlog?.featured_image_url &&
        body.featured_image_url !== originalBlog.featured_image_url) {

      // Debug logging for image replacement
      logBlogImageReplacement(
        originalBlog.featured_image_url,
        body.featured_image_url,
        'blog-update-validation'
      );

      // Safety check: Don't delete if URLs point to the same Cloudinary image
      // (this can happen with different transformations of the same image)
      if (!areCloudinaryUrlsSameImage(originalBlog.featured_image_url, body.featured_image_url)) {
        oldImageToDelete = originalBlog.featured_image_url;
        console.log(`✅ Safe to delete old image: ${originalBlog.featured_image_url}`);
      } else {
        console.log(`⚠️ Skipping deletion - URLs point to same Cloudinary image`);
      }
    }

    // Generate new slug if title changed
    if (body.title) {
      body.slug = body.title
        .toLowerCase()
        .replace(/[^a-z0-9]+/g, '-')
        .replace(/(^-|-$)/g, '');
    }

    // Update published_at if is_published changed to true
    if (body.is_published && !body.published_at) {
      body.published_at = new Date().toISOString();
    }

    // Update blog in database
    const { data: blog, error: dbError } = await supabase
      .from('blog_posts')
      .update({
        ...body,
        updated_at: new Date().toISOString(),
      })
      .eq('id', id)
      .select()
      .single();

    if (dbError) {
      console.error('Error updating blog:', dbError);

      // Log failed update
      await crudAuditLogger.logFailedOperation(
        'update',
        'blog_post',
        id,
        user.id,
        user.username || 'Unknown',
        dbError.message,
        request
      );

      return NextResponse.json(
        { error: 'Failed to update blog' },
        { status: 500 }
      );
    }

    // Track changes for audit log using utility function
    const changes = originalBlog ? CrudAuditLogger.calculateChanges(originalBlog, body) : {};

    // Log successful update
    await logBlogCrud.update(
      blog.id,
      blog.title,
      user.id,
      user.username || 'Unknown',
      changes,
      request
    );

    // Clean up old image from Cloudinary after successful DB update (non-blocking)
    if (oldImageToDelete) {
      const oldPublicId = extractCloudinaryPublicId(oldImageToDelete);
      if (oldPublicId) {
        // Fire and forget - don't block the response
        deleteFromCloudinary(oldPublicId, 'image', 'blog-replace').catch(cloudinaryError => {
          console.error('Error deleting old blog featured image from Cloudinary:', cloudinaryError);
        });
      }
    }

    return NextResponse.json({ data: blog });
  } catch (error) {
    const appError = handleApiError(error);
    return NextResponse.json(
      { error: appError.message },
      { status: appError.statusCode }
    );
  }
}

// DELETE /api/admin/blogs/[id] - Delete a blog
export async function DELETE(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    // Verify admin access
    const { user, hasAccess } = await verifyAdminAccess('blog', 'delete');

    if (!hasAccess || !user) {
      return NextResponse.json(
        { error: 'Unauthorized. Admin privileges required.' },
        { status: 403 }
      );
    }

    const { id } = await params;
    const supabase = createAdminClient();

    // Get blog data before deletion for audit log and Cloudinary cleanup
    const { data: blogToDelete } = await supabase
      .from('blog_posts')
      .select('title, featured_image_url')
      .eq('id', id)
      .single();

    // Delete featured image and folder from Cloudinary if it exists
    if (blogToDelete?.featured_image_url) {
      const publicId = extractCloudinaryPublicId(blogToDelete.featured_image_url);
      if (publicId) {
        try {
          console.log(`[blog-delete] Deleting featured image and folder: ${publicId}`);
          await cleanupSingleImage(publicId, 'blog-delete', true); // Enable folder cleanup
          console.log(`✅ Successfully deleted blog featured image and folder: ${publicId}`);
        } catch (cloudinaryError) {
          console.error('Error deleting blog featured image from Cloudinary:', cloudinaryError);
          // Continue with database deletion even if Cloudinary deletion fails
        }
      }
    }

    const { error } = await supabase
      .from('blog_posts')
      .delete()
      .eq('id', id);

    if (error) {
      console.error('Error deleting blog:', error);

      // Log failed deletion
      await crudAuditLogger.logFailedOperation(
        'delete',
        'blog_post',
        id,
        user.id,
        user.username || 'Unknown',
        error.message,
        request
      );

      return NextResponse.json(
        { error: 'Failed to delete blog' },
        { status: 500 }
      );
    }

    // Log successful deletion
    await logBlogCrud.delete(
      id,
      blogToDelete?.title || 'Unknown Blog',
      user.id,
      user.username || 'Unknown',
      request
    );

    return NextResponse.json({ message: 'Blog deleted successfully' });
  } catch (error) {
    const appError = handleApiError(error);
    return NextResponse.json(
      { error: appError.message },
      { status: appError.statusCode }
    );
  }
} 