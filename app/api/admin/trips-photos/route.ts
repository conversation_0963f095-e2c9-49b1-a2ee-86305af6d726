import { NextRequest, NextResponse } from 'next/server';
import { createServerSupabase } from '@/lib/supabase-server';
import { hashPassword } from '@/lib/security';
import { createSafeSearchCondition } from '@/lib/input-sanitization';

// GET /api/admin/trips-photos - Get all trip photo details with pagination and filters
export async function GET(request: NextRequest) {
  try {
    const supabase = createServerSupabase();
    const { searchParams } = new URL(request.url);

    // Parse query parameters with validation to avoid negative ranges
    const page = Math.max(1, parseInt(searchParams.get('page') || '1', 10) || 1);
    const limit = Math.max(1, Math.min(100, parseInt(searchParams.get('limit') || '10', 10) || 10));
    const search = searchParams.get('search');

    // Build query
    let query = supabase
      .from('trip_photos_details')
      .select('*', { count: 'exact' });

    // Apply filters with safe search
    if (search) {
      const searchConditions = createSafeSearchCondition(search, ['trip_name', 'trip_description']);
      if (searchConditions) {
        query = query.or(searchConditions);
      }
    }

    // Apply pagination
    const from = (page - 1) * limit;
    const to = from + limit - 1;

    query = query
      .order('created_at', { ascending: false })
      .range(from, to);

    const { data: tripPhotos, error, count } = await query;

    if (error) {
      console.error('Error fetching trip photos details:', error);
      return NextResponse.json(
        { error: 'Failed to fetch trip photos details' },
        { status: 500 }
      );
    }

    const totalPages = Math.ceil((count || 0) / limit);

    return NextResponse.json({
      data: tripPhotos || [],
      pagination: {
        page,
        limit,
        total: count || 0,
        totalPages,
      },
    });
  } catch (error) {
    console.error('Error in GET /api/admin/trips-photos:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}

// POST /api/admin/trips-photos - Create a new trip photos detail
export async function POST(request: NextRequest) {
  try {
    const json = await request.json();

    const {
      trip_name,
      trip_description,
      featured_image_url,
      access_password,
      storage_type,
      oauth_user_email,
      google_drive_link
    } = json;

    // Surface unsupported fields early
    if (google_drive_link !== undefined) {
      return NextResponse.json(
        { error: 'Google Drive links are no longer supported. Please use Google Photos OAuth instead.' },
        { status: 400 }
      );
    }

    if (!trip_name) {
      return NextResponse.json(
        { error: 'Trip name is required' },
        { status: 400 }
      );
    }

    const supabase = createServerSupabase();

    // Validate storage_type early to surface unsupported fields
    if (storage_type && storage_type !== 'google_photos_oauth') {
      return NextResponse.json(
        { error: 'Only Google Photos OAuth storage type is supported' },
        { status: 400 }
      );
    }

    const insertData: any = {
      trip_name,
      trip_description,
      featured_image_url,
      storage_type: 'google_photos_oauth' // Only Google Photos OAuth supported
    };

    // Handle password - hash if provided
    if (access_password?.trim()) {
      const trimmedPassword = access_password.trim();
      if (trimmedPassword.length < 4) {
        return NextResponse.json(
          { error: 'Password must be at least 4 characters long' },
          { status: 400 }
        );
      }
      insertData.access_password_hash = await hashPassword(trimmedPassword);
    }

    // Add OAuth user email if provided (for Google Photos OAuth)
    if (oauth_user_email) {
      insertData.oauth_user_email = oauth_user_email;
    }

    const { data, error } = await supabase
      .from('trip_photos_details')
      .insert([insertData])
      .select()
      .single();

    if (error) {
      throw error;
    }

    return NextResponse.json({ data });
  } catch (error: any) {
    return NextResponse.json(
      { error: error.message || 'An error occurred creating trip photo details' },
      { status: 500 }
    );
  }
}