import { NextRequest, NextResponse } from 'next/server';
import { verifyAdminAccess, createAdminClient } from '@/lib/auth-server';
import { hasPermission } from '@/lib/auth';
import { measureApiCall } from '@/lib/performance-monitor';
import { SupabaseClient } from '@supabase/supabase-js';

interface DashboardStats {
  totalTrips: number;
  activeTrips: number;
  draftTrips: number;
  totalBlogs: number;
  publishedBlogs: number;
  draftBlogs: number;
  totalInquiries: number;
  newInquiries: number;
  respondedInquiries: number;
  totalPhotos: number;
  recentTrips: Array<{
    id: string;
    title: string;
    slug: string;
    destination: string;
    created_at: string | null;
    is_active: boolean | null;
  }>;
  recentBlogs: Array<{
    id: string;
    title: string;
    slug: string;
    created_at: string | null;
    is_published: boolean | null;
  }>;
  recentInquiries: Array<{
    id: string;
    name: string;
    email: string;
    subject: string | null;
    status: string | null;
    created_at: string | null;
  }>;
}

// Reusable function to fetch dashboard stats with RPC fallback
async function fetchDashboardStats<T extends Record<string, any>>(
  supabase: SupabaseClient,
  config: {
    rpcFunction: string;
    table: string;
    recentItemsSelect: string;
    fallbackQueries: Array<{
      filter?: { column: string; value: any };
      statKey: keyof T;
    }>;
    statsMapping: Record<string, keyof T>;
    recentItemsKey: keyof T;
  }
): Promise<Partial<T>> {
  const stats: Partial<T> = {};

  try {
    // Use RPC function for aggregated stats and fetch recent items
    const [aggregateResult, recentItemsResult] = await Promise.all([
      supabase.rpc(config.rpcFunction),
      supabase
        .from(config.table)
        .select(config.recentItemsSelect)
        .order('created_at', { ascending: false })
        .limit(5)
    ]);

    // Map RPC results to stats
    if (aggregateResult.data && Array.isArray(aggregateResult.data) && aggregateResult.data.length > 0) {
      const rpcStats = aggregateResult.data[0] as Record<string, any>;
      Object.entries(config.statsMapping).forEach(([rpcKey, statsKey]) => {
        (stats as Record<string, any>)[statsKey as string] = rpcStats[rpcKey] || 0;
      });
    }

    (stats as Record<string, any>)[config.recentItemsKey as string] = recentItemsResult.data || [];
  } catch (error) {
    console.error(`Error fetching ${config.table} data:`, error);

    // Fallback to individual queries if RPC function doesn't exist
    try {
      const queries = config.fallbackQueries.map(query => {
        let supabaseQuery = supabase.from(config.table).select('id', { count: 'exact', head: true });
        if (query.filter) {
          supabaseQuery = supabaseQuery.eq(query.filter.column, query.filter.value);
        }
        return supabaseQuery;
      });

      const results = await Promise.all(queries);
      config.fallbackQueries.forEach((query, index) => {
        (stats as Record<string, any>)[query.statKey as string] = results[index].count || 0;
      });
    } catch (fallbackError) {
      console.error(`Error in ${config.table} fallback query:`, fallbackError);
    }
  }

  return stats;
}

export async function GET(_request: NextRequest) {
  return measureApiCall('admin_dashboard_get', async () => {
    try {
      // Verify admin authentication - any admin can access dashboard
      const { user, hasAccess } = await verifyAdminAccess();

    if (!hasAccess || !user) {
      return NextResponse.json(
        { error: 'Unauthorized. Admin privileges required.' },
        { status: 403 }
      );
    }

    const supabase = createAdminClient();

    // Check user permissions to determine what data to fetch
    const userRoles = user.roles;

    const canReadTrips = hasPermission(userRoles, 'trips', 'read');
    const canReadBlogs = hasPermission(userRoles, 'blog', 'read');
    const canReadInquiries = hasPermission(userRoles, 'inquiries', 'read');
    const canReadPhotos = hasPermission(userRoles, 'trip_photos', 'read');

    // Initialize stats object
    const stats: DashboardStats = {
      totalTrips: 0,
      activeTrips: 0,
      draftTrips: 0,
      totalBlogs: 0,
      publishedBlogs: 0,
      draftBlogs: 0,
      totalInquiries: 0,
      newInquiries: 0,
      respondedInquiries: 0,
      totalPhotos: 0,
      recentTrips: [],
      recentBlogs: [],
      recentInquiries: []
    };

    // Fetch trips data conditionally
    if (canReadTrips) {
      const tripStats = await fetchDashboardStats<DashboardStats>(supabase, {
        rpcFunction: 'get_trips_dashboard_stats',
        table: 'trips',
        recentItemsSelect: 'id, title, slug, destination, created_at, is_active',
        fallbackQueries: [
          { statKey: 'totalTrips' },
          { filter: { column: 'is_active', value: true }, statKey: 'activeTrips' },
          { filter: { column: 'is_active', value: false }, statKey: 'draftTrips' }
        ],
        statsMapping: {
          total_trips: 'totalTrips',
          active_trips: 'activeTrips',
          draft_trips: 'draftTrips'
        },
        recentItemsKey: 'recentTrips'
      });

      // Merge trip stats into main stats object
      Object.assign(stats, tripStats);
    }

    // Fetch blogs data conditionally
    if (canReadBlogs) {
      const blogStats = await fetchDashboardStats<DashboardStats>(supabase, {
        rpcFunction: 'get_blogs_dashboard_stats',
        table: 'blog_posts',
        recentItemsSelect: 'id, title, slug, created_at, is_published',
        fallbackQueries: [
          { statKey: 'totalBlogs' },
          { filter: { column: 'is_published', value: true }, statKey: 'publishedBlogs' },
          { filter: { column: 'is_published', value: false }, statKey: 'draftBlogs' }
        ],
        statsMapping: {
          total_blogs: 'totalBlogs',
          published_blogs: 'publishedBlogs',
          draft_blogs: 'draftBlogs'
        },
        recentItemsKey: 'recentBlogs'
      });

      // Merge blog stats into main stats object
      Object.assign(stats, blogStats);
    }

    // Fetch inquiries data conditionally
    if (canReadInquiries) {
      const inquiryStats = await fetchDashboardStats<DashboardStats>(supabase, {
        rpcFunction: 'get_inquiries_dashboard_stats',
        table: 'inquiries',
        recentItemsSelect: 'id, name, email, subject, status, created_at',
        fallbackQueries: [
          { statKey: 'totalInquiries' },
          { filter: { column: 'status', value: 'new' }, statKey: 'newInquiries' },
          { filter: { column: 'status', value: 'resolved' }, statKey: 'respondedInquiries' }
        ],
        statsMapping: {
          total_inquiries: 'totalInquiries',
          new_inquiries: 'newInquiries',
          responded_inquiries: 'respondedInquiries'
        },
        recentItemsKey: 'recentInquiries'
      });

      // Merge inquiry stats into main stats object
      Object.assign(stats, inquiryStats);
    }

    // Fetch photos data conditionally
    if (canReadPhotos) {
      try {
        const photosResult = await supabase
          .from('trip_photos_details')
          .select('id', { count: 'exact', head: true });

        stats.totalPhotos = photosResult.count || 0;
      } catch (error) {
        console.error('Error fetching photos data:', error);
      }
    }

    // Dashboard access is a read operation - no audit logging needed

    return NextResponse.json({ stats }, {
      headers: {
        'Cache-Control': 'private, max-age=120, stale-while-revalidate=60', // Cache for 2 minutes, allow stale for 1 minute
        'Vary': 'Authorization', // Vary by user since data is permission-based
      },
    });

  } catch (error) {
    console.error('Dashboard API error:', error);
    return NextResponse.json(
      { error: 'Failed to fetch dashboard data' },
      { status: 500 }
    );
  }
  }, '/api/admin/dashboard');
}
