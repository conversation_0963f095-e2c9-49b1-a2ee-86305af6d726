import { NextRequest, NextResponse } from 'next/server';
import { auditLogger } from '@/lib/audit-logger';
import { verifyAdminAccess } from '@/lib/auth-server';
import { AppError, handleApiError, getClientIP, ErrorCode } from '@/lib/error-handler';
import { logAdminAccess, getRequestContext } from '@/lib/audit-logger';

// Force dynamic rendering
export const dynamic = 'force-dynamic';

// GET /api/admin/audit-logs/export - Export audit logs as CSV
export async function GET(request: NextRequest): Promise<NextResponse> {
  try {
    // Check admin authentication and permissions - only super admins can access audit logs
    const { user: adminUser, hasAccess } = await verifyAdminAccess('audit_logs', 'export');

    if (!hasAccess || !adminUser) {
      return NextResponse.json(
        { error: 'Unauthorized. Super admin privileges required to export audit logs.' },
        { status: 403 }
      );
    }

    // Get the user's email from Supabase auth
    const { createServerSupabaseClient } = await import('@/lib/auth-server');
    const supabase = await createServerSupabaseClient();
    const { data: { user: authUser } } = await supabase.auth.getUser();
    const userEmail = authUser?.email || adminUser.username || 'Unknown';

    const { searchParams } = new URL(request.url);
    const requestContext = getRequestContext(request);
    
    // Parse query parameters
    const params = {
      user_id: searchParams.get('user_id') || undefined,
      event_type: searchParams.get('event_type') as any || undefined,
      severity: searchParams.get('severity') as any || undefined,
      resource_type: searchParams.get('resource_type') || undefined,
      success: searchParams.get('success') ? searchParams.get('success') === 'true' : undefined,
      start_date: searchParams.get('start_date') || undefined,
      end_date: searchParams.get('end_date') || undefined,
      limit: 10000, // Large limit for export
      offset: 0,
    };

    // Query audit logs
    const logs = await auditLogger.query(params);

    // Log the export action
    await logAdminAccess(
      adminUser.id,
      userEmail,
      'audit-logs',
      'export audit logs',
      {
        ...requestContext,
        metadata: {
          exported_count: logs.length,
          filters: params,
        },
      }
    );

    // Convert logs to CSV format
    const csvHeaders = [
      'Timestamp',
      'Event Type',
      'Severity',
      'User Email',
      'IP Address',
      'Action',
      'Description',
      'Resource Type',
      'Resource ID',
      'Success',
      'Error Message',
      'User Agent',
      'Session ID',
      'Request ID',
    ];

    const csvRows = logs.map(log => [
      log.timestamp,
      log.event_type,
      log.severity,
      log.user_email || '',
      log.ip_address || '',
      log.action,
      log.description,
      log.resource_type || '',
      log.resource_id || '',
      log.success ? 'true' : 'false',
      log.error_message || '',
      log.user_agent || '',
      log.session_id || '',
      log.request_id || '',
    ]);

    // Escape CSV values - handle non-string types safely
    const escapeCsvValue = (value: any): string => {
      // Convert to string first to handle booleans, numbers, etc.
      const stringValue = value?.toString() || '';
      if (stringValue.includes(',') || stringValue.includes('"') || stringValue.includes('\n')) {
        return `"${stringValue.replace(/"/g, '""')}"`;
      }
      return stringValue;
    };

    // Generate CSV content
    const csvContent = [
      csvHeaders.join(','),
      ...csvRows.map(row => row.map(escapeCsvValue).join(','))
    ].join('\n');

    // Create filename with timestamp
    const timestamp = new Date().toISOString().split('T')[0];
    const filename = `audit-logs-${timestamp}.csv`;

    // Return CSV file
    return new NextResponse(csvContent, {
      status: 200,
      headers: {
        'Content-Type': 'text/csv',
        'Content-Disposition': `attachment; filename="${filename}"`,
        'Cache-Control': 'no-cache, no-store, must-revalidate',
      },
    });

  } catch (error) {
    const appError = handleApiError(error);
    return NextResponse.json(
      { error: appError.message },
      { status: appError.statusCode }
    );
  }
}
