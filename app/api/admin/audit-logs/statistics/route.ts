import { NextRequest, NextResponse } from 'next/server';
import { auditLogger } from '@/lib/audit-logger';
import { verifyAdminAccess } from '@/lib/auth-server';
import { AppError, handleApiError, ErrorCode } from '@/lib/error-handler';

// Force dynamic rendering
export const dynamic = 'force-dynamic';

// GET /api/admin/audit-logs/statistics - Get audit log statistics
export async function GET(request: NextRequest): Promise<NextResponse> {
  try {
    // Check admin authentication and permissions - only super admins can access audit logs
    const { user, hasAccess } = await verifyAdminAccess('audit_logs', 'read');

    if (!hasAccess || !user) {
      return NextResponse.json(
        { error: 'Unauthorized. Super admin privileges required to view audit logs.' },
        { status: 403 }
      );
    }

    const { searchParams } = new URL(request.url);
    const days = parseInt(searchParams.get('days') || '30', 10);

    // Validate days parameter
    if (days < 1 || days > 365) {
      throw new AppError('Days parameter must be between 1 and 365', ErrorCode.VALIDATION_ERROR, 400);
    }

    // Get audit statistics
    const statistics = await auditLogger.getStatistics(days);

    return NextResponse.json({
      success: true,
      ...statistics,
      period: {
        days,
        start_date: new Date(Date.now() - days * 24 * 60 * 60 * 1000).toISOString(),
        end_date: new Date().toISOString(),
      },
    });

  } catch (error) {
    console.error('Error fetching audit log statistics:', error);
    return NextResponse.json(
      { error: 'Failed to fetch audit log statistics' },
      { status: 500 }
    );
  }
}
