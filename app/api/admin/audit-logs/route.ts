import { NextRequest, NextResponse } from 'next/server';
import { auditLogger } from '@/lib/audit-logger';
import { verifyAdminAccess } from '@/lib/auth-server';
import { AppError, handleApiError, ErrorCode } from '@/lib/error-handler';

// Force dynamic rendering
export const dynamic = 'force-dynamic';

// GET /api/admin/audit-logs - Get audit logs with filtering
export async function GET(request: NextRequest): Promise<NextResponse> {
  try {
    // Check admin authentication and permissions - only super admins can access audit logs
    const { user, hasAccess } = await verifyAdminAccess('audit_logs', 'read');

    if (!hasAccess || !user) {
      return NextResponse.json(
        { error: 'Unauthorized. Super admin privileges required to view audit logs.' },
        { status: 403 }
      );
    }

    const { searchParams } = new URL(request.url);
    
    // Parse query parameters with proper validation
    const limitParam = searchParams.get('limit') || '50';
    const offsetParam = searchParams.get('offset') || '0';

    // Graceful handling of non-numeric parameters
    const parsedLimit = parseInt(limitParam, 10);
    const parsedOffset = parseInt(offsetParam, 10);

    const limit = Math.max(1, Math.min(1000, isNaN(parsedLimit) ? 50 : parsedLimit)); // Ensure valid range
    const offset = Math.max(0, isNaN(parsedOffset) ? 0 : parsedOffset); // Ensure non-negative

    const params = {
      user_id: searchParams.get('user_id') || undefined,
      event_type: searchParams.get('event_type') as any || undefined,
      severity: searchParams.get('severity') as any || undefined,
      resource_type: searchParams.get('resource_type') || undefined,
      success: searchParams.get('success') ? searchParams.get('success') === 'true' : undefined,
      start_date: searchParams.get('start_date') || undefined,
      end_date: searchParams.get('end_date') || undefined,
      limit,
      offset,
    };

    // Query audit logs
    const logs = await auditLogger.query(params);

    // Get total count more accurately
    const totalCount = logs.length < limit ? offset + logs.length : offset + logs.length + 1; // More accurate estimation

    return NextResponse.json({
      success: true,
      logs,
      total: totalCount,
      pagination: {
        page: Math.floor(params.offset / params.limit) + 1,
        limit: params.limit,
        offset: params.offset,
        total: totalCount,
      },
    });

  } catch (error) {
    const appError = handleApiError(error);
    return NextResponse.json(
      { error: appError.message },
      { status: appError.statusCode }
    );
  }
}
