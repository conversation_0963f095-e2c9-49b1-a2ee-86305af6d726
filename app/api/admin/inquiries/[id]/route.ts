import { NextRequest, NextResponse } from 'next/server';
import { verifyAdminAccess, createAdminClient } from '@/lib/auth-server';
import { crud<PERSON><PERSON>tLogger, CrudAuditLogger } from '@/lib/crud-audit-logger';
import { handleApiError } from '@/lib/error-handler';

// Force dynamic rendering to prevent caching
export const dynamic = 'force-dynamic';

// GET /api/admin/inquiries/[id] - Get a single inquiry
export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    // Verify admin access
    const { user, hasAccess } = await verifyAdminAccess('inquiries', 'read');

    if (!hasAccess || !user) {
      return NextResponse.json(
        { error: 'Unauthorized. Admin privileges required.' },
        { status: 403 }
      );
    }

    const { id } = await params;
    const supabase = createAdminClient();
    const { data: inquiry, error } = await supabase
      .from('inquiries')
      .select('*, trips(title, slug)')
      .eq('id', id)
      .single();

    if (error) {
      console.error('Error fetching inquiry:', error);
      return NextResponse.json(
        { error: 'Failed to fetch inquiry' },
        { status: 500 }
      );
    }

    if (!inquiry) {
      return NextResponse.json(
        { error: 'Inquiry not found' },
        { status: 404 }
      );
    }

    return NextResponse.json({ data: inquiry });
  } catch (error) {
    console.error('Error in GET /api/admin/inquiries/[id]:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}

// PUT /api/admin/inquiries/[id] - Update an inquiry
export async function PUT(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    // Verify admin access
    const { user, hasAccess } = await verifyAdminAccess('inquiries', 'update');

    if (!hasAccess || !user) {
      return NextResponse.json(
        { error: 'Unauthorized. Admin privileges required.' },
        { status: 403 }
      );
    }

    const { id } = await params;
    const supabase = createAdminClient();
    const body = await request.json();

    // Get original inquiry data for change tracking
    const { data: originalInquiry } = await supabase
      .from('inquiries')
      .select('*')
      .eq('id', id)
      .single();

    const updates: any = {};

    // Only update specified fields
    if (body.status) updates.status = body.status;
    if (body.admin_notes !== undefined) updates.admin_notes = body.admin_notes;
    
    // If status is being updated to anything other than 'new', set responded_at if not already set
    if (body.status && body.status !== 'new') {
      const { data: currentInquiry } = await supabase
        .from('inquiries')
        .select('responded_at')
        .eq('id', id)
        .single();

      if (!currentInquiry?.responded_at) {
        updates.responded_at = new Date().toISOString();
      }
    }

    // Update inquiry in database
    const { data: inquiry, error: dbError } = await supabase
      .from('inquiries')
      .update({
        ...updates,
        updated_at: new Date().toISOString(),
      })
      .eq('id', id)
      .select()
      .single();

    if (dbError) {
      console.error('Error updating inquiry:', dbError);

      // Log failed update (wrap in try/catch)
      try {
        await crudAuditLogger.logFailedOperation(
          'update',
          'inquiry',
          id,
          user.id,
          user.username || 'Unknown',
          dbError.message,
          request
        );
      } catch (auditError) {
        console.error('Failed to log audit entry:', auditError);
      }

      return NextResponse.json(
        { error: 'Failed to update inquiry' },
        { status: 500 }
      );
    }

    // Track changes for audit log using utility function
    const changes = originalInquiry ? CrudAuditLogger.calculateChanges(originalInquiry, updates) : {};

    // Log successful update (wrap in try/catch to avoid breaking the happy-path)
    try {
      await crudAuditLogger.logCrudOperation({
        operation: 'update',
        resourceType: 'inquiry',
        resourceId: inquiry.id,
        resourceTitle: `Inquiry from ${inquiry.name}`,
        userId: user.id,
        userEmail: user.username || 'Unknown',
        changes,
        success: true,
        metadata: {
          updated_at: new Date().toISOString(),
          fields_changed: Object.keys(changes),
        },
      }, request);
    } catch (auditError) {
      console.error('Failed to log audit entry:', auditError);
      // Continue with the response - don't fail the operation due to audit logging issues
    }


    return NextResponse.json({ data: inquiry });
  } catch (error) {
    const appError = handleApiError(error);
    return NextResponse.json(
      { error: appError.message },
      { status: appError.statusCode }
    );
  }
} 