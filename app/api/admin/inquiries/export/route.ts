import { NextRequest, NextResponse } from 'next/server';
import { verifyAdminAccess, createAdminClient } from '@/lib/auth-server';
import { handleApiError } from '@/lib/error-handler';

// Force dynamic rendering to prevent caching
export const dynamic = 'force-dynamic';

// GET /api/admin/inquiries/export - Export inquiries as CSV with date filters
export async function GET(request: NextRequest) {
  try {
    // Verify admin access
    const { user, hasAccess } = await verifyAdminAccess('inquiries', 'read');

    if (!hasAccess || !user) {
      return NextResponse.json(
        { error: 'Unauthorized. Admin privileges required.' },
        { status: 403 }
      );
    }

    const supabase = createAdminClient();
    const { searchParams } = new URL(request.url);

    // Parse date filter parameter
    const dateFilter = searchParams.get('dateFilter') || 'all'; // 'today', 'month', 'all'
    
    // Build query
    let query = supabase
      .from('inquiries')
      .select('*, trips(title)')
      .order('created_at', { ascending: false });

    // Apply date filters
    const now = new Date();
    if (dateFilter === 'today') {
      const today = new Date(now.getFullYear(), now.getMonth(), now.getDate());
      query = query.gte('created_at', today.toISOString());
    } else if (dateFilter === 'month') {
      const firstDayOfMonth = new Date(now.getFullYear(), now.getMonth(), 1);
      query = query.gte('created_at', firstDayOfMonth.toISOString());
    }

    const { data: inquiries, error } = await query;

    if (error) {
      console.error('Error fetching inquiries for export:', error);
      return NextResponse.json(
        { error: 'Failed to fetch inquiries' },
        { status: 500 }
      );
    }

    // Generate CSV content
    const csvHeaders = [
      'ID',
      'Name',
      'Email',
      'Phone',
      'Subject',
      'Message',
      'Inquiry Type',
      'Trip',
      'Status',
      'Created At',
      'Admin Notes'
    ];

    // Escape CSV values - handle non-string types safely
    const escapeCsvValue = (value: any): string => {
      // Convert to string first to handle booleans, numbers, etc.
      const stringValue = value?.toString() || '';
      if (stringValue.includes(',') || stringValue.includes('"') || stringValue.includes('\n')) {
        return `"${stringValue.replace(/"/g, '""')}"`;
      }
      return stringValue;
    };

    const csvRows = inquiries?.map(inquiry => [
      escapeCsvValue(inquiry.id),
      escapeCsvValue(inquiry.name),
      escapeCsvValue(inquiry.email),
      escapeCsvValue(inquiry.phone),
      escapeCsvValue(inquiry.subject),
      escapeCsvValue(inquiry.message),
      escapeCsvValue(inquiry.inquiry_type),
      escapeCsvValue(inquiry.trips?.title),
      escapeCsvValue(inquiry.status),
      escapeCsvValue(new Date(inquiry.created_at || '').toLocaleString()),
      escapeCsvValue(inquiry.admin_notes)
    ]) || [];

    const csvContent = [
      csvHeaders.join(','),
      ...csvRows.map(row => row.join(','))
    ].join('\n');

    // Generate filename with date filter
    const dateStr = new Date().toISOString().split('T')[0];
    const filename = `inquiries_${dateFilter}_${dateStr}.csv`;

    // Return CSV file
    return new NextResponse(csvContent, {
      status: 200,
      headers: {
        'Content-Type': 'text/csv',
        'Content-Disposition': `attachment; filename="${filename}"`,
        'Cache-Control': 'no-store, max-age=0'
      }
    });

  } catch (error) {
    const appError = handleApiError(error);
    return NextResponse.json(
      { error: appError.message },
      { status: appError.statusCode }
    );
  }
}
