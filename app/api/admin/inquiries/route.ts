import { NextRequest, NextResponse } from 'next/server';
import { verifyAdminAccess, createAdminClient } from '@/lib/auth-server';
import { InquiryStatus } from '@/types/inquiry';
import { createSafeSearchCondition } from '@/lib/input-sanitization';
import { crudAuditLogger } from '@/lib/crud-audit-logger';
import { handleApiError } from '@/lib/error-handler';

// Force dynamic rendering to prevent caching
export const dynamic = 'force-dynamic';

// GET /api/admin/inquiries - Get all inquiries with pagination and filters
export async function GET(request: NextRequest) {
  try {
    // Verify admin access
    const { user, hasAccess } = await verifyAdminAccess('inquiries', 'read');

    if (!hasAccess || !user) {
      return NextResponse.json(
        { error: 'Unauthorized. Admin privileges required.' },
        { status: 403 }
      );
    }

    const supabase = createAdminClient();
    const { searchParams } = new URL(request.url);

    // Parse query parameters with validation
    const pageParam = searchParams.get('page') || '1';
    const limitParam = searchParams.get('limit') || '10';

    // Graceful handling of non-numeric parameters
    const parsedPage = parseInt(pageParam, 10);
    const parsedLimit = parseInt(limitParam, 10);

    const page = Math.max(1, isNaN(parsedPage) ? 1 : parsedPage);
    const limit = Math.max(1, Math.min(100, isNaN(parsedLimit) ? 10 : parsedLimit));
    const search = searchParams.get('search');
    const status = searchParams.get('status');
    const tripId = searchParams.get('tripId');
    const startDate = searchParams.get('startDate');
    const endDate = searchParams.get('endDate');

    // Build query
    let query = supabase
      .from('inquiries')
      .select('*, trips(title)', { count: 'exact' });

    // Apply filters with safe search
    if (search) {
      const searchConditions = createSafeSearchCondition(search, ['name', 'email', 'message']);
      if (searchConditions) {
        query = query.or(searchConditions);
      }
    }
    if (status && status !== 'all') {
      // Validate that status is one of the allowed values
      const validStatus = ['new', 'in_progress', 'resolved', 'closed'].includes(status) 
        ? status as InquiryStatus 
        : 'new';
      query = query.eq('status', validStatus);
    }
    if (tripId && tripId !== 'all') {
      query = query.eq('trip_id', tripId);
    }
    
    // Apply date filters if provided
    if (startDate) {
      const startDateObj = new Date(startDate);
      startDateObj.setUTCHours(0, 0, 0, 0); // Start of the day
      query = query.gte('created_at', startDateObj.toISOString());
    }
    
    if (endDate) {
      const endDateObj = new Date(endDate);
      endDateObj.setUTCHours(23, 59, 59, 999); // End of the day
      query = query.lte('created_at', endDateObj.toISOString());
    }

    // Apply pagination
    const from = (page - 1) * limit;
    const to = from + limit - 1;

    query = query
      .order('created_at', { ascending: false })
      .range(from, to);

    const { data: inquiries, error, count } = await query;

    if (error) {
      console.error('Error fetching inquiries:', error);
      return NextResponse.json(
        { error: 'Failed to fetch inquiries' },
        { status: 500 }
      );
    }

    const totalPages = Math.ceil((count || 0) / limit);

    return NextResponse.json({
      data: inquiries || [],
      pagination: {
        page,
        limit,
        total: count || 0,
        totalPages,
      },
    });
  } catch (error) {
    const appError = handleApiError(error);
    return NextResponse.json(
      { error: appError.message },
      { status: appError.statusCode }
    );
  }
}

// PUT /api/admin/inquiries - Update inquiry status or add admin notes
export async function PUT(request: NextRequest) {
  try {
    // Verify admin access
    const { user, hasAccess } = await verifyAdminAccess('inquiries', 'update');

    if (!hasAccess || !user) {
      return NextResponse.json(
        { error: 'Unauthorized. Admin privileges required.' },
        { status: 403 }
      );
    }

    const supabase = createAdminClient();
    const body = await request.json();

    // Basic validation
    if (!body.id) {
      return NextResponse.json(
        { error: 'Missing required field: id' },
        { status: 400 }
      );
    }

    // Get original inquiry data for change tracking (only once)
    const { data: originalInquiry } = await supabase
      .from('inquiries')
      .select('*')
      .eq('id', body.id)
      .single();

    if (!originalInquiry) {
      return NextResponse.json(
        { error: 'Inquiry not found' },
        { status: 404 }
      );
    }

    const updates: any = {};
    
    // Only update specified fields
    if (body.status) {
      // Validate status is one of the allowed values
      const validStatus = ['new', 'in_progress', 'resolved', 'closed'].includes(body.status)
        ? body.status as InquiryStatus
        : 'new';
      updates.status = validStatus;
    }
    
    if (body.admin_notes !== undefined) updates.admin_notes = body.admin_notes;
    
    // If status is being updated to anything other than 'new', set responded_at if not already set
    if (body.status && body.status !== 'new' && !body.responded_at) {
      updates.responded_at = new Date().toISOString();
    }

    // Update inquiry in database
    const { data: inquiry, error: dbError } = await supabase
      .from('inquiries')
      .update({
        ...updates,
        updated_at: new Date().toISOString(),
      })
      .eq('id', body.id)
      .select()
      .single();

    if (dbError) {
      console.error('Error updating inquiry:', dbError);

      // Log failed update
      await crudAuditLogger.logFailedOperation(
        'update',
        'inquiry',
        body.id,
        user.id,
        user.email || user.username || 'Unknown',
        dbError.message,
        request
      );

      return NextResponse.json(
        { error: 'Failed to update inquiry' },
        { status: 500 }
      );
    }

    // Track changes for audit log
    const changes: Record<string, { old: any; new: any }> = {};
    if (originalInquiry) {
      Object.keys(updates).forEach(key => {
        if ((originalInquiry as any)[key] !== updates[key]) {
          changes[key] = { old: (originalInquiry as any)[key], new: updates[key] };
        }
      });
    }

    // Log successful update (wrap in try/catch to avoid breaking the happy-path)
    try {
      await crudAuditLogger.logCrudOperation({
        operation: 'update',
        resourceType: 'inquiry',
        resourceId: inquiry.id,
        resourceTitle: `Inquiry from ${inquiry.name}`,
        userId: user.id,
        userEmail: user.email || user.username || 'Unknown',
        changes,
        success: true,
        metadata: {
          updated_at: new Date().toISOString(),
          fields_changed: Object.keys(changes),
        },
      }, request);
    } catch (auditError) {
      console.error('Failed to log audit entry:', auditError);
      // Continue with the response - don't fail the operation due to audit logging issues
    }


    return NextResponse.json({ data: inquiry });
  } catch (error) {
    const appError = handleApiError(error);
    return NextResponse.json(
      { error: appError.message },
      { status: appError.statusCode }
    );
  }
} 