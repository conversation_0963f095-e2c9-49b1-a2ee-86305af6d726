import { NextRequest, NextResponse } from 'next/server';
import { createServerSupabase } from '@/lib/supabase-server';
import { addWatermarkToImage } from '@/lib/watermark';
import { uploadToGooglePhotos } from '@/lib/google-photos';
import { handleApiError } from '@/lib/error-handler';

// POST /api/admin/photo-albums/upload - Upload photos to Google Photos albums
// NOTE: No rate limiting applied - admins need to upload large batches of images (200-250 at a time)
// This endpoint is used for bulk photo uploads to Google Photos albums with watermarking
export async function POST(request: NextRequest) {
  try {
    const formData = await request.formData();
    const image = formData.get('image') as File;
    const albumId = formData.get('albumId') as string;
    
    // Validation
    if (!image) {
      return NextResponse.json(
        { error: 'No image file provided' },
        { status: 400 }
      );
    }
    
    if (!albumId) {
      return NextResponse.json(
        { error: 'Album ID is required' },
        { status: 400 }
      );
    }
    
    // Fetch album details
    const supabase = createServerSupabase();
    const { data: album, error: albumError } = await supabase
      .from('trip_photos_details')
      .select('*')
      .eq('id', albumId)
      .single();
    
    if (albumError || !album) {
      return NextResponse.json(
        { error: 'Album not found' },
        { status: 404 }
      );
    }
    
    // Check if album has Google Photos setup
    const oauthToken = (album as any).oauth_refresh_token;
    const googlePhotosAlbumId = (album as any).google_photos_album_id;

    if (!oauthToken || !googlePhotosAlbumId) {
      return NextResponse.json(
        { error: 'Album not properly configured for Google Photos' },
        { status: 400 }
      );
    }

    // Convert File to Buffer
    const arrayBuffer = await image.arrayBuffer();
    const buffer = Buffer.from(arrayBuffer);

    // Add watermark
    const watermarkedBuffer = await addWatermarkToImage(buffer);

    // Upload to Google Photos
    const uploadResult = await uploadToGooglePhotos(
      watermarkedBuffer,
      image.name,
      googlePhotosAlbumId,
      oauthToken
    );

    return NextResponse.json({
      success: true,
      mediaItemId: uploadResult.mediaItemId,
      message: 'Photo uploaded successfully to Google Photos'
    });
    
  } catch (error) {
    const appError = handleApiError(error);
    return NextResponse.json(
      { error: appError.message },
      { status: appError.statusCode }
    );
  }
}

// Set runtime to nodejs for better performance with image processing
export const runtime = 'nodejs';

// Set max duration for serverless functions (Vercel Pro: 60s, Hobby: 10s)
export const maxDuration = 30;
