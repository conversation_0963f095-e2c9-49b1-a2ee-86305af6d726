import { NextRequest, NextResponse } from 'next/server';
import { verifyAdminAccess, createAdminClient } from '@/lib/auth-server';
import { hashPassword } from '@/lib/security';
import { crud<PERSON><PERSON><PERSON>Logger, CrudAuditLogger } from '@/lib/crud-audit-logger';
import { measureApiCall } from '@/lib/performance-monitor';

export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  return measureApiCall('admin_photo_album_get', async () => {
    console.log('[PHOTO_ALBUM_API] ==> Starting fetch single album request');

    try {
      // Verify admin access
      const { user, hasAccess } = await verifyAdminAccess('trip_photos', 'read');

      if (!hasAccess || !user) {
        return NextResponse.json(
          { error: 'Unauthorized. Admin privileges required.' },
          { status: 403 }
        );
      }

      const { id } = await params;
      console.log('[PHOTO_ALBUM_API] Album ID:', id);

      const supabase = createAdminClient();

      const { data, error } = await supabase
        .from('trip_photos_details')
        .select('*')
        .eq('id', id)
        .single();

      if (error) {
        console.error('[PHOTO_ALBUM_API] ❌ Database error:', error);
        return NextResponse.json(
          { error: 'Album not found' },
          { status: 404 }
        );
      }

      console.log('[PHOTO_ALBUM_API] ✅ Album fetched successfully');

      return NextResponse.json({ album: data });

    } catch (error) {
      console.error('[PHOTO_ALBUM_API] ❌ Unexpected error:', error);
      return NextResponse.json(
        { error: 'Internal server error' },
        { status: 500 }
      );
    }
  }, '/api/admin/photo-albums/[id]');
}

export async function PATCH(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  return measureApiCall('admin_photo_album_update', async () => {
    console.log('[PHOTO_ALBUM_API] ==> Starting update album request');

  try {
    // Verify admin access
    const { user, hasAccess } = await verifyAdminAccess('trip_photos', 'update');

    if (!hasAccess || !user) {
      return NextResponse.json(
        { error: 'Unauthorized. Admin privileges required.' },
        { status: 403 }
      );
    }

    const { id } = await params;
    const body = await request.json();
    console.log('[PHOTO_ALBUM_API] Album ID:', id);
    console.log('[PHOTO_ALBUM_API] Update data:', body);
    
    const { trip_name, trip_description, featured_image_url, access_password, manual_shareable_url } = body;

    // Validation
    if (trip_name !== undefined && !trip_name?.trim()) {
      return NextResponse.json(
        { error: 'Trip name cannot be empty' },
        { status: 400 }
      );
    }

    // Validate manual shareable URL if provided (supports both old and new formats)
    if (manual_shareable_url !== undefined && manual_shareable_url?.trim()) {
      const googlePhotosUrlPattern = /^https:\/\/(photos\.google\.com\/share\/[A-Za-z0-9_-]+|photos\.app\.goo\.gl\/[A-Za-z0-9]+)$/;
      if (!googlePhotosUrlPattern.test(manual_shareable_url.trim())) {
        return NextResponse.json(
          { error: 'Invalid Google Photos shareable link format. Please use photos.google.com/share/... or photos.app.goo.gl/... format' },
          { status: 400 }
        );
      }
    }

    const supabase = createAdminClient();

    // Get original album data for change tracking
    const { data: originalAlbum } = await supabase
      .from('trip_photos_details')
      .select('*')
      .eq('id', id)
      .single();

    // Build update object with only provided fields
    const updateData: {
      updated_at: string;
      trip_name?: string;
      trip_description?: string | null;
      featured_image_url?: string | null;
      manual_shareable_url?: string | null;
      access_password_hash?: string | null;
    } = {
      updated_at: new Date().toISOString()
    };

    if (trip_name !== undefined) updateData.trip_name = trip_name.trim();
    if (trip_description !== undefined) updateData.trip_description = trip_description?.trim() || null;
    if (featured_image_url !== undefined) updateData.featured_image_url = featured_image_url?.trim() || null;
    if (manual_shareable_url !== undefined) updateData.manual_shareable_url = manual_shareable_url?.trim() || null;

    // Handle password - hash if provided, clear if empty
    if (access_password !== undefined) {
      const trimmedPassword = access_password?.trim();
      if (trimmedPassword) {
        // Validate password length
        if (trimmedPassword.length < 4) {
          return NextResponse.json(
            { error: 'Password must be at least 4 characters long' },
            { status: 400 }
          );
        }
        // Hash the password and store in access_password_hash
        updateData.access_password_hash = await hashPassword(trimmedPassword);
      } else {
        // Clear password field if empty password provided
        updateData.access_password_hash = null;
      }
    }
    
    console.log('[PHOTO_ALBUM_API] Final update data:', updateData);
    
    const { data, error } = await supabase
      .from('trip_photos_details')
      .update(updateData)
      .eq('id', id)
      .select()
      .single();
    
    if (error) {
      console.error('[PHOTO_ALBUM_API] ❌ Database error:', error);
      return NextResponse.json(
        { error: 'Failed to update album' },
        { status: 500 }
      );
    }
    
    console.log('[PHOTO_ALBUM_API] ✅ Album updated successfully');

    // Calculate changes for audit log
    const changes = originalAlbum ? CrudAuditLogger.calculateChanges(originalAlbum, updateData) : {};

    // Log the update operation
    await crudAuditLogger.logUpdate(
      'trip_photo_album',
      id,
      data.trip_name,
      user.id,
      user.username || 'Unknown',
      changes,
      request
    );

    return NextResponse.json({
      album: data,
      message: 'Album updated successfully'
    });

  } catch (error) {
    console.error('[PHOTO_ALBUM_API] ❌ Unexpected error:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
  }, '/api/admin/photo-albums/[id]');
}

export async function DELETE(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  return measureApiCall('admin_photo_album_delete', async () => {
    console.log('[PHOTO_ALBUM_API] ==> Starting delete album request');

    try {
    // Verify admin access
    const { user, hasAccess } = await verifyAdminAccess('trip_photos', 'delete');

    if (!hasAccess || !user) {
      return NextResponse.json(
        { error: 'Unauthorized. Admin privileges required.' },
        { status: 403 }
      );
    }

    const { id } = await params;
    console.log('[PHOTO_ALBUM_API] Album ID:', id);

    const supabase = createAdminClient();

    // First get the album data for audit logging
    const { data: albumData } = await supabase
      .from('trip_photos_details')
      .select('trip_name')
      .eq('id', id)
      .single();

    const { error } = await supabase
      .from('trip_photos_details')
      .delete()
      .eq('id', id);

    if (error) {
      console.error('[PHOTO_ALBUM_API] ❌ Database error:', error);
      return NextResponse.json(
        { error: 'Failed to delete album' },
        { status: 500 }
      );
    }

    console.log('[PHOTO_ALBUM_API] ✅ Album deleted successfully from database');

    // Log the delete operation
    await crudAuditLogger.logDelete(
      'trip_photo_album',
      id,
      albumData?.trip_name || 'Unknown Album',
      user.id,
      user.username || 'Unknown',
      request
    );

    return NextResponse.json({
      message: 'Album deleted successfully from database',
      note: 'Due to Google Photos API limitations, the album must be manually deleted from Google Photos at photos.google.com'
    });

  } catch (error) {
    console.error('[PHOTO_ALBUM_API] ❌ Unexpected error:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
  }, '/api/admin/photo-albums/[id]');
}
