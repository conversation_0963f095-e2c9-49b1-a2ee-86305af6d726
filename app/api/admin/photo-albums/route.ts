import { NextRequest, NextResponse } from 'next/server';
import { verifyAdminAccess, createAdminClient } from '@/lib/auth-server';
import { hashPassword, sanitizeInput } from '@/lib/security';
import { createSafeSearchCondition } from '@/lib/input-sanitization';
import {
  handleApiError,
  ValidationError,
  formatErrorResponse,
  logError,
  validateRequired,
  getClientIP
} from '@/lib/error-handler';

export async function GET(request: NextRequest) {
  try {
    // Verify admin access
    const { user, hasAccess } = await verifyAdminAccess('trip_photos', 'read');

    if (!hasAccess || !user) {
      return NextResponse.json(
        { error: 'Unauthorized. Admin privileges required.' },
        { status: 403 }
      );
    }

    const { searchParams } = new URL(request.url);

    // Graceful handling of non-numeric parameters
    const pageParam = searchParams.get('page') || '1';
    const limitParam = searchParams.get('limit') || '10';
    const parsedPage = parseInt(pageParam, 10);
    const parsedLimit = parseInt(limitParam, 10);

    const page = Math.max(1, isNaN(parsedPage) ? 1 : parsedPage);
    const limit = Math.min(100, Math.max(1, isNaN(parsedLimit) ? 10 : parsedLimit));
    const search = searchParams.get('search') || ''; // Remove double-sanitization

    const offset = (page - 1) * limit;

    console.log('[PHOTO_ALBUMS_API] Query params:', { page, limit, search, offset });

    const supabase = createAdminClient();

    // Build query - exclude sensitive fields from general listing
    let query = supabase
      .from('trip_photos_details')
      .select('*', { count: 'exact' });

    // Add search filter if provided - createSafeSearchCondition handles sanitization
    if (search) {
      const searchConditions = createSafeSearchCondition(search, ['trip_name']);
      if (searchConditions) {
        query = query.or(searchConditions);
      }
    }

    // Add pagination
    query = query
      .order('created_at', { ascending: false })
      .range(offset, offset + limit - 1);

    const { data, error, count } = await query;

    if (error) {
      console.error('[PHOTO_ALBUMS_API] ❌ Database error:', error);
      throw new Error('Database query failed');
    }

    const totalPages = Math.ceil((count || 0) / limit);

    console.log('[PHOTO_ALBUMS_API] ✅ Successfully fetched albums:', {
      count: data?.length || 0,
      total: count,
      totalPages
    });

    // Filter out sensitive data before sending to client
    const safeAlbums = (data || []).map((album: any) => ({
      id: album.id,
      trip_name: album.trip_name,
      trip_description: album.trip_description,
      featured_image_url: album.featured_image_url,
      storage_type: album.storage_type,
      google_photos_album_id: album.google_photos_album_id,
      oauth_user_email: album.oauth_user_email,
      manual_shareable_url: album.manual_shareable_url,
      created_at: album.created_at,
      updated_at: album.updated_at,
      // Exclude sensitive fields: access_password, access_password_hash, oauth_refresh_token, oauth_refresh_token_encrypted
    }));

    return NextResponse.json({
      success: true,
      albums: safeAlbums,
      pagination: {
        page,
        limit,
        total: count || 0,
        totalPages
      }
    });

  } catch (error) {
    const appError = handleApiError(error);
    logError(appError, {
      endpoint: '/api/admin/photo-albums',
      method: 'GET',
      ip: getClientIP(request)
    });

    return NextResponse.json(
      formatErrorResponse(appError),
      { status: appError.statusCode }
    );
  }
}

export async function POST(request: NextRequest) {
  console.log('[PHOTO_ALBUMS_API] ==> Starting create request');

  try {
    // Verify admin access
    const { user, hasAccess } = await verifyAdminAccess('trip_photos', 'create');

    if (!hasAccess || !user) {
      return NextResponse.json(
        { error: 'Unauthorized. Admin privileges required.' },
        { status: 403 }
      );
    }

    const body = await request.json();
    console.log('[PHOTO_ALBUMS_API] Request body (sanitized):', {
      ...body,
      access_password: body.access_password ? '[REDACTED]' : undefined
    });

    const { trip_name, trip_description, featured_image_url, access_password } = body;

    // Validation
    validateRequired(trip_name, 'Trip name');

    const sanitizedTripName = sanitizeInput(trip_name.trim());
    const sanitizedDescription = trip_description ? sanitizeInput(trip_description.trim()) : null;
    const sanitizedImageUrl = featured_image_url ? sanitizeInput(featured_image_url.trim()) : null;

    if (sanitizedTripName.length < 2) {
      throw new ValidationError('Trip name must be at least 2 characters long');
    }

    const supabase = createAdminClient();

    // Prepare insert data
    const insertData: any = {
      trip_name: sanitizedTripName,
      trip_description: sanitizedDescription,
      featured_image_url: sanitizedImageUrl,
      storage_type: 'google_photos_oauth', // Always Google Photos OAuth for new albums
      security_version: 1, // Track security version for future migrations
    };

    // Handle password - hash if provided
    if (access_password?.trim()) {
      const trimmedPassword = access_password.trim();
      if (trimmedPassword.length < 4) {
        throw new ValidationError('Password must be at least 4 characters long');
      }

      insertData.access_password_hash = await hashPassword(trimmedPassword);
      // Don't store plain text password for new records
    }

    console.log('[PHOTO_ALBUMS_API] Insert data prepared (password hashed if provided)');

    const { data, error } = await supabase
      .from('trip_photos_details')
      .insert(insertData)
      .select()
      .single();

    if (error) {
      console.error('[PHOTO_ALBUMS_API] ❌ Database error:', error);
      throw new Error('Failed to create photo album in database');
    }

    if (!data) {
      throw new Error('No data returned from database insert');
    }

    console.log('[PHOTO_ALBUMS_API] ✅ Album created successfully:', data.id);

    // Return only safe fields to client
    const safeData = {
      id: data.id,
      trip_name: data.trip_name,
      trip_description: data.trip_description,
      featured_image_url: data.featured_image_url,
      storage_type: (data as any).storage_type || 'google_photos_oauth',
      created_at: data.created_at,
      updated_at: data.updated_at,
    };

    return NextResponse.json({
      success: true,
      album: safeData,
      message: 'Photo album created successfully'
    });

  } catch (error) {
    const appError = handleApiError(error);
    logError(appError, {
      endpoint: '/api/admin/photo-albums',
      method: 'POST',
      ip: getClientIP(request)
    });

    return NextResponse.json(
      formatErrorResponse(appError),
      { status: appError.statusCode }
    );
  }
}
