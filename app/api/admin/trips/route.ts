import { NextRequest, NextResponse } from 'next/server';
import { verifyAdminAccess, createAdminClient } from '@/lib/auth-server';
import { createSafeSearchCondition } from '@/lib/input-sanitization';
import { logTripCrud, crudAuditLogger } from '@/lib/crud-audit-logger';
import { handleApiError } from '@/lib/error-handler';
import { measureApiCall } from '@/lib/performance-monitor';

// GET /api/admin/trips - Get all trips with pagination and filters
export async function GET(request: NextRequest) {
  return measureApiCall('admin_trips_get', async () => {
    try {
    // Verify admin access
    const { user, hasAccess } = await verifyAdminAccess('trips', 'read');

    if (!hasAccess || !user) {
      return NextResponse.json(
        { error: 'Unauthorized. Admin privileges required.' },
        { status: 403 }
      );
    }

    const supabase = createAdminClient();
    const { searchParams } = new URL(request.url);

    // Parse query parameters
    const page = parseInt(searchParams.get('page') || '1', 10);
    const limit = parseInt(searchParams.get('limit') || '10', 10);
    const search = searchParams.get('search');
    const category = searchParams.get('category');
    const destination = searchParams.get('destination');
    const isActive = searchParams.get('isActive');

    // Build query
    let query = supabase
      .from('trips')
      .select('*', { count: 'exact' });

    // Apply filters with safe search
    if (search) {
      const searchConditions = createSafeSearchCondition(search, ['title', 'description']);
      if (searchConditions) {
        query = query.or(searchConditions);
      }
    }
    if (category && category !== 'all') {
      query = query.eq('category', category);
    }
    if (destination && destination !== 'all') {
      query = query.eq('destination', destination);
    }
    if (isActive !== null && isActive !== 'all') {
      query = query.eq('is_active', isActive === 'true');
    }

    // Apply pagination
    const from = (page - 1) * limit;
    const to = from + limit - 1;

    query = query
      .order('created_at', { ascending: false })
      .range(from, to);

    const { data: trips, error, count } = await query;

    if (error) {
      console.error('Error fetching trips:', error);
      return NextResponse.json(
        { error: 'Failed to fetch trips' },
        { status: 500 }
      );
    }

    const totalPages = Math.ceil((count || 0) / limit);

    return NextResponse.json({
      data: trips || [],
      pagination: {
        page,
        limit,
        total: count || 0,
        totalPages,
      },
    });
  } catch (error) {
    const appError = handleApiError(error);
    return NextResponse.json(
      { error: appError.message },
      { status: appError.statusCode }
    );
  }
  }, '/api/admin/trips');
}

// POST /api/admin/trips - Create a new trip
export async function POST(request: NextRequest) {
  return measureApiCall('admin_trips_create', async () => {
    try {
    // Verify admin access
    const { user, hasAccess } = await verifyAdminAccess('trips', 'create');

    if (!hasAccess || !user) {
      return NextResponse.json(
        { error: 'Unauthorized. Admin privileges required.' },
        { status: 403 }
      );
    }

    const supabase = createAdminClient();
    const body = await request.json();

    // Basic validation - must match database NOT NULL constraints
    const requiredFields = ['title', 'destination', 'days', 'nights', 'difficulty', 'price_per_person'];
    const missingFields = [];

    for (const field of requiredFields) {
      if (body[field] === undefined || body[field] === null || body[field] === '') {
        missingFields.push(field);
      }
    }

    if (missingFields.length > 0) {
      return NextResponse.json(
        {
          error: `Missing required fields: ${missingFields.join(', ')}`,
          missingFields,
          details: 'All required fields must be provided and cannot be empty'
        },
        { status: 400 }
      );
    }

    // Enhanced validation for price_per_person
    const pricePerPerson = parseFloat(body.price_per_person);
    if (isNaN(pricePerPerson) || pricePerPerson < 0 || pricePerPerson > 1000000) {
      return NextResponse.json(
        {
          error: 'Price per person must be a number between 0 and 10,00,000',
          field: 'price_per_person',
          value: body.price_per_person,
          validRange: { min: 0, max: 1000000 }
        },
        { status: 400 }
      );
    }

    // Enhanced validation for days and nights
    const days = parseInt(body.days);
    const nights = parseInt(body.nights);

    // Validate days
    if (isNaN(days) || days < 1 || days > 30) {
      return NextResponse.json(
        {
          error: 'Days must be a number between 1 and 30',
          field: 'days',
          value: body.days,
          validRange: { min: 1, max: 30 }
        },
        { status: 400 }
      );
    }

    // Validate nights
    if (isNaN(nights) || nights < 0 || nights > 31) {
      return NextResponse.json(
        {
          error: 'Nights must be a number between 0 and 31',
          field: 'nights',
          value: body.nights,
          validRange: { min: 0, max: 31 }
        },
        { status: 400 }
      );
    }

    // Validate difficulty
    const validDifficulties = ['easy', 'moderate', 'challenging', 'difficult'];
    if (!validDifficulties.includes(body.difficulty?.toLowerCase())) {
      return NextResponse.json(
        {
          error: `Difficulty must be one of: ${validDifficulties.join(', ')}`,
          field: 'difficulty',
          value: body.difficulty,
          validOptions: validDifficulties
        },
        { status: 400 }
      );
    }

    // Validate days/nights relationship
    const expectedNights = Math.max(0, days - 1);
    if (nights !== expectedNights) {
      return NextResponse.json(
        {
          error: `Nights must be exactly ${expectedNights} for ${days} day${days > 1 ? 's' : ''}`,
          field: 'nights',
          expectedValue: expectedNights,
          actualValue: nights,
          relatedField: 'days'
        },
        { status: 400 }
      );
    }

    // Generate slug from title
    const slug = body.title
      .toLowerCase()
      .replace(/[^a-z0-9]+/g, '-')
      .replace(/(^-|-$)/g, '');

    // Save trip to database
    const { data: trip, error: dbError } = await supabase
      .from('trips')
      .insert({
        ...body,
        slug,
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString(),
      })
      .select()
      .single();

    if (dbError) {
      console.error('Error saving trip to database:', dbError);

      // Log failed creation
      await crudAuditLogger.logFailedOperation(
        'create',
        'trip',
        'unknown',
        user.id,
        user.username || 'Unknown',
        dbError.message,
        request
      );

      return NextResponse.json(
        { error: 'Failed to save trip' },
        { status: 500 }
      );
    }

    // Log successful creation
    await logTripCrud.create(
      trip.id,
      trip.title,
      user.id,
      user.username || 'Unknown',
      body,
      request
    );

    return NextResponse.json({ data: trip });
  } catch (error) {
    const appError = handleApiError(error);
    return NextResponse.json(
      { error: appError.message },
      { status: appError.statusCode }
    );
  }
  }, '/api/admin/trips');
}