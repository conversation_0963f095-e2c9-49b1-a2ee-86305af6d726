import { NextRequest, NextResponse } from 'next/server';
import { verifyAdminAccess, createAdminClient } from '@/lib/auth-server';
import { handleApiError } from '@/lib/error-handler';
import { getDefaultTemplateValues } from '@/lib/utils/trip-templates';

// GET /api/admin/trips/template-values - Get template values from most recent trip
export async function GET(request: NextRequest) {
  try {
    // Verify admin access
    const { user, hasAccess } = await verifyAdminAccess('trips', 'read');

    if (!hasAccess || !user) {
      return NextResponse.json(
        { error: 'Unauthorized. Admin privileges required.' },
        { status: 403 }
      );
    }

    const supabase = createAdminClient();

    // Get the most recently created trip to use as template
    const { data: recentTrip, error } = await supabase
      .from('trips')
      .select(`
        cancellation_policy,
        things_to_carry,
        inclusions,
        exclusions,
        safety_supervision,
        benefits,
        payment_terms,
        special_notes
      `)
      .order('created_at', { ascending: false })
      .limit(1)
      .single();

    let templateValues;

    if (error || !recentTrip) {
      // If no trips exist or error occurred, use default values
      console.warn('No recent trip found for template values, using defaults');
      templateValues = getDefaultTemplateValues();
    } else {
      // Use values from most recent trip, falling back to defaults for null fields
      const defaults = getDefaultTemplateValues();
      templateValues = {
        cancellation_policy: recentTrip.cancellation_policy || defaults.cancellation_policy,
        things_to_carry: recentTrip.things_to_carry || defaults.things_to_carry,
        inclusions: recentTrip.inclusions || defaults.inclusions,
        exclusions: recentTrip.exclusions || defaults.exclusions,
        safety_supervision: recentTrip.safety_supervision || defaults.safety_supervision,
        benefits: recentTrip.benefits || defaults.benefits,
        payment_terms: recentTrip.payment_terms || defaults.payment_terms,
        special_notes: recentTrip.special_notes || defaults.special_notes,
      };
    }

    return NextResponse.json({ templateValues });
  } catch (error) {
    console.error('Error fetching template values:', error);
    const appError = handleApiError(error);
    return NextResponse.json(
      { error: appError.message },
      { status: appError.statusCode }
    );
  }
}
