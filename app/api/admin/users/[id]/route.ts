import { NextRequest, NextResponse } from 'next/server';
import { verifyOwnerAccess, createAdminClient } from '@/lib/auth-server';
import { crudAuditLogger } from '@/lib/crud-audit-logger';
import { handleApiError } from '@/lib/error-handler';

// PUT /api/admin/users/[id] - Update user roles (owner only)
export async function PUT(
  request: NextRequest,
  context: { params: Promise<{ id: string }> }
) {
  try {
    const { user, hasAccess } = await verifyOwnerAccess();

    if (!hasAccess || !user) {
      return NextResponse.json(
        { error: 'Unauthorized. Owner privileges required.' },
        { status: 403 }
      );
    }

    const body = await request.json();
    const { role_names, is_active } = body;
    const resolvedParams = await context.params;
    const userId = resolvedParams.id;

    if (!userId) {
      return NextResponse.json(
        { error: 'User ID is required' },
        { status: 400 }
      );
    }

    const adminSupabase = createAdminClient();

    // Get original user data for change tracking
    const { data: originalUser, error: userError } = await adminSupabase
      .from('admin_profiles')
      .select('*')
      .eq('id', userId)
      .single();

    if (userError) {
      console.error('Error fetching original user data:', userError);
      return NextResponse.json(
        { error: 'Failed to fetch user data' },
        { status: 500 }
      );
    }

    // Get original user roles for change tracking
    const { data: originalRoles, error: rolesError } = await adminSupabase
      .from('admin_user_roles')
      .select(`
        admin_roles (
          name
        )
      `)
      .eq('user_id', userId);

    if (rolesError) {
      console.error('Error fetching original user roles:', rolesError);
      return NextResponse.json(
        { error: 'Failed to fetch user roles' },
        { status: 500 }
      );
    }

    const originalRoleNames = originalRoles?.map((ur: any) => ur.admin_roles?.name).filter(Boolean) || [];

    // Update user active status if provided
    if (typeof is_active === 'boolean') {
      const { error: updateError } = await adminSupabase
        .from('admin_profiles')
        .update({ is_active })
        .eq('id', userId);

      if (updateError) {
        console.error('Error updating user status:', updateError);
        return NextResponse.json(
          { error: 'Failed to update user status' },
          { status: 500 }
        );
      }
    }

    // Update roles if provided
    if (role_names && Array.isArray(role_names)) {
      // Remove existing roles
      const { error: deleteError } = await adminSupabase
        .from('admin_user_roles')
        .delete()
        .eq('user_id', userId);

      if (deleteError) {
        console.error('Error removing existing roles:', deleteError);
        return NextResponse.json(
          { error: 'Failed to update user roles' },
          { status: 500 }
        );
      }

      // Get new role IDs
      const { data: roles, error: rolesError } = await adminSupabase
        .from('admin_roles')
        .select('id, name')
        .in('name', role_names);

      if (rolesError || !roles) {
        console.error('Error fetching roles:', rolesError);
        return NextResponse.json(
          { error: 'Invalid roles specified' },
          { status: 400 }
        );
      }

      // Assign new roles
      if (roles.length > 0) {
        const userRoleInserts = roles.map(role => ({
          user_id: userId,
          role_id: role.id
        }));

        const { error: insertError } = await adminSupabase
          .from('admin_user_roles')
          .insert(userRoleInserts);

        if (insertError) {
          console.error('Error assigning new roles:', insertError);
          return NextResponse.json(
            { error: 'Failed to assign new roles' },
            { status: 500 }
          );
        }
      }
    }

    // Track changes for audit log
    const changes: Record<string, { old: any; new: any }> = {};

    if (typeof is_active === 'boolean' && originalUser && originalUser.is_active !== is_active) {
      changes.is_active = { old: originalUser.is_active, new: is_active };
    }

    if (role_names && Array.isArray(role_names)) {
      const newRoleNames = role_names.sort();
      const oldRoleNames = originalRoleNames.sort();

      if (JSON.stringify(newRoleNames) !== JSON.stringify(oldRoleNames)) {
        changes.roles = { old: oldRoleNames, new: newRoleNames };
      }
    }

    // Log successful update
    await crudAuditLogger.logCrudOperation({
      operation: 'update',
      resourceType: 'admin_user',
      resourceId: userId,
      resourceTitle: `Admin User: ${originalUser?.username || originalUser?.full_name || 'Unknown'}`,
      userId: user.id,
      userEmail: user.email || user.username || 'Unknown',
      changes,
      success: true,
      metadata: {
        updated_at: new Date().toISOString(),
        fields_changed: Object.keys(changes),
        status_updated: typeof is_active === 'boolean',
        roles_updated: role_names && Array.isArray(role_names),
      },
    }, request);


    return NextResponse.json({ success: true });

  } catch (error) {
    const appError = handleApiError(error);
    return NextResponse.json(
      { error: appError.message },
      { status: appError.statusCode }
    );
  }
}

// DELETE /api/admin/users/[id] - Delete admin user (owner only)
export async function DELETE(
  request: NextRequest,
  context: { params: Promise<{ id: string }> }
) {
  try {
    const { user, hasAccess } = await verifyOwnerAccess();

    if (!hasAccess || !user) {
      return NextResponse.json(
        { error: 'Unauthorized. Owner privileges required.' },
        { status: 403 }
      );
    }

    const resolvedParams = await context.params;
    const userId = resolvedParams.id;

    if (!userId) {
      return NextResponse.json(
        { error: 'User ID is required' },
        { status: 400 }
      );
    }

    // Prevent deleting self
    if (userId === user.id) {
      return NextResponse.json(
        { error: 'Cannot delete your own account' },
        { status: 400 }
      );
    }

    const adminSupabase = createAdminClient();

    // Get user data before deletion for audit log
    const { data: userToDelete } = await adminSupabase
      .from('admin_profiles')
      .select('username, full_name')
      .eq('id', userId)
      .single();

    // Delete user from Supabase Auth (this will cascade delete related records)
    const { error: deleteError } = await adminSupabase.auth.admin.deleteUser(userId);

    if (deleteError) {
      console.error('Error deleting user:', deleteError);

      // Log failed deletion
      await crudAuditLogger.logFailedOperation(
        'delete',
        'admin_user',
        userId,
        user.id,
        user.email || user.username || 'Unknown',
        deleteError.message,
        request
      );

      return NextResponse.json(
        { error: 'Failed to delete user', details: deleteError.message },
        { status: 500 }
      );
    }

    // Log successful deletion
    await crudAuditLogger.logCrudOperation({
      operation: 'delete',
      resourceType: 'admin_user',
      resourceId: userId,
      resourceTitle: `Admin User: ${userToDelete?.username || 'Unknown'}`,
      userId: user.id,
      userEmail: user.email || user.username || 'Unknown',
      success: true,
      metadata: {
        deleted_at: new Date().toISOString(),
        deleted_user_username: userToDelete?.username,
      },
    }, request);


    return NextResponse.json({ success: true });

  } catch (error) {
    const appError = handleApiError(error);
    return NextResponse.json(
      { error: appError.message },
      { status: appError.statusCode }
    );
  }
}
