import { NextRequest, NextResponse } from 'next/server';
import { measureApiCall } from '@/lib/performance-monitor';

interface InstagramPost {
  id: string;
  caption: string;
  media_url: string;
  media_type: 'IMAGE' | 'VIDEO' | 'CAROUSEL_ALBUM';
  permalink: string;
  timestamp: string;
  like_count?: number;
  comments_count?: number;
}

// Cache for Instagram posts
let cachedPosts: InstagramPost[] | null = null;
let cacheTimestamp = 0;
const CACHE_DURATION = 60 * 60 * 1000; // 1 hour

export async function GET(request: NextRequest) {
  return measureApiCall('public_instagram_get', async () => {
    try {
    // Check if Instagram API is configured
    const accessToken = process.env.INSTAGRAM_ACCESS_TOKEN;

    if (!accessToken) {
      console.warn('Instagram access token not configured, Instagram feed disabled');
      return NextResponse.json({ error: 'Instagram not configured', disabled: true }, {
        status: 503,
        headers: {
          'Cache-Control': 'public, s-maxage=300, stale-while-revalidate=600',
        },
      });
    }

    // Check cache first
    if (cachedPosts && Date.now() - cacheTimestamp < CACHE_DURATION) {
      return NextResponse.json(cachedPosts, {
        headers: {
          'Cache-Control': 'public, s-maxage=3600, stale-while-revalidate=7200',
        },
      });
    }

    // Fetch from Instagram Basic Display API
    const response = await fetch(
      `https://graph.instagram.com/me/media?fields=id,caption,media_url,media_type,permalink,timestamp&access_token=${accessToken}&limit=12`
    );

    if (!response.ok) {
      throw new Error(`Instagram API error: ${response.status}`);
    }

    const data = await response.json();
    
    // Transform the data
    const posts: InstagramPost[] = data.data.map((post: any) => ({
      id: post.id,
      caption: post.caption || '',
      media_url: post.media_url,
      media_type: post.media_type,
      permalink: post.permalink,
      timestamp: post.timestamp,
    }));

    // Update cache
    cachedPosts = posts;
    cacheTimestamp = Date.now();

    return NextResponse.json(posts, {
      headers: {
        'Cache-Control': 'public, s-maxage=3600, stale-while-revalidate=7200',
      },
    });

  } catch (error) {
    console.error('Instagram API error:', error);

    // Check if Instagram is configured before returning fallback
    const accessToken = process.env.INSTAGRAM_ACCESS_TOKEN;
    if (!accessToken) {
      return NextResponse.json({ error: 'Instagram not configured', disabled: true }, {
        status: 503,
        headers: {
          'Cache-Control': 'public, s-maxage=300, stale-while-revalidate=600',
        },
      });
    }

    // Return cached posts if available, otherwise return error
    if (cachedPosts) {
      return NextResponse.json(cachedPosts, {
        headers: {
          'Cache-Control': 'public, s-maxage=300, stale-while-revalidate=600',
        },
      });
    }

    return NextResponse.json({ error: 'Instagram API temporarily unavailable' }, {
      status: 503,
      headers: {
        'Cache-Control': 'public, s-maxage=60, stale-while-revalidate=120',
      },
    });
  }
  }, '/api/instagram');
}


