import { NextRequest, NextResponse } from 'next/server';
import { createServerSupabase } from '@/lib/supabase-server';
import { handleApiError } from '@/lib/error-handler';

interface RouteParams {
  params: Promise<{
    slug: string;
  }>;
}

export async function GET(request: NextRequest, { params }: RouteParams) {
  try {
    const { slug } = await params;
    
    if (!slug) {
      return NextResponse.json(
        { error: 'Trip slug is required' },
        { status: 400 }
      );
    }

    const supabase = createServerSupabase();

    // Get trip by slug
    const { data: trip, error } = await supabase
      .from('trips')
      .select('*')
      .eq('slug', slug)
      .eq('is_active', true)
      .single();

    if (error) {
      if (error.code === 'PGRST116') {
        return NextResponse.json(
          { error: 'Trip not found or has been deactivated' },
          {
            status: 404,
            headers: {
              'Cache-Control': 'no-store, no-cache, must-revalidate, proxy-revalidate, max-age=0',
              'Pragma': 'no-cache',
              'Expires': '0',
              'Surrogate-Control': 'no-store',
            }
          }
        );
      }
      throw error;
    }

    // Transform the database trip data to match the expected client format
    const transformedTrip = {
      id: trip.id,
      title: trip.title,
      slug: trip.slug,
      description: trip.description || '',
      destination: trip.destination,
      days: trip.days || 0,
      nights: trip.nights || 0,
      difficulty: trip.difficulty || 'moderate',
      price_per_person: trip.price_per_person || 0,
      featured_image_url: trip.featured_image_url || '/images/fallback-trip.jpg',
      detailed_description: trip.detailed_description || trip.description || '',
      mode_of_travel: trip.mode_of_travel || undefined,
      pickup_location: trip.pickup_location || undefined,
      drop_location: trip.drop_location || undefined,
      property_used: trip.property_used || undefined,
      activities: trip.activities || [],
      optional_activities: trip.optional_activities || [],
      inclusions: trip.inclusions || [],
      exclusions: trip.exclusions || [],
      benefits: trip.benefits || [],
      safety_supervision: trip.safety_supervision || [],
      things_to_carry: trip.things_to_carry || [],
      available_dates: trip.available_dates || [], // Add the missing field
      itinerary: trip.itinerary || [],
      is_featured: trip.is_featured || false,
      is_active: trip.is_active || false,
      created_at: trip.created_at,
      updated_at: trip.updated_at,
    };

    return NextResponse.json(transformedTrip, {
      headers: {
        'Cache-Control': 'public, s-maxage=300, stale-while-revalidate=600',
      },
    });
  } catch (error) {
    const appError = handleApiError(error);
    return NextResponse.json(
      { error: appError.message },
      { status: appError.statusCode }
    );
  }
}
