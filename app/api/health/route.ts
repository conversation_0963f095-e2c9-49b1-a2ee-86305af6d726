import { NextRequest, NextResponse } from 'next/server';
import { getSystemHealth, getMetricsReport } from '@/lib/monitoring';
import { createServerSupabase } from '@/lib/supabase-server';
import { measureApiCall } from '@/lib/performance-monitor';

// Force dynamic rendering
export const dynamic = 'force-dynamic';

// GET /api/health - System health check
export async function GET(request: NextRequest) {
  return measureApiCall('public_health_check', async () => {
    const { searchParams } = new URL(request.url);
    const detailed = searchParams.get('detailed') === 'true';
    const since = searchParams.get('since');

    try {
    // Basic health check
    const systemHealth = getSystemHealth();
    
    // Test database connectivity
    let dbStatus = 'healthy';
    let dbResponseTime = 0;
    
    try {
      const startTime = performance.now();
      const supabase = createServerSupabase();
      
      // Simple query to test database
      const { error } = await supabase
        .from('trip_photos_details')
        .select('id')
        .limit(1);
      
      dbResponseTime = performance.now() - startTime;
      
      if (error) {
        dbStatus = 'unhealthy';
        console.error('Database health check failed:', error);
      } else if (dbResponseTime > 1000) {
        dbStatus = 'degraded';
      }
    } catch (error) {
      dbStatus = 'unhealthy';
      console.error('Database connection failed:', error);
    }

    const healthData = {
      status: systemHealth.status,
      timestamp: new Date().toISOString(),
      uptime: systemHealth.uptime,
      version: process.env.npm_package_version || '1.0.0',
      environment: process.env.NODE_ENV || 'development',
      services: {
        database: {
          status: dbStatus,
          responseTime: dbResponseTime,
        },
        api: {
          status: systemHealth.status,
          responseTime: systemHealth.responseTime,
        },
      },
      system: {
        memory: systemHealth.memoryUsage,
        errorRate: systemHealth.errorRate,
      },
    };

    // Add detailed metrics if requested
    let responseData: typeof healthData & { metrics?: any } = healthData;
    if (detailed) {
      const sinceDate = since ? new Date(since) : undefined;
      const metricsReport = getMetricsReport(sinceDate);

      responseData = { ...healthData, metrics: metricsReport };
    }

    // Determine overall status
    let overallStatus = 'healthy';
    if (dbStatus === 'degraded' || systemHealth.status === 'degraded') {
      overallStatus = 'degraded';
    }
    if (dbStatus === 'unhealthy' || systemHealth.status === 'unhealthy') {
      overallStatus = 'unhealthy';
    }

    const statusCode = overallStatus === 'healthy' ? 200 : 
                      overallStatus === 'degraded' ? 200 : 503;

    return NextResponse.json({
      ...responseData,
      status: overallStatus,
    }, {
      status: statusCode,
      headers: {
        'Cache-Control': 'no-cache, no-store, must-revalidate',
        'Pragma': 'no-cache',
        'Expires': '0',
      },
    });

  } catch (error) {
    console.error('Health check failed:', error);

    return NextResponse.json({
      status: 'unhealthy',
      timestamp: new Date().toISOString(),
      error: 'Health check failed',
      message: error instanceof Error ? error.message : 'Unknown error',
    }, {
      status: 503,
      headers: {
        'Cache-Control': 'no-cache, no-store, must-revalidate',
        'Pragma': 'no-cache',
        'Expires': '0',
      },
    });
  }
  }, '/api/health');
}
