import { NextRequest, NextResponse } from 'next/server';

export async function POST(request: NextRequest) {
  // Only allow in development
  if (process.env.NODE_ENV !== 'development') {
    return NextResponse.json({ error: 'Not available in production' }, { status: 404 });
  }

  try {
    const errorData = await request.json();
    
    // Log to terminal with clear formatting
    console.log('\n' + '='.repeat(80));
    console.log('🚨 CLIENT-SIDE ERROR DETECTED');
    console.log('='.repeat(80));
    console.log(`⏰ Time: ${errorData.timestamp}`);
    console.log(`🆔 Error ID: ${errorData.errorId}`);
    console.log(`🌐 URL: ${errorData.url}`);
    console.log(`📝 Type: ${errorData.type}`);
    console.log(`💬 Message: ${errorData.message}`);
    
    if (errorData.componentStack) {
      console.log('\n📍 Component Stack:');
      console.log(errorData.componentStack);
    }
    
    if (errorData.stack) {
      console.log('\n🔍 Error Stack:');
      console.log(errorData.stack);
    }
    
    console.log('='.repeat(80) + '\n');
    
    return NextResponse.json({ success: true });
  } catch (error) {
    console.error('Failed to log client error:', error);
    return NextResponse.json({ error: 'Failed to log error' }, { status: 500 });
  }
}
