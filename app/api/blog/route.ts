import { NextRequest, NextResponse } from 'next/server';
import { createServerSupabase } from '@/lib/supabase-server';
import { measureDatabaseQuery, measureApiCall } from '@/lib/performance-monitor';
import { createSafeSearchCondition } from '@/lib/input-sanitization';
import { handleApiError } from '@/lib/error-handler';

// Force dynamic rendering to prevent caching
export const dynamic = 'force-dynamic';

// GET /api/blog - Get blog posts with filtering
export async function GET(request: NextRequest) {
  return measureApiCall('public_blog_list', async () => {
    try {
    const { searchParams } = new URL(request.url);

    // Parse query parameters with validation
    const pageParam = searchParams.get('page') || '1';
    const limitParam = searchParams.get('limit') || '10';

    // Graceful handling of non-numeric parameters
    const parsedPage = parseInt(pageParam, 10);
    const parsedLimit = parseInt(limitParam, 10);

    const page = Math.max(1, isNaN(parsedPage) ? 1 : parsedPage);
    const limit = Math.max(1, Math.min(50, isNaN(parsedLimit) ? 10 : parsedLimit));
    const search = searchParams.get('search');
    const category = searchParams.get('category');

    // Use performance monitoring without caching for real-time updates
    const result = await measureDatabaseQuery(
      'public_blog_list',
      async () => {
        const supabase = createServerSupabase();

            // Build query
            let query = supabase
              .from('blog_posts')
              .select(`
                id,
                title,
                slug,
                excerpt,
                content,
                featured_image_url,
                author,
                category,
                tags,
                published_at,
                created_at
              `, { count: 'exact' })
              .eq('is_published', true);

            // Apply filters with safe search
            if (search) {
              const searchConditions = createSafeSearchCondition(search, ['title', 'excerpt', 'content']);
              if (searchConditions) {
                query = query.or(searchConditions);
              }
            }
            if (category) {
              query = query.eq('category', category);
            }

            // Apply pagination
            const from = (page - 1) * limit;
            const to = from + limit - 1;

            query = query
              .order('published_at', { ascending: false })
              .range(from, to);

            const { data: posts, error, count } = await query;

            if (error) {
              throw error;
            }

            const totalPages = Math.ceil((count || 0) / limit);

        return {
          data: posts || [],
          pagination: {
            page,
            limit,
            total: count || 0,
            totalPages,
          },
        };
      },
      'public_query'
    );

    return NextResponse.json(result, {
      headers: {
        'Cache-Control': 'no-store, no-cache, must-revalidate, proxy-revalidate, max-age=0',
        'Pragma': 'no-cache',
        'Expires': '0',
        'Surrogate-Control': 'no-store',
      },
    });
  } catch (error) {
    const appError = handleApiError(error);
    return NextResponse.json(
      { error: appError.message },
      { status: appError.statusCode }
    );
  }
  }, '/api/blog');
}

// POST /api/blog - Create a new blog post (Admin only)
export async function POST(request: NextRequest) {
  return measureApiCall('public_blog_create', async () => {
    try {
    // For now, return placeholder response
    return NextResponse.json({
      message: 'Blog post creation functionality temporarily disabled',
      data: null
    }, { status: 501 });
  } catch (error) {
    const appError = handleApiError(error);
    return NextResponse.json(
      { error: appError.message },
      { status: appError.statusCode }
    );
  }
  }, '/api/blog');
}
