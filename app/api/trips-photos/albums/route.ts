import { NextRequest, NextResponse } from 'next/server';
import { createServerSupabase } from '@/lib/supabase-server';

// Type guard to validate raw album data
function isValidRawAlbum(data: any): data is { id: string; trip_name: string; [key: string]: any } {
  return data &&
         typeof data === 'object' &&
         typeof data.id === 'string' &&
         typeof data.trip_name === 'string';
}

// Utility function to transform raw album data to client format
function transformAlbumData(rawAlbum: any) {
  if (!isValidRawAlbum(rawAlbum)) {
    console.warn('Invalid album data received:', rawAlbum);
    return null;
  }

  // Safely extract and validate fields
  const album = {
    id: rawAlbum.id,
    trip_name: rawAlbum.trip_name,
    trip_description: rawAlbum.trip_description || undefined,
    featured_image_url: rawAlbum.featured_image_url || undefined,
    access_password_hash: rawAlbum.access_password_hash || undefined,
    storage_type: rawAlbum.storage_type || null,
    created_at: rawAlbum.created_at || new Date().toISOString(),
    updated_at: rawAlbum.updated_at || new Date().toISOString(),
    google_photos_album_id: rawAlbum.google_photos_album_id || undefined,
    oauth_user_email: rawAlbum.oauth_user_email || undefined,
    oauth_refresh_token: rawAlbum.oauth_refresh_token || undefined,
    oauth_refresh_token_encrypted: rawAlbum.oauth_refresh_token_encrypted || undefined,
    security_version: rawAlbum.security_version || undefined,
    manual_shareable_url: rawAlbum.manual_shareable_url || undefined,
  };

  // Use manual shareable URL only
  const downloadLink = album.manual_shareable_url || '#';

  return {
    id: album.id,
    title: album.trip_name,
    coverImage: album.featured_image_url || 'https://res.cloudinary.com/peebst3r/image/upload/v1748754487/positive7/trips/Manali-River.jpg',
    downloadLink: downloadLink,
    description: album.trip_description || 'Photo album from our educational trip',
    password: album.access_password_hash ? 'protected' : null
  };
}

export async function GET(request: NextRequest) {
  try {
    const supabase = createServerSupabase();

    // Fetch trip photo albums from the database
    const { data: tripPhotoAlbums, error } = await supabase
      .from('trip_photos_details')
      .select('*')
      .order('created_at', { ascending: false });

    if (error) {
      console.error('Error fetching trip photo albums:', error);
      return NextResponse.json(
        { error: 'Failed to fetch photo albums' },
        { status: 500 }
      );
    }

    // Transform the data to match the client component's expected format
    const transformedAlbums = tripPhotoAlbums
      .map(transformAlbumData)
      .filter((album): album is NonNullable<typeof album> => album !== null);

    return NextResponse.json(transformedAlbums, {
      headers: {
        'Cache-Control': 'no-store, no-cache, must-revalidate, proxy-revalidate, max-age=0',
        'Pragma': 'no-cache',
        'Expires': '0',
        'Surrogate-Control': 'no-store',
      },
    });
  } catch (error) {
    console.error('API Error:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}
