import { NextRequest, NextResponse } from 'next/server';
import { createServerSupabase } from '@/lib/supabase-server';
import { verifyPassword } from '@/lib/security';
import { passwordVerifyRateLimit, withRateLimit } from '@/lib/rate-limit';
import {
  handleApiError,
  ValidationError,
  NotFoundError,
  AuthenticationError,
  formatErrorResponse,
  logError,
  validateRequired,
  validateUUID,
  getClientIP
} from '@/lib/error-handler';

async function handlePasswordVerification(request: NextRequest) {
  try {
    const body = await request.json();
    const { albumId, password } = body;

    // Validate required fields
    validateRequired(albumId, 'Album ID');
    validateRequired(password, 'Password');
    validateUUID(albumId, 'Album ID');

    const supabase = createServerSupabase();

    // Fetch the album details
    const { data: album, error } = await supabase
      .from('trip_photos_details')
      .select('*')
      .eq('id', albumId)
      .single();

    if (error || !album) {
      throw new NotFoundError('Album');
    }

    // Type assertion for the album data
    const albumData = album as any;

    // Check password using hashed password verification only
    if (!albumData.access_password_hash) {
      throw new ValidationError('Album password not properly configured');
    }

    const isPasswordValid = await verifyPassword(password, albumData.access_password_hash);

    if (!isPasswordValid) {
      throw new AuthenticationError('Incorrect password');
    }

    // Return the download link only if password is correct
    const downloadLink = albumData.manual_shareable_url || '#';

    if (downloadLink === '#') {
      throw new ValidationError('Album shareable link not configured');
    }

    return NextResponse.json({
      success: true,
      downloadLink: downloadLink,
      albumName: albumData.trip_name
    });

  } catch (error) {
    const appError = handleApiError(error);
    logError(appError, {
      endpoint: '/api/trips-photos/verify-password',
      method: 'POST',
      ip: getClientIP(request)
    });

    return NextResponse.json(
      formatErrorResponse(appError),
      { status: appError.statusCode }
    );
  }
}

// Apply rate limiting to the password verification endpoint
export const POST = withRateLimit(passwordVerifyRateLimit, handlePasswordVerification, {
  windowMs: 5 * 60 * 1000, // 5 minutes
  max: 10,
  message: 'Too many password verification attempts, please try again later.'
});
