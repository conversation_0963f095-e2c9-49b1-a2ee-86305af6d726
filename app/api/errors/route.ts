import { NextRequest, NextResponse } from 'next/server';
import { withRateLimit, rateLimiters } from '@/lib/rate-limiter';
import { handleApiError } from '@/lib/error-handler';

// Force dynamic rendering to prevent caching
export const dynamic = 'force-dynamic';

interface ErrorLogData {
  message: string;
  stack?: string;
  componentStack?: string;
  errorId?: string;
  timestamp: string;
  userAgent: string;
  url: string;
  userId?: string;
  sessionId?: string;
  additionalData?: Record<string, any>;
}

// POST /api/errors - Log client-side errors
async function handleErrorLogging(request: NextRequest) {
  try {
    const body: ErrorLogData = await request.json();

    // Basic validation
    if (!body.message || !body.timestamp) {
      return NextResponse.json(
        { error: 'Missing required fields: message, timestamp' },
        { status: 400 }
      );
    }

    // Get client information
    const clientInfo = {
      ip: request.headers.get('x-forwarded-for') || 
          request.headers.get('x-real-ip') || 
          'unknown',
      userAgent: request.headers.get('user-agent') || 'unknown',
      referer: request.headers.get('referer') || 'unknown',
      timestamp: new Date().toISOString()
    };

    // Sanitize error data to prevent XSS
    const sanitizedError = {
      message: body.message.replace(/<script\b[^<]*(?:(?!<\/script>)<[^<]*)*<\/script>/gi, ''),
      stack: body.stack?.replace(/<script\b[^<]*(?:(?!<\/script>)<[^<]*)*<\/script>/gi, ''),
      componentStack: body.componentStack?.replace(/<script\b[^<]*(?:(?!<\/script>)<[^<]*)*<\/script>/gi, ''),
      errorId: body.errorId,
      timestamp: body.timestamp,
      userAgent: body.userAgent,
      url: body.url,
      userId: body.userId,
      sessionId: body.sessionId,
      additionalData: body.additionalData
    };

    // Create comprehensive error log
    const errorLog = {
      ...sanitizedError,
      clientInfo,
      severity: determineSeverity(sanitizedError.message),
      category: categorizeError(sanitizedError.message, sanitizedError.stack),
      environment: process.env.NODE_ENV || 'unknown'
    };

    // Log to console with formatting
    console.error('🚨 CLIENT ERROR LOGGED:');
    console.error('━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━');
    console.error('Error ID:', errorLog.errorId || 'N/A');
    console.error('Severity:', errorLog.severity);
    console.error('Category:', errorLog.category);
    console.error('Message:', errorLog.message);
    console.error('URL:', errorLog.url);
    console.error('User Agent:', errorLog.userAgent);
    console.error('Client IP:', clientInfo.ip);
    console.error('Timestamp:', errorLog.timestamp);
    
    if (errorLog.stack) {
      console.error('Stack Trace:');
      console.error(errorLog.stack);
    }
    
    if (errorLog.componentStack) {
      console.error('Component Stack:');
      console.error(errorLog.componentStack);
    }
    
    console.error('━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━');

    // In production, you might want to:
    // 1. Store errors in a database
    // 2. Send to error tracking service (Sentry, LogRocket, etc.)
    // 3. Send alerts for critical errors
    // 4. Aggregate error metrics

    if (process.env.NODE_ENV === 'production') {
      // Example: Send to external error tracking service
      await sendToErrorTrackingService(errorLog);
      
      // Example: Store in database for analysis
      await storeErrorInDatabase(errorLog);
      
      // Example: Send alert for critical errors
      if (errorLog.severity === 'critical') {
        await sendCriticalErrorAlert(errorLog);
      }
    }

    return NextResponse.json({
      message: 'Error logged successfully',
      errorId: errorLog.errorId,
      timestamp: clientInfo.timestamp
    }, { status: 200 });

  } catch (error) {
    const appError = handleApiError(error);

    // Don't expose internal errors to client
    return NextResponse.json(
      { error: 'Failed to log error' },
      { status: 500 }
    );
  }
}

// Determine error severity based on message content
function determineSeverity(message: string): 'low' | 'medium' | 'high' | 'critical' {
  const criticalKeywords = [
    'network error',
    'failed to fetch',
    'authentication failed',
    'payment failed',
    'database error'
  ];
  
  const highKeywords = [
    'uncaught',
    'unhandled',
    'cannot read property',
    'is not a function',
    'permission denied'
  ];
  
  const mediumKeywords = [
    'warning',
    'deprecated',
    'validation error',
    'form error'
  ];

  const lowerMessage = message.toLowerCase();
  
  if (criticalKeywords.some(keyword => lowerMessage.includes(keyword))) {
    return 'critical';
  }
  
  if (highKeywords.some(keyword => lowerMessage.includes(keyword))) {
    return 'high';
  }
  
  if (mediumKeywords.some(keyword => lowerMessage.includes(keyword))) {
    return 'medium';
  }
  
  return 'low';
}

// Categorize error for better organization
function categorizeError(message: string, stack?: string): string {
  const lowerMessage = message.toLowerCase();
  const lowerStack = stack?.toLowerCase() || '';
  
  if (lowerMessage.includes('network') || lowerMessage.includes('fetch')) {
    return 'network';
  }
  
  if (lowerMessage.includes('auth') || lowerMessage.includes('permission')) {
    return 'authentication';
  }
  
  if (lowerMessage.includes('validation') || lowerMessage.includes('form')) {
    return 'validation';
  }
  
  if (lowerStack.includes('react') || lowerStack.includes('component')) {
    return 'react';
  }
  
  if (lowerMessage.includes('javascript') || lowerMessage.includes('syntax')) {
    return 'javascript';
  }
  
  return 'general';
}

// Placeholder functions for production error handling
async function sendToErrorTrackingService(errorLog: any) {
  // Example: Send to Sentry, LogRocket, etc.
  // await sentry.captureException(errorLog);
}

async function storeErrorInDatabase(errorLog: any) {
  // Example: Store in Supabase or other database
  // const supabase = createServerSupabase();
  // await supabase.from('error_logs').insert(errorLog);
}

async function sendCriticalErrorAlert(errorLog: any) {
  // Example: Send email/SMS alert for critical errors
  // await sendEmail({
  //   to: '<EMAIL>',
  //   subject: 'Critical Error Alert',
  //   body: `Critical error occurred: ${errorLog.message}`
  // });
}

// Apply rate limiting to prevent spam
export const POST = withRateLimit(handleErrorLogging, rateLimiters.general);

// GET method for health check
export async function GET() {
  return NextResponse.json({
    status: 'ok',
    endpoint: 'error-logging',
    timestamp: new Date().toISOString()
  });
}
