import { NextRequest, NextResponse } from 'next/server';
import { createServerSupabase } from '@/lib/supabase-server';
import { sendInquiryNotification, InquiryEmailData } from '@/lib/email';
import { measureApiCall } from '@/lib/performance-monitor';

import { apiRateLimit, withRateLimit } from '@/lib/rate-limit';
import { sanitizeInput } from '@/lib/security';
import {
  handleApiError,
  ValidationError,
  RateLimitError,
  formatErrorResponse,
  logError,
  validateRequired,
  validateEmail,
  getClientIP
} from '@/lib/error-handler';

// Contact form validation schema
const contactFormSchema = {
  name: { required: true, minLength: 2 },
  email: { required: true, format: 'email' },
  message: { required: true, minLength: 10 },
  phone: { required: false },
  subject: { required: false }
};

// Validation helper function with proper trimming and unknown field rejection
const validateData = (data: any, schema: any) => {
  // Check for unknown fields first
  const allowedFields = Object.keys(schema);
  const providedFields = Object.keys(data);
  const unknownFields = providedFields.filter(field => !allowedFields.includes(field));

  if (unknownFields.length > 0) {
    throw new ValidationError(`Unknown fields: ${unknownFields.join(', ')}`);
  }

  for (const [field, rules] of Object.entries(schema)) {
    const rawValue = data[field];
    const fieldRules = rules as any;

    // Trim string values properly
    const value = typeof rawValue === 'string' ? rawValue.trim() : rawValue;

    if (fieldRules.required && (!value || value === '')) {
      throw new ValidationError(`${field} is required`);
    }

    if (value && fieldRules.minLength && value.length < fieldRules.minLength) {
      throw new ValidationError(`${field} must be at least ${fieldRules.minLength} characters long`);
    }

    if (value && fieldRules.format === 'email') {
      validateEmail(value);
    }
  }
};

// Force dynamic rendering to prevent caching
export const dynamic = 'force-dynamic';

// POST /api/contact - Submit contact form with enhanced validation and rate limiting
async function handleContactSubmission(request: NextRequest) {
  return measureApiCall('public_contact_submit', async () => {
    try {
    const body = await request.json();

    // Schema-based validation
    validateData(body, contactFormSchema);

    // Sanitize inputs
    const sanitizedData = {
      name: sanitizeInput(body.name?.trim() || ''),
      email: sanitizeInput(body.email?.trim() || ''),
      phone: body.phone ? sanitizeInput(body.phone.trim()) : null,
      subject: body.subject ? sanitizeInput(body.subject.trim()) : null,
      message: sanitizeInput(body.message?.trim() || ''),
    };

    // Additional validation after sanitization
    if (sanitizedData.name.length < 2) {
      throw new ValidationError('Name must be at least 2 characters long');
    }
    if (sanitizedData.message.length < 10) {
      throw new ValidationError('Message must be at least 10 characters long');
    }

    const validatedData = sanitizedData;

    // Additional security checks
    const suspiciousPatterns = [
      /<script/i,
      /javascript:/i,
      /on\w+\s*=/i,
      /data:text\/html/i,
      /<iframe/i,
      /<object/i,
      /<embed/i
    ];

    const textFields = [validatedData.name, validatedData.subject, validatedData.message].filter(Boolean);
    const hasSuspiciousContent = textFields.some(field =>
      field && suspiciousPatterns.some(pattern => pattern.test(field))
    );

    if (hasSuspiciousContent) {
      console.warn('Suspicious content detected in contact form:', {
        ip: request.headers.get('x-forwarded-for') || 'unknown',
        userAgent: request.headers.get('user-agent') || 'unknown',
        timestamp: new Date().toISOString()
      });
      
      return NextResponse.json(
        { error: 'Invalid content detected' },
        { status: 400 }
      );
    }

    // Check for spam patterns
    const spamIndicators = [
      /\b(viagra|cialis|casino|lottery|winner|congratulations)\b/i,
      /\b(click here|act now|limited time|urgent)\b/i,
      /\$\$\$|\b\d+\s*%\s*off\b/i,
      /\b(free money|make money|work from home)\b/i
    ];

    const hasSpamContent = textFields.some(field =>
      field && spamIndicators.some(pattern => pattern.test(field))
    );

    if (hasSpamContent) {
      console.warn('Potential spam detected in contact form:', {
        ip: request.headers.get('x-forwarded-for') || 'unknown',
        email: validatedData.email,
        timestamp: new Date().toISOString()
      });
      
      // Return success to avoid revealing spam detection
      return NextResponse.json({
        message: 'Thank you for your message. We will get back to you soon!'
      }, { status: 200 });
    }

    const supabase = createServerSupabase();

    // Check for duplicate submissions (same email and message in last 5 minutes)
    const fiveMinutesAgo = new Date(Date.now() - 5 * 60 * 1000).toISOString();
    
    const { data: recentSubmissions } = await supabase
      .from('inquiries')
      .select('id')
      .eq('email', validatedData.email)
      .eq('message', validatedData.message)
      .gte('created_at', fiveMinutesAgo);

    if (recentSubmissions && recentSubmissions.length > 0) {
      throw new RateLimitError('Duplicate submission detected. Please wait before submitting again.');
    }

    // Save inquiry to database
    const { data: inquiry, error: dbError } = await supabase
      .from('inquiries')
      .insert({
        name: validatedData.name,
        email: validatedData.email,
        phone: validatedData.phone,
        subject: validatedData.subject,
        message: validatedData.message,
        inquiry_type: 'Contact Form',
        status: 'new',
        metadata: {
          source: 'contact_form',
          ip: getClientIP(request),
          userAgent: request.headers.get('user-agent') || 'unknown',
          timestamp: new Date().toISOString()
        }
      })
      .select()
      .single();

    if (dbError) {
      console.error('Error saving contact form to database:', dbError);
      throw new Error('Failed to save contact form to database');
    }

    // Log successful submission
    console.log('📧 CONTACT FORM SUBMITTED:');
    console.log('━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━');
    console.log('Name:', inquiry.name);
    console.log('Email:', inquiry.email);
    console.log('Subject:', inquiry.subject);
    console.log('Message:', inquiry.message.substring(0, 100) + '...');
    console.log('━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━');

    // Send email notification
    try {
      const emailData: InquiryEmailData = {
        id: inquiry.id,
        name: inquiry.name,
        email: inquiry.email,
        phone: inquiry.phone || undefined,
        subject: inquiry.subject || undefined,
        message: inquiry.message,
        inquiry_type: 'Contact Form',
        created_at: inquiry.created_at || new Date().toISOString()
      };
      
      await sendInquiryNotification(emailData);
      console.log('✅ Email notification sent successfully');
    } catch (emailError) {
      console.error('❌ Error sending email notification:', emailError);
      // Don't fail the request if email fails, just log it
    }

    return NextResponse.json({
      message: 'Thank you for your message! We will get back to you within 24 hours.',
      data: {
        id: inquiry.id,
        name: inquiry.name,
        email: inquiry.email,
        subject: inquiry.subject,
        created_at: inquiry.created_at
      }
    }, { status: 201 });

  } catch (error) {
    const appError = handleApiError(error);
    logError(appError, {
      endpoint: '/api/contact',
      method: 'POST',
      ip: getClientIP(request)
    });

    return NextResponse.json(
      formatErrorResponse(appError),
      { status: appError.statusCode }
    );
  }
  }, '/api/contact');
}

// Apply rate limiting to the contact form endpoint
export const POST = withRateLimit(apiRateLimit, handleContactSubmission, {
  windowMs: 15 * 60 * 1000, // 15 minutes
  max: 100,
  message: 'Too many requests, please try again later.'
});

// GET method for health check
export async function GET() {
  return measureApiCall('public_contact_health', async () => {
    return NextResponse.json({
      status: 'ok',
      endpoint: 'contact',
      timestamp: new Date().toISOString()
    });
  }, '/api/contact');
}
