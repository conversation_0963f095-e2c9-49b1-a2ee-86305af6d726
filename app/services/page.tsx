import { Metadata } from 'next';
import { Suspense } from 'react';
import Header from '@/components/layout/Header';
import Footer from '@/components/layout/Footer';
import ServicesOverview from '@/components/services/ServicesOverview';
import { PageErrorBoundary } from '@/components/error/ComprehensiveErrorBoundary';
import CacheManager from '@/components/cache/CacheManager';
import { JsonLd } from '@/components/seo/JsonLd';

export const metadata: Metadata = {
  title: 'Our Services - Positive7 Educational Tours',
  description: 'Discover our comprehensive range of educational and adventure services including EduTours, Adventures, Girls Go Solo trips, and Holiday packages. Experience learning beyond the classroom with Positive7.',
  keywords: 'educational tours, adventure camps, girls solo travel, student trips, experiential learning, outdoor education, team building, camping, Gujarat tourism',
  openGraph: {
    title: 'Our Services - Positive7 Educational Tours',
    description: 'Comprehensive educational and adventure services for students and travelers. From classroom to adventure - experience learning like never before.',
    type: 'website',
    url: 'https://positive7.in/services',
    siteName: 'Positive7 Educational Tours',
    images: [
      {
        url: '/images/fallback-image.jpg',
        width: 1200,
        height: 630,
        alt: 'Positive7 Services - Educational Tours and Adventures',
      },
    ],
  },
  twitter: {
    card: 'summary_large_image',
    title: 'Our Services - Positive7 Educational Tours',
    description: 'Comprehensive educational and adventure services for students and travelers.',
    images: ['/images/fallback-image.jpg'],
  },
  alternates: {
    canonical: 'https://positive7.in/services',
  },
};

export default function ServicesPage() {
  const structuredData = {
    "@context": "https://schema.org",
    "@type": "Service",
    "name": "Positive7 Educational Tours Services",
    "description": "Comprehensive educational and adventure services including EduTours, Adventures, Girls Go Solo trips, and Holiday packages.",
    "provider": {
      "@type": "Organization",
      "name": "Positive7 Educational Tours",
      "url": "https://positive7.in",
      "logo": "https://positive7.in/images/positive7-logo.png"
    },
    "serviceType": [
      "Educational Tours",
      "Adventure Camps",
      "Solo Travel for Women",
      "Holiday Packages"
    ],
    "areaServed": "India",
    "url": "https://positive7.in/services"
  };

  return (
    <PageErrorBoundary context="services-page">
      {/* Cache management for dynamic content */}
      <CacheManager addMetaTags={true} updateSW={true} />

      {/* Add JSON-LD structured data */}
      <JsonLd data={structuredData} />

      <Header />
      <main className="flex-1">
        <div className="min-h-screen bg-gradient-to-br from-coral-50 to-teal-50">
          <Suspense fallback={
            <div className="flex items-center justify-center min-h-screen">
              <div className="animate-spin rounded-full h-32 w-32 border-b-2 border-primary-600"></div>
            </div>
          }>
            <ServicesOverview />
          </Suspense>
        </div>
      </main>
      <Footer />
    </PageErrorBoundary>
  );
}
