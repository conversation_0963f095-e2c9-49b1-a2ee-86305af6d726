import { NextRequest, NextResponse } from 'next/server';
import { COMPANY_INFO } from '@/lib/constants';

export async function GET(request: NextRequest) {
  const baseUrl = COMPANY_INFO.website;
  
  const robotsTxt = `User-agent: *
Allow: /

# Disallow admin and private areas
Disallow: /admin/
Disallow: /api/
Disallow: /_next/
Disallow: /offline/

# Allow specific API endpoints for SEO
Allow: /api/sitemap
Allow: /api/rss

# Crawl delay (optional, helps with server load)
Crawl-delay: 1

# Sitemap location
Sitemap: ${baseUrl}/sitemap.xml

# Additional sitemaps (if needed in future)
# Sitemap: ${baseUrl}/sitemap-images.xml
# Sitemap: ${baseUrl}/sitemap-news.xml

# Host directive (helps with canonical domain)
Host: ${baseUrl}`;

  return new NextResponse(robotsTxt, {
    headers: {
      'Content-Type': 'text/plain',
      'Cache-Control': 'public, max-age=86400, s-maxage=86400',
    },
  });
}
