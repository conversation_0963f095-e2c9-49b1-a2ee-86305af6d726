import { Metadata } from 'next';
import { Suspense } from 'react';
import Header from '@/components/layout/Header';
import Footer from '@/components/layout/Footer';
import CacheManager from '@/components/cache/CacheManager';
import { PageErrorBoundary } from '@/components/error/ComprehensiveErrorBoundary';
import UdbhavClient from '@/components/rural-initiative/UdbhavClient';
import { UDBHAV_INFO } from '@/lib/constants';

export const metadata: Metadata = {
  title: 'Udbhav Initiative | Positive7 Educational Tours - Connecting Rural & Urban India',
  description: 'Experience authentic rural life with Udbhav - farming, traditional crafts like Warli art, bamboo craft, cattle activities, folk music & dance. A program to improve tribal livelihood and connect urban youth with their roots.',
  keywords: 'Udbhav, rural initiative, farming activities, Warli art, bamboo craft, traditional games, cattle activities, folk music, rural tourism, village betterment, tribal livelihood, Positive7, Dang villages, cultural exchange, traditional cooking, gadget free life',
  openGraph: {
    title: 'Udbhav Initiative | Positive7 - Authentic Rural Life Experience',
    description: 'Take a step backwards to move forward stronger. Water your roots so your soul can blossom. Experience authentic rural life through farming, traditional crafts, and cultural exchange.',
    images: [
      {
        url: UDBHAV_INFO.images[0],
        width: 1200,
        height: 630,
        alt: 'Udbhav Initiative - Rural Life Experience with Traditional Activities'
      }
    ]
  },
  twitter: {
    card: 'summary_large_image',
    title: 'Udbhav Initiative | Positive7 - Authentic Rural Life',
    description: 'Experience farming, Warli art, traditional games, and folk music. Connect urban youth with rural roots through authentic cultural exchange.',
    images: [UDBHAV_INFO.images[0]]
  }
};

// Loading component for Suspense fallback
function UdbhavPageLoading() {
  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 via-white to-green-50 flex items-center justify-center">
      <div className="text-center">
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto mb-4"></div>
        <p className="text-gray-600">Loading Udbhav Initiative...</p>
      </div>
    </div>
  );
}

export default function UdbhavPage() {
  return (
    <PageErrorBoundary context="udbhav-page">
      {/* Cache management for dynamic content */}
      <CacheManager addMetaTags={true} updateSW={true} />

      <Header />
      <main className="flex-1">
        <div className="min-h-screen bg-gradient-to-br from-blue-50 via-white to-green-50">
          <Suspense fallback={<UdbhavPageLoading />}>
            <UdbhavClient />
          </Suspense>
        </div>
      </main>
      <Footer />
    </PageErrorBoundary>
  );
}
