# Dependencies
/node_modules
/.pnp
.pnp.js
yarn.lock
package-lock.json
pnpm-lock.yaml

# Next.js
/.next/
/out/
/build

# Production builds
dist/
build/

# Environment files
.env
.env*.local
.env.development
.env.test
.env.production

# Vercel
.vercel

# TypeScript
*.tsbuildinfo
next-env.d.ts

# IDE/Editor specific
.idea/
.vscode/
*.swp
*.swo
.cursor/
*.sublime-*

# OS specific
.DS_Store
Thumbs.db
*.tmp
*.temp

# Logs
logs/
*.log
npm-debug.log*
yarn-debug.log*
yarn-error.log*
.pnpm-debug.log*
lerna-debug.log*

# Cache directories
.npm
.yarn
.pnpm
.eslintcache
.stylelintcache
.cache
.turbo
.parcel-cache

# Runtime data
pids
*.pid
*.seed
*.pid.lock

# Coverage directory used by tools like istanbul
coverage/
*.lcov

# nyc test coverage
.nyc_output

# Dependency directories
jspm_packages/

# Optional npm cache directory
.npm

# Optional eslint cache
.eslintcache

# Microbundle cache
.rpt2_cache/
.rts2_cache_cjs/
.rts2_cache_es/
.rts2_cache_umd/

# Optional REPL history
.node_repl_history

# Output of 'npm pack'
*.tgz

# Yarn Integrity file
.yarn-integrity

# dotenv environment variables file
.env.backup

# Stores VSCode versions used for testing VSCode extensions
.vscode-test

# Temporary folders
tmp/
temp/

# Documentation folder (personal use only)
documentation/

# Journal and reference files (personal use only)
journal.md
*_REFERENCE_GUIDE.md
*_PROJECT_GUIDE.md
*_JOURNAL.md

# Backup files
*.bak
*.backup
*~

# Certificate files
*.pem
*.key
*.crt
*.cert

# Database files
*.db
*.sqlite
*.sqlite3

# Misc
.turbo
.swc
investigation/
proofread_list.txt
missing_images.txt