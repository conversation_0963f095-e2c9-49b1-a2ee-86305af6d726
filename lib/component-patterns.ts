/**
 * Standardized Component Architecture Patterns
 * 
 * This file defines the standard patterns and interfaces that all components
 * in the application should follow for consistency and maintainability.
 */

import { ReactNode } from 'react';

// Base component props that all components should extend
export interface BaseComponentProps {
  className?: string;
  children?: ReactNode;
  'data-testid'?: string;
}

// Loading state interface for components that handle async operations
export interface LoadingState {
  isLoading: boolean;
  loadingMessage?: string;
}

// Error state interface for components that can error
export interface ErrorState {
  error?: Error | string | null;
  onRetry?: () => void;
}

// Standard component state that combines loading and error states
export interface ComponentState extends LoadingState, ErrorState {
  isInitialized: boolean;
}

// Props for components that handle data fetching
export interface DataComponentProps<T = any> extends BaseComponentProps {
  data?: T;
  loading?: boolean;
  error?: Error | string | null;
  onRefresh?: () => void;
  emptyStateMessage?: string;
  loadingComponent?: ReactNode;
  errorComponent?: ReactNode;
}

// Props for form components
export interface FormComponentProps extends BaseComponentProps {
  onSubmit?: (data: any) => void | Promise<void>;
  onCancel?: () => void;
  initialData?: any;
  isSubmitting?: boolean;
  submitText?: string;
  cancelText?: string;
  showCancel?: boolean;
}

// Props for modal/dialog components
export interface ModalComponentProps extends BaseComponentProps {
  isOpen: boolean;
  onClose: () => void;
  title?: string;
  size?: 'sm' | 'md' | 'lg' | 'xl' | 'full';
  closeOnOverlayClick?: boolean;
  closeOnEscape?: boolean;
}

// Props for list/grid components
export interface ListComponentProps<T = any> extends BaseComponentProps {
  items: T[];
  renderItem: (item: T, index: number) => ReactNode;
  keyExtractor?: (item: T, index: number) => string;
  emptyMessage?: string;
  loading?: boolean;
  error?: Error | string | null;
  onRefresh?: () => void;
}

// Props for card components
export interface CardComponentProps extends BaseComponentProps {
  title?: string;
  subtitle?: string;
  image?: string;
  imageAlt?: string;
  actions?: ReactNode;
  variant?: 'default' | 'elevated' | 'outlined' | 'filled';
  size?: 'sm' | 'md' | 'lg';
}

// Props for button components
export interface ButtonComponentProps extends BaseComponentProps {
  variant?: 'primary' | 'secondary' | 'outline' | 'ghost' | 'danger';
  size?: 'sm' | 'md' | 'lg' | 'xl';
  loading?: boolean;
  disabled?: boolean;
  icon?: ReactNode;
  iconPosition?: 'left' | 'right';
  onClick?: () => void | Promise<void>;
  type?: 'button' | 'submit' | 'reset';
}

// Props for input components
export interface InputComponentProps extends BaseComponentProps {
  label?: string;
  placeholder?: string;
  value?: string;
  onChange?: (value: string) => void;
  onBlur?: () => void;
  onFocus?: () => void;
  error?: string;
  disabled?: boolean;
  required?: boolean;
  type?: 'text' | 'email' | 'password' | 'tel' | 'url' | 'search' | 'number';
  autoComplete?: string;
  maxLength?: number;
}

// Standard animation variants for consistent motion
export const standardAnimations = {
  fadeIn: {
    initial: { opacity: 0 },
    animate: { opacity: 1 },
    exit: { opacity: 0 },
  },
  slideUp: {
    initial: { opacity: 0, y: 20 },
    animate: { opacity: 1, y: 0 },
    exit: { opacity: 0, y: -20 },
  },
  slideDown: {
    initial: { opacity: 0, y: -20 },
    animate: { opacity: 1, y: 0 },
    exit: { opacity: 0, y: 20 },
  },
  slideLeft: {
    initial: { opacity: 0, x: 20 },
    animate: { opacity: 1, x: 0 },
    exit: { opacity: 0, x: -20 },
  },
  slideRight: {
    initial: { opacity: 0, x: -20 },
    animate: { opacity: 1, x: 0 },
    exit: { opacity: 0, x: 20 },
  },
  scale: {
    initial: { opacity: 0, scale: 0.9 },
    animate: { opacity: 1, scale: 1 },
    exit: { opacity: 0, scale: 0.9 },
  },
  stagger: {
    animate: {
      transition: {
        staggerChildren: 0.1,
      },
    },
  },
} as const;

// Standard transition durations
export const transitionDurations = {
  fast: 0.15,
  normal: 0.3,
  slow: 0.5,
} as const;

// Standard spacing values
export const spacing = {
  xs: '0.25rem',
  sm: '0.5rem',
  md: '1rem',
  lg: '1.5rem',
  xl: '2rem',
  '2xl': '3rem',
  '3xl': '4rem',
} as const;

// Standard border radius values
export const borderRadius = {
  none: '0',
  sm: '0.25rem',
  md: '0.375rem',
  lg: '0.5rem',
  xl: '0.75rem',
  '2xl': '1rem',
  full: '9999px',
} as const;

// Standard shadow values
export const shadows = {
  sm: '0 1px 2px 0 rgb(0 0 0 / 0.05)',
  md: '0 4px 6px -1px rgb(0 0 0 / 0.1), 0 2px 4px -2px rgb(0 0 0 / 0.1)',
  lg: '0 10px 15px -3px rgb(0 0 0 / 0.1), 0 4px 6px -4px rgb(0 0 0 / 0.1)',
  xl: '0 20px 25px -5px rgb(0 0 0 / 0.1), 0 8px 10px -6px rgb(0 0 0 / 0.1)',
} as const;

// Utility function to create consistent class names
export const createClassName = (...classes: (string | undefined | null | false)[]): string => {
  return classes.filter(Boolean).join(' ');
};

// Utility function to create consistent data attributes
export const createDataAttributes = (testId?: string, additionalData?: Record<string, string>) => {
  const attributes: Record<string, string> = {};
  
  if (testId) {
    attributes['data-testid'] = testId;
  }
  
  if (additionalData) {
    Object.entries(additionalData).forEach(([key, value]) => {
      attributes[`data-${key}`] = value;
    });
  }
  
  return attributes;
};

// Standard error messages
export const standardErrorMessages = {
  network: 'Unable to connect to the server. Please check your internet connection.',
  unauthorized: 'You are not authorized to perform this action.',
  notFound: 'The requested resource was not found.',
  validation: 'Please check your input and try again.',
  generic: 'Something went wrong. Please try again.',
} as const;

// Standard success messages
export const standardSuccessMessages = {
  created: 'Successfully created!',
  updated: 'Successfully updated!',
  deleted: 'Successfully deleted!',
  saved: 'Successfully saved!',
} as const;
