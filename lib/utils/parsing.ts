/**
 * Utility functions for safe parsing operations
 */

/**
 * Safely parse JSON with error handling and input validation
 * @param jsonString - The JSON string to parse
 * @param fallback - Fallback value if parsing fails
 * @returns Parsed object or fallback value
 */
export function safeJsonParse<T = any>(jsonString: string, fallback: T | null = null): T | null {
  // Input validation for type safety
  if (typeof jsonString !== 'string') {
    console.warn('safeJsonParse: Input is not a string, returning fallback');
    return fallback;
  }

  // Check for empty or whitespace-only strings
  if (!jsonString.trim()) {
    console.warn('safeJsonParse: Input is empty or whitespace-only, returning fallback');
    return fallback;
  }

  try {
    const parsed = JSON.parse(jsonString);

    // Additional validation to ensure we got a valid result
    if (parsed === undefined) {
      console.warn('safeJsonParse: Parsed result is undefined, returning fallback');
      return fallback;
    }

    return parsed;
  } catch (error) {
    console.warn('safeJsonParse: JSON parsing failed:', error instanceof Error ? error.message : 'Unknown error');
    return fallback;
  }
}

/**
 * Extract error information from API error messages safely
 * @param error - The error object
 * @returns Structured error information
 */
export function extractApiErrorInfo(error: unknown): {
  message: string;
  field?: string;
  missingFields?: string[];
  detailedError?: string;
} {
  let errorMessage = 'An unexpected error occurred';
  let detailedError: string | undefined;

  if (error instanceof Error) {
    errorMessage = error.message;

    // Try to parse as JSON for structured error information
    const parsedError = safeJsonParse(error.message);
    if (parsedError && typeof parsedError === 'object') {
      if (parsedError.error) {
        errorMessage = parsedError.error;
      }

      // Build detailed error information without overwriting
      const detailParts: string[] = [];

      if (parsedError.field) {
        detailParts.push(`Field: ${parsedError.field}`);
      }

      if (parsedError.missingFields && Array.isArray(parsedError.missingFields)) {
        detailParts.push(`Missing fields: ${parsedError.missingFields.join(', ')}`);
      }

      // Combine all detail parts
      detailedError = detailParts.length > 0 ? detailParts.join('; ') : undefined;

      return {
        message: errorMessage,
        field: parsedError.field,
        missingFields: parsedError.missingFields,
        detailedError
      };
    }
  }

  return { message: errorMessage, detailedError };
}

/**
 * Extract Cloudinary public ID from URL with robust error handling
 * @param url - Cloudinary URL
 * @returns Public ID or null if extraction fails
 */
export function extractCloudinaryPublicId(url: string): string | null {
  if (!url || typeof url !== 'string' || !url.includes('cloudinary.com')) {
    return null;
  }

  try {
    const urlParts = url.split('/');
    const uploadIndex = urlParts.findIndex(part => part === 'upload');
    
    if (uploadIndex === -1 || uploadIndex + 2 >= urlParts.length) {
      return null;
    }

    // Get everything after /upload/version/ (skip version if present)
    const pathParts = urlParts.slice(uploadIndex + 2);
    
    // Remove version if it's a version string (starts with 'v' followed by numbers)
    if (pathParts.length > 0 && /^v\d+$/.test(pathParts[0])) {
      pathParts.shift();
    }

    if (pathParts.length === 0) {
      return null;
    }

    // Get filename without extension
    const fileName = pathParts[pathParts.length - 1];
    const fileNameWithoutExt = fileName.split('.')[0];
    
    if (!fileNameWithoutExt) {
      return null;
    }

    // Reconstruct full public ID with folder path
    const folderPath = pathParts.slice(0, -1).join('/');
    return folderPath ? `${folderPath}/${fileNameWithoutExt}` : fileNameWithoutExt;
  } catch {
    return null;
  }
}

/**
 * Validate and sanitize toast notification messages
 * @param message - The message to sanitize
 * @returns Sanitized message without newlines and control characters
 */
export function sanitizeToastMessage(message: string): string {
  if (!message || typeof message !== 'string') {
    return 'An error occurred';
  }

  return message
    .replace(/[\r\n\t]/g, ' ') // Replace newlines and tabs with spaces
    .replace(/\s+/g, ' ') // Replace multiple spaces with single space
    .trim();
}

/**
 * Check if a string is a valid URL
 * @param url - String to validate
 * @returns True if valid URL, false otherwise
 */
export function isValidUrl(url: string): boolean {
  if (!url || typeof url !== 'string') {
    return false;
  }

  try {
    new URL(url);
    return true;
  } catch {
    return false;
  }
}

/**
 * Extract domain from URL safely
 * @param url - URL to extract domain from
 * @returns Domain or null if extraction fails
 */
export function extractDomain(url: string): string | null {
  if (!isValidUrl(url)) {
    return null;
  }

  try {
    return new URL(url).hostname;
  } catch {
    return null;
  }
}
