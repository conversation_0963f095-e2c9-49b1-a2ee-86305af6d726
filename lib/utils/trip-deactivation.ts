export interface DeactivationJobStatus {
  job_id: number;
  job_name: string;
  schedule: string;
  schedule_natural?: string;
  command: string;
  active: boolean;
  last_run_started_at: string | null;
  last_run_status: string;
}

export interface DeactivationResponse {
  success: boolean;
  error?: string;
  message: string;
  [key: string]: any;
}

// Consolidated fetch error handling utility
async function handleFetchResponse<T>(response: Response, defaultReturn: T): Promise<T | { error: string }> {
  if (!response.ok) {
    const errorData = await response.json().catch(() => ({}));
    return { ...defaultReturn, error: errorData.error || `HTTP ${response.status}` } as T & { error: string };
  }
  return response.json();
}

/**
 * Manually trigger deactivation of expired trips
 */
export async function triggerTripDeactivation(): Promise<{ count: number; error?: string }> {
  try {
    const response = await fetch('/api/admin/auto-deactivation/trigger', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
    });

    const result = await handleFetchResponse(response, { count: 0 });
    if ('error' in result) {
      return { count: 0, error: result.error };
    }
    return { count: result.count || 0 };
  } catch (error) {
    console.error('Error triggering trip deactivation:', error);
    return { count: 0, error: 'Failed to trigger trip deactivation' };
  }
}

/**
 * Update the auto deactivation date for a specific trip
 */
export async function updateTripDeactivationDate(
  tripId: string,
  newDate: string | null
): Promise<DeactivationResponse> {
  try {
    const response = await fetch(`/api/admin/trips/${tripId}`, {
      method: 'PATCH',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        auto_deactivation_date: newDate
      }),
    });

    if (!response.ok) {
      const errorData = await response.json().catch(() => ({}));
      return {
        success: false,
        error: errorData.error || `HTTP ${response.status}`,
        message: 'Failed to update auto-deactivation date'
      };
    }

    return {
      success: true,
      message: newDate ? 'Auto-deactivation date updated successfully' : 'Auto-deactivation date cleared successfully'
    };
  } catch (error) {
    console.error('Error updating trip deactivation date:', error);
    return {
      success: false,
      error: 'Unexpected error occurred',
      message: 'Failed to update auto-deactivation date'
    };
  }
}

/**
 * Get the status of the deactivation cron job and trips with auto deactivation
 */
export async function getDeactivationJobStatus(): Promise<{
  jobs: DeactivationJobStatus[];
  trips: Array<{
    id: string;
    title: string;
    auto_deactivation_date: string;
    is_active: boolean;
  }>;
  error?: string
}> {
  try {
    const response = await fetch('/api/admin/auto-deactivation/status');

    if (!response.ok) {
      const errorData = await response.json().catch(() => ({}));
      return { jobs: [], trips: [], error: errorData.error || `HTTP ${response.status}` };
    }

    const data = await response.json();
    return {
      jobs: data.jobs || [],
      trips: data.trips || []
    };
  } catch (error) {
    console.error('Error getting deactivation job status:', error);
    return { jobs: [], trips: [], error: 'Failed to get job status' };
  }
}




