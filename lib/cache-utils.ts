/**
 * Browser cache utilities for PWA and service worker management
 * Note: Data caching is now handled by React Query
 */

/**
 * Clear browser caches programmatically
 */
export async function clearBrowserCaches(): Promise<void> {
  if ('caches' in window) {
    try {
      const cacheNames = await caches.keys();
      await Promise.all(
        cacheNames.map(cacheName => caches.delete(cacheName))
      );
      console.log('Browser caches cleared');
    } catch (error) {
      console.error('Error clearing browser caches:', error);
    }
  }
}

/**
 * Force reload of service worker
 */
export async function updateServiceWorker(): Promise<void> {
  if ('serviceWorker' in navigator) {
    try {
      const registration = await navigator.serviceWorker.getRegistration();
      if (registration) {
        await registration.update();
        console.log('Service worker updated');
      }
    } catch (error) {
      console.error('Error updating service worker:', error);
    }
  }
}

/**
 * Add cache-busting meta tags to prevent page caching
 */
export function addNoCacheMetaTags(): void {
  if (typeof document !== 'undefined') {
    // Remove existing cache meta tags
    const existingTags = document.querySelectorAll('meta[http-equiv="Cache-Control"], meta[http-equiv="Pragma"], meta[http-equiv="Expires"]');
    existingTags.forEach(tag => tag.remove());
    
    // Add new no-cache meta tags
    const metaTags = [
      { httpEquiv: 'Cache-Control', content: 'no-cache, no-store, must-revalidate' },
      { httpEquiv: 'Pragma', content: 'no-cache' },
      { httpEquiv: 'Expires', content: '0' }
    ];
    
    metaTags.forEach(({ httpEquiv, content }) => {
      const meta = document.createElement('meta');
      meta.httpEquiv = httpEquiv;
      meta.content = content;
      document.head.appendChild(meta);
    });
  }
}

/**
 * Hook to use in React components for browser cache management
 * Note: Data caching is now handled by React Query
 */
export function useCacheBuster() {
  const timestamp = Date.now();

  return {
    clearBrowserCaches,
    updateServiceWorker,
    timestamp
  };
}
