/**
 * Input sanitization and validation utilities for security
 */

/**
 * Sanitize search query to prevent SQL injection
 * @param query - Raw search query
 * @param maxLength - Maximum allowed length (default: 100)
 * @returns Sanitized query string
 */
export function sanitizeSearchQuery(query: string, maxLength: number = 100): string {
  if (!query || typeof query !== 'string') {
    return '';
  }

  return query
    // Remove null bytes and control characters
    // eslint-disable-next-line no-control-regex
    .replace(/[\x00-\x1F\x7F]/g, '')
    // Escape SQL wildcards to prevent injection
    .replace(/[%_\\]/g, '\\$&')
    // Remove potentially dangerous characters
    .replace(/[<>'"`;]/g, '')
    // Normalize whitespace
    .replace(/\s+/g, ' ')
    // Trim and limit length
    .trim()
    .substring(0, maxLength);
}

/**
 * Validate and sanitize UUID
 * @param uuid - UUID string to validate
 * @returns Sanitized UUID or null if invalid
 */
export function sanitizeUUID(uuid: string): string | null {
  if (!uuid || typeof uuid !== 'string') {
    return null;
  }

  // UUID v4 regex pattern
  const uuidPattern = /^[0-9a-f]{8}-[0-9a-f]{4}-4[0-9a-f]{3}-[89ab][0-9a-f]{3}-[0-9a-f]{12}$/i;
  
  const cleanUuid = uuid.trim().toLowerCase();
  return uuidPattern.test(cleanUuid) ? cleanUuid : null;
}

/**
 * Sanitize email address
 * @param email - Email address to sanitize
 * @returns Sanitized email or null if invalid
 */
export function sanitizeEmail(email: string): string | null {
  if (!email || typeof email !== 'string') {
    return null;
  }

  const cleanEmail = email.trim().toLowerCase();
  
  // Basic email validation regex
  const emailPattern = /^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$/;
  
  return emailPattern.test(cleanEmail) && cleanEmail.length <= 254 ? cleanEmail : null;
}

/**
 * Sanitize numeric input
 * @param value - Value to sanitize
 * @param min - Minimum allowed value
 * @param max - Maximum allowed value
 * @returns Sanitized number or null if invalid
 */
export function sanitizeNumber(value: any, min?: number, max?: number): number | null {
  const num = typeof value === 'string' ? parseInt(value, 10) : Number(value);
  
  if (isNaN(num) || !isFinite(num)) {
    return null;
  }

  if (min !== undefined && num < min) {
    return min;
  }

  if (max !== undefined && num > max) {
    return max;
  }

  return num;
}

/**
 * Sanitize string input for general use
 * @param input - Input string
 * @param maxLength - Maximum allowed length
 * @param allowHtml - Whether to allow HTML tags
 * @returns Sanitized string
 */
export function sanitizeString(input: string, maxLength: number = 1000, allowHtml: boolean = false): string {
  if (!input || typeof input !== 'string') {
    return '';
  }

  let sanitized = input
    // Remove null bytes and control characters
    // eslint-disable-next-line no-control-regex
    .replace(/[\x00-\x1F\x7F]/g, '')
    // Normalize whitespace
    .replace(/\s+/g, ' ')
    .trim();

  // Remove HTML tags if not allowed
  if (!allowHtml) {
    sanitized = sanitized.replace(/<[^>]*>/g, '');
  }

  return sanitized.substring(0, maxLength);
}

/**
 * Sanitize filename for safe storage
 * @param filename - Original filename
 * @returns Safe filename
 */
export function sanitizeFilename(filename: string): string {
  if (!filename || typeof filename !== 'string') {
    return 'unnamed_file';
  }

  return filename
    // Remove path separators and dangerous characters
    .replace(/[/\\:*?"<>|]/g, '_')
    // Remove control characters
    // eslint-disable-next-line no-control-regex
    .replace(/[\x00-\x1F\x7F]/g, '')
    // Limit length
    .substring(0, 255)
    .trim()
    || 'unnamed_file';
}

/**
 * Create safe search conditions for Supabase queries
 * @param searchQuery - Raw search query
 * @param fields - Fields to search in
 * @returns Safe search condition string
 */
export function createSafeSearchCondition(searchQuery: string, fields: string[]): string {
  const sanitizedQuery = sanitizeSearchQuery(searchQuery);
  
  if (!sanitizedQuery || fields.length === 0) {
    return '';
  }

  // Create safe search conditions using escaped query
  return fields
    .map(field => `${field}.ilike.%${sanitizedQuery}%`)
    .join(',');
}

/**
 * Validate and sanitize pagination parameters
 * @param page - Page number
 * @param limit - Items per page
 * @param maxLimit - Maximum allowed limit
 * @returns Sanitized pagination parameters
 */
export function sanitizePagination(page: any, limit: any, maxLimit: number = 100): { page: number; limit: number; offset: number } {
  const sanitizedPage = Math.max(1, sanitizeNumber(page, 1) || 1);
  const sanitizedLimit = Math.max(1, Math.min(maxLimit, sanitizeNumber(limit, 1) || 10));
  const offset = (sanitizedPage - 1) * sanitizedLimit;

  return {
    page: sanitizedPage,
    limit: sanitizedLimit,
    offset
  };
}

/**
 * Sanitize sort parameters
 * @param sortField - Field to sort by
 * @param sortDirection - Sort direction
 * @param allowedFields - List of allowed sort fields
 * @returns Sanitized sort parameters
 */
export function sanitizeSort(
  sortField: string, 
  sortDirection: string, 
  allowedFields: string[]
): { field: string; direction: 'asc' | 'desc' } {
  const field = allowedFields.includes(sortField) ? sortField : allowedFields[0] || 'created_at';
  const direction = sortDirection?.toLowerCase() === 'desc' ? 'desc' : 'asc';

  return { field, direction };
}
