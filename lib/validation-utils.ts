/**
 * Enhanced validation utilities for form inputs
 * Provides real-time, field-level validation with detailed error messages
 */

export interface ValidationResult {
  isValid: boolean;
  error?: string;
}

/**
 * Validate phone number - exactly 10 digits, no symbols
 */
export function validatePhoneNumber(phone: string): ValidationResult {
  if (!phone || typeof phone !== 'string') {
    return { isValid: false, error: 'Phone number is required' };
  }

  const cleanPhone = phone.trim();
  
  if (cleanPhone.length === 0) {
    return { isValid: false, error: 'Phone number is required' };
  }

  // Check if contains only digits
  if (!/^[0-9]+$/.test(cleanPhone)) {
    return { isValid: false, error: 'Phone number can only contain digits (no symbols or spaces)' };
  }

  // Check exact length
  if (cleanPhone.length !== 10) {
    if (cleanPhone.length < 10) {
      return { isValid: false, error: `Phone number must be exactly 10 digits (${cleanPhone.length} entered)` };
    } else {
      return { isValid: false, error: `Phone number must be exactly 10 digits (${cleanPhone.length} entered)` };
    }
  }

  // Check if starts with valid digit (not 0 or 1)
  if (cleanPhone.startsWith('0') || cleanPhone.startsWith('1')) {
    return { isValid: false, error: 'Phone number cannot start with 0 or 1' };
  }

  return { isValid: true };
}

/**
 * Validate email address with proper domain checking
 */
export function validateEmailAddress(email: string): ValidationResult {
  if (!email || typeof email !== 'string') {
    return { isValid: false, error: 'Email address is required' };
  }

  const cleanEmail = email.trim().toLowerCase();
  
  if (cleanEmail.length === 0) {
    return { isValid: false, error: 'Email address is required' };
  }

  // Check basic format
  if (!cleanEmail.includes('@')) {
    return { isValid: false, error: 'Email must contain @ symbol' };
  }

  // Split email into parts
  const parts = cleanEmail.split('@');
  if (parts.length !== 2) {
    return { isValid: false, error: 'Email format is invalid' };
  }

  const [localPart, domainPart] = parts;

  // Validate local part (before @)
  if (localPart.length === 0) {
    return { isValid: false, error: 'Email must have text before @ symbol' };
  }

  if (localPart.length > 64) {
    return { isValid: false, error: 'Email local part is too long' };
  }

  // Validate domain part (after @)
  if (domainPart.length === 0) {
    return { isValid: false, error: 'Email must have domain after @ symbol' };
  }

  if (!domainPart.includes('.')) {
    return { isValid: false, error: 'Email domain must contain a dot (e.g., example.com)' };
  }

  // Check domain format
  const domainParts = domainPart.split('.');
  if (domainParts.length < 2) {
    return { isValid: false, error: 'Email domain format is invalid' };
  }

  // Check if domain parts are valid
  for (const part of domainParts) {
    if (part.length === 0) {
      return { isValid: false, error: 'Email domain format is invalid' };
    }
    if (!/^[a-zA-Z0-9-]+$/.test(part)) {
      return { isValid: false, error: 'Email domain contains invalid characters' };
    }
  }

  // Check top-level domain
  const tld = domainParts[domainParts.length - 1];
  if (tld.length < 2) {
    return { isValid: false, error: 'Email domain extension is too short' };
  }

  if (!/^[a-zA-Z]+$/.test(tld)) {
    return { isValid: false, error: 'Email domain extension can only contain letters' };
  }

  // Final regex check for comprehensive validation
  const emailRegex = /^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$/;
  if (!emailRegex.test(cleanEmail)) {
    return { isValid: false, error: 'Email format is invalid' };
  }

  return { isValid: true };
}

/**
 * Validate name field
 */
export function validateName(name: string): ValidationResult {
  if (!name || typeof name !== 'string') {
    return { isValid: false, error: 'Name is required' };
  }

  const cleanName = name.trim();
  
  if (cleanName.length === 0) {
    return { isValid: false, error: 'Name is required' };
  }

  if (cleanName.length < 2) {
    return { isValid: false, error: 'Name must be at least 2 characters long' };
  }

  if (cleanName.length > 100) {
    return { isValid: false, error: 'Name must be less than 100 characters' };
  }

  // Check if contains only letters, spaces, and common international characters
  if (!/^[a-zA-Z\s\u00C0-\u017F\u0100-\u024F\u1E00-\u1EFF\u0400-\u04FF\u0370-\u03FF\u0590-\u05FF\u0600-\u06FF\u4E00-\u9FFF\u3040-\u309F\u30A0-\u30FF]+$/.test(cleanName)) {
    return { isValid: false, error: 'Name can only contain letters, spaces, and international characters' };
  }

  return { isValid: true };
}

/**
 * Validate message field
 */
export function validateMessage(message: string, minLength: number = 10): ValidationResult {
  if (!message || typeof message !== 'string') {
    return { isValid: false, error: 'Message is required' };
  }

  const cleanMessage = message.trim();
  
  if (cleanMessage.length === 0) {
    return { isValid: false, error: 'Message is required' };
  }

  if (cleanMessage.length < minLength) {
    return { isValid: false, error: `Message must be at least ${minLength} characters long (${cleanMessage.length} entered)` };
  }

  if (cleanMessage.length > 2000) {
    return { isValid: false, error: `Message must be less than 2000 characters (${cleanMessage.length} entered)` };
  }

  return { isValid: true };
}

/**
 * Validate subject field
 */
export function validateSubject(subject: string): ValidationResult {
  if (!subject || typeof subject !== 'string') {
    return { isValid: false, error: 'Subject is required' };
  }

  const cleanSubject = subject.trim();
  
  if (cleanSubject.length === 0) {
    return { isValid: false, error: 'Subject is required' };
  }

  if (cleanSubject.length < 5) {
    return { isValid: false, error: `Subject must be at least 5 characters long (${cleanSubject.length} entered)` };
  }

  if (cleanSubject.length > 200) {
    return { isValid: false, error: `Subject must be less than 200 characters (${cleanSubject.length} entered)` };
  }

  return { isValid: true };
}

/**
 * Format phone number for display (add spaces for readability)
 */
export function formatPhoneForDisplay(phone: string): string {
  if (!phone || phone.length !== 10) {
    return phone;
  }
  
  // Format as XXX XXX XXXX
  return `${phone.slice(0, 3)} ${phone.slice(3, 6)} ${phone.slice(6)}`;
}

/**
 * Clean phone number input (remove all non-digits)
 */
export function cleanPhoneInput(phone: string): string {
  if (!phone || typeof phone !== 'string') {
    return '';
  }
  
  return phone.replace(/\D/g, '');
}

/**
 * Real-time phone input handler
 */
export function handlePhoneInput(value: string): string {
  const cleaned = cleanPhoneInput(value);
  // Limit to 10 digits
  return cleaned.slice(0, 10);
}
