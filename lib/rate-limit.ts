import { NextRequest } from 'next/server';
import { RateLimitError } from './error-handler';

/**
 * Simple in-memory rate limiting for serverless environments
 * For production, consider using Redis or a dedicated rate limiting service
 */

interface RateLimitConfig {
  windowMs: number; // Time window in milliseconds
  max: number; // Maximum number of requests per window
  message?: string; // Custom error message
  keyGenerator?: (request: NextRequest, identifier?: string) => string; // Custom key generator
}

interface RateLimitEntry {
  count: number;
  resetTime: number;
}

// In-memory store for rate limiting
// Note: This will reset on server restart, which is fine for serverless
const rateLimitStore = new Map<string, RateLimitEntry>();

/**
 * Clean up expired entries from the rate limit store
 */
function cleanupExpiredEntries() {
  const now = Date.now();
  const keysToDelete: string[] = [];

  rateLimitStore.forEach((entry, key) => {
    if (now > entry.resetTime) {
      keysToDelete.push(key);
    }
  });

  keysToDelete.forEach(key => {
    rateLimitStore.delete(key);
  });
}

/**
 * Generate a key for rate limiting based on IP address and optional identifier
 */
function generateKey(request: NextRequest, identifier?: string): string {
  const forwarded = request.headers.get('x-forwarded-for');
  const ip = forwarded ? forwarded.split(',')[0].trim() :
    request.headers.get('x-real-ip') ||
    request.headers.get('cf-connecting-ip') ||
    'unknown';

  return identifier ? `${ip}:${identifier}` : ip;
}

/**
 * Rate limiting middleware
 */
export function rateLimit(config: RateLimitConfig) {
  const {
    windowMs,
    max,
    message = 'Too many requests, please try again later.',
    keyGenerator = generateKey
  } = config;

  return async (request: NextRequest, identifier?: string) => {
    // Clean up expired entries periodically
    if (Math.random() < 0.01) { // 1% chance to clean up
      cleanupExpiredEntries();
    }

    const key = keyGenerator(request, identifier);
    const now = Date.now();
    const resetTime = now + windowMs;

    let entry = rateLimitStore.get(key);

    if (!entry || now > entry.resetTime) {
      // Create new entry or reset expired entry
      entry = {
        count: 1,
        resetTime
      };
      rateLimitStore.set(key, entry);
      return; // Allow request
    }

    if (entry.count >= max) {
      // Rate limit exceeded
      throw new RateLimitError(message);
    }

    // Increment count
    entry.count++;
    rateLimitStore.set(key, entry);
  };
}

/**
 * Pre-configured rate limiters for different endpoints
 */
export const authRateLimit = rateLimit({
  windowMs: 15 * 60 * 1000, // 15 minutes
  max: 5, // 5 attempts per window
  message: 'Too many authentication attempts, please try again later.',
});

export const apiRateLimit = rateLimit({
  windowMs: 15 * 60 * 1000, // 15 minutes
  max: 100, // 100 requests per window
  message: 'Too many requests, please try again later.',
});

// Admin upload rate limit - Very generous for batch operations
export const adminUploadRateLimit = rateLimit({
  windowMs: 60 * 60 * 1000, // 1 hour
  max: 1000, // 1000 uploads per hour for admins (batch gallery uploads)
  message: 'Admin upload limit reached, please try again later.',
});

// Public upload rate limit - More restrictive for public endpoints
export const publicUploadRateLimit = rateLimit({
  windowMs: 60 * 60 * 1000, // 1 hour
  max: 50, // 50 uploads per hour for public users
  message: 'Too many upload attempts, please try again later.',
});

export const passwordVerifyRateLimit = rateLimit({
  windowMs: 15 * 60 * 1000, // 15 minutes
  max: 10, // 10 attempts per window
  message: 'Too many password attempts, please try again later.',
});

/**
 * Rate limit wrapper for API routes
 */
export function withRateLimit<T extends any[]>(
  rateLimiter: (request: NextRequest, identifier?: string) => Promise<void>,
  handler: (request: NextRequest, ...args: T) => Promise<Response>,
  config?: RateLimitConfig
) {
  return async (request: NextRequest, ...args: T): Promise<Response> => {
    try {
      await rateLimiter(request);
      return await handler(request, ...args);
    } catch (error) {
      if (error instanceof RateLimitError) {
        const retryAfterSeconds = config ? Math.ceil(config.windowMs / 1000) : 900; // Default 15 minutes
        return new Response(
          JSON.stringify({
            success: false,
            error: {
              message: error.message,
              code: error.code,
              timestamp: error.timestamp,
            },
          }),
          {
            status: error.statusCode,
            headers: {
              'Content-Type': 'application/json',
              'Retry-After': retryAfterSeconds.toString(),
            },
          }
        );
      }
      throw error; // Re-throw other errors
    }
  };
}

/**
 * Get rate limit status for a key
 */
export function getRateLimitStatus(request: NextRequest, config: RateLimitConfig, identifier?: string) {
  const key = generateKey(request, identifier);
  const entry = rateLimitStore.get(key);
  const now = Date.now();

  if (!entry || now > entry.resetTime) {
    return {
      limit: config.max,
      remaining: config.max,
      reset: now + config.windowMs,
      retryAfter: null,
    };
  }

  const remaining = Math.max(0, config.max - entry.count);
  const retryAfter = remaining === 0 ? Math.ceil((entry.resetTime - now) / 1000) : null;

  return {
    limit: config.max,
    remaining,
    reset: entry.resetTime,
    retryAfter,
  };
}

/**
 * Add rate limit headers to response
 */
export function addRateLimitHeaders(
  response: Response,
  request: NextRequest,
  config: RateLimitConfig,
  identifier?: string
): Response {
  const status = getRateLimitStatus(request, config, identifier);
  
  const headers = new Headers(response.headers);
  headers.set('X-RateLimit-Limit', status.limit.toString());
  headers.set('X-RateLimit-Remaining', status.remaining.toString());
  headers.set('X-RateLimit-Reset', status.reset.toString());
  
  if (status.retryAfter) {
    headers.set('Retry-After', status.retryAfter.toString());
  }

  return new Response(response.body, {
    status: response.status,
    statusText: response.statusText,
    headers,
  });
}

/**
 * Enhanced rate limiter with different limits for different user types
 */
export function createTieredRateLimit(configs: {
  default: RateLimitConfig;
  authenticated?: RateLimitConfig;
  admin?: RateLimitConfig;
}) {
  const defaultLimiter = rateLimit(configs.default);
  const authenticatedLimiter = configs.authenticated ? rateLimit(configs.authenticated) : null;
  const adminLimiter = configs.admin ? rateLimit(configs.admin) : null;

  return async (request: NextRequest, userType: 'default' | 'authenticated' | 'admin' = 'default') => {
    switch (userType) {
      case 'admin':
        if (adminLimiter) {
          await adminLimiter(request, 'admin');
          return;
        }
        // Fall through to authenticated if no admin limiter
      case 'authenticated':
        if (authenticatedLimiter) {
          await authenticatedLimiter(request, 'auth');
          return;
        }
        // Fall through to default if no authenticated limiter
      default:
        await defaultLimiter(request);
    }
  };
}

/**
 * Rate limit for specific album access
 */
export const albumAccessRateLimit = rateLimit({
  windowMs: 5 * 60 * 1000, // 5 minutes
  max: 20, // 20 album access attempts per 5 minutes
  message: 'Too many album access attempts, please try again later.',
  keyGenerator: (request: NextRequest, albumId?: string) => {
    const baseKey = generateKey(request);
    return albumId ? `${baseKey}:album:${albumId}` : baseKey;
  },
});

/**
 * Create admin-specific rate limiter with generous limits for batch operations
 */
export const createAdminRateLimit = (config: Partial<RateLimitConfig> = {}) => {
  return rateLimit({
    windowMs: 60 * 60 * 1000, // 1 hour default
    max: 1000, // Very generous limit for admin operations
    message: 'Admin rate limit reached, please try again later.',
    ...config,
  });
};
