/**
 * Performance monitoring and metrics collection system
 */

// Performance metrics interface
export interface PerformanceMetrics {
  timestamp: string;
  metric_type: 'api_response_time' | 'database_query' | 'page_load' | 'bundle_size' | 'web_vitals' | 'custom';
  value: number;
  unit: 'ms' | 'bytes' | 'count' | 'score';
  endpoint?: string;
  query_type?: string;
  user_id?: string;
  metadata?: Record<string, any>;
}



export class PerformanceMonitor {
  private static instance: PerformanceMonitor;
  private metrics: PerformanceMetrics[] = [];
  private timers: Map<string, number> = new Map();

  private constructor() {}

  static getInstance(): PerformanceMonitor {
    if (!PerformanceMonitor.instance) {
      PerformanceMonitor.instance = new PerformanceMonitor();
    }
    return PerformanceMonitor.instance;
  }

  /**
   * Start timing an operation
   */
  startTimer(operationId: string): void {
    this.timers.set(operationId, Date.now());
  }

  /**
   * End timing and record metric
   */
  endTimer(
    operationId: string,
    metricType: PerformanceMetrics['metric_type'],
    metadata?: Record<string, any>
  ): number {
    const startTime = this.timers.get(operationId);
    if (!startTime) {
      // No start time found - operation may have been cleared or not started
      return 0;
    }

    const duration = Date.now() - startTime;
    this.timers.delete(operationId);

    this.recordMetric({
      timestamp: new Date().toISOString(),
      metric_type: metricType,
      value: duration,
      unit: 'ms',
      metadata: {
        operation_id: operationId,
        ...metadata,
      },
    });

    return duration;
  }

  /**
   * Record a performance metric
   */
  recordMetric(metric: PerformanceMetrics): void {
    this.metrics.push(metric);

    // Keep only last 1000 metrics in memory
    if (this.metrics.length > 1000) {
      this.metrics = this.metrics.slice(-1000);
    }

    // No console logging - admin will check performance tab instead
  }



  /**
   * Get performance statistics
   */
  getStatistics(timeRange: number = 3600000): { // Default 1 hour
    total: number;
    averages: Record<string, number>;
    slowestOperations: Array<{ operation: string; duration: number; timestamp: string }>;
  } {
    const cutoffTime = Date.now() - timeRange;
    const recentMetrics = this.metrics.filter(
      m => new Date(m.timestamp).getTime() > cutoffTime
    );

    const total = recentMetrics.length;
    const averages: Record<string, number> = {};
    const slowestOperations: Array<{ operation: string; duration: number; timestamp: string }> = [];

    // Calculate averages by metric type
    const metricsByType = recentMetrics.reduce((acc, metric) => {
      if (!acc[metric.metric_type]) {
        acc[metric.metric_type] = [];
      }
      acc[metric.metric_type].push(metric.value);
      return acc;
    }, {} as Record<string, number[]>);

    Object.entries(metricsByType).forEach(([type, values]) => {
      averages[type] = values.reduce((sum, val) => sum + val, 0) / values.length;
    });

    // Find slowest operations
    const sortedMetrics = recentMetrics
      .filter(m => m.metadata?.operation_id)
      .sort((a, b) => b.value - a.value)
      .slice(0, 10);

    slowestOperations.push(...sortedMetrics.map(m => ({
      operation: m.metadata?.operation_id || 'unknown',
      duration: m.value,
      timestamp: m.timestamp,
    })));

    return {
      total,
      averages,
      slowestOperations,
    };
  }



  /**
   * Clear metrics (for testing or memory management)
   */
  clearMetrics(): void {
    this.metrics = [];
    this.timers.clear();
  }

  /**
   * Export metrics for analysis
   */
  exportMetrics(format: 'json' | 'csv' = 'json'): string {
    if (format === 'csv') {
      const headers = ['timestamp', 'metric_type', 'value', 'unit', 'endpoint', 'metadata'];
      const rows = this.metrics.map(m => [
        m.timestamp,
        m.metric_type,
        m.value.toString(),
        m.unit,
        m.endpoint || '',
        JSON.stringify(m.metadata || {}),
      ]);
      
      return [headers, ...rows].map(row => row.join(',')).join('\n');
    }
    
    return JSON.stringify(this.metrics, null, 2);
  }
}

// Singleton instance
export const performanceMonitor = PerformanceMonitor.getInstance();

// Utility functions for common operations
export const measureApiCall = async <T>(
  operationId: string,
  apiCall: () => Promise<T>,
  endpoint?: string
): Promise<T> => {
  performanceMonitor.startTimer(operationId);
  
  try {
    const result = await apiCall();
    performanceMonitor.endTimer(operationId, 'api_response_time', { endpoint });
    return result;
  } catch (error) {
    performanceMonitor.endTimer(operationId, 'api_response_time', { 
      endpoint, 
      error: true,
      error_message: error instanceof Error ? error.message : 'Unknown error'
    });
    throw error;
  }
};

export const measureDatabaseQuery = async <T>(
  operationId: string,
  queryCall: () => Promise<T>,
  queryType?: string
): Promise<T> => {
  performanceMonitor.startTimer(operationId);
  
  try {
    const result = await queryCall();
    performanceMonitor.endTimer(operationId, 'database_query', { query_type: queryType });
    return result;
  } catch (error) {
    performanceMonitor.endTimer(operationId, 'database_query', { 
      query_type: queryType,
      error: true,
      error_message: error instanceof Error ? error.message : 'Unknown error'
    });
    throw error;
  }
};

// Browser-side performance monitoring
export const measurePageLoad = (pageName: string): void => {
  if (typeof window !== 'undefined' && window.performance) {
    const navigation = performance.getEntriesByType('navigation')[0] as PerformanceNavigationTiming;
    if (navigation) {
      const loadTime = navigation.loadEventEnd - navigation.fetchStart;
      performanceMonitor.recordMetric({
        timestamp: new Date().toISOString(),
        metric_type: 'page_load',
        value: loadTime,
        unit: 'ms',
        metadata: {
          page_name: pageName,
          dom_content_loaded: navigation.domContentLoadedEventEnd - navigation.fetchStart,
          first_paint: navigation.responseEnd - navigation.fetchStart,
        },
      });
    }
  }
};

// Resource usage monitoring
export const measureBundleSize = (bundleName: string, sizeInBytes: number): void => {
  performanceMonitor.recordMetric({
    timestamp: new Date().toISOString(),
    metric_type: 'bundle_size',
    value: sizeInBytes,
    unit: 'bytes',
    metadata: {
      bundle_name: bundleName,
      size_mb: (sizeInBytes / (1024 * 1024)).toFixed(2),
    },
  });
};
