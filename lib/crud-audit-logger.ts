/**
 * CUD Operations Audit Logging System
 * Logs Create, Update, Delete operations for content management
 * Read operations are not logged to reduce audit log noise
 */

import { auditLogger, getRequestContext } from './audit-logger';
import { NextRequest } from 'next/server';

// CUD operation types (removed 'read' as we only log Create, Update, Delete operations)
export type CrudOperation = 'create' | 'update' | 'delete' | 'bulk_delete' | 'bulk_update';

// Resource types for CRUD operations
export type CrudResourceType = 
  | 'trip' 
  | 'blog_post' 
  | 'gallery' 
  | 'gallery_image'
  | 'inquiry' 
  | 'team_member' 
  | 'trip_photo_album'
  | 'admin_user'
;

// CRUD audit log entry
export interface CrudAuditEntry {
  operation: CrudOperation;
  resourceType: CrudResourceType;
  resourceId: string;
  resourceTitle?: string;
  userId: string;
  userEmail: string;
  changes?: Record<string, { old: any; new: any }>;
  metadata?: Record<string, any>;
  success: boolean;
  errorMessage?: string;
  ipAddress?: string;
  userAgent?: string;
  sessionId?: string;
  requestId?: string;
}

export class CrudAuditLogger {
  private static instance: CrudAuditLogger;

  private constructor() {}

  static getInstance(): CrudAuditLogger {
    if (!CrudAuditLogger.instance) {
      CrudAuditLogger.instance = new CrudAuditLogger();
    }
    return CrudAuditLogger.instance;
  }

  /**
   * Log a CRUD operation
   */
  async logCrudOperation(entry: CrudAuditEntry, request?: NextRequest): Promise<void> {
    try {
      const requestContext = request ? getRequestContext(request) : {};
      
      // Determine severity based on operation
      const severity = this.getSeverityForOperation(entry.operation, entry.resourceType);
      
      // Create description
      const description = this.createDescription(entry);
      
      // Log to audit system
      await auditLogger.log({
        event_type: 'admin_access',
        severity,
        user_id: entry.userId,
        user_email: entry.userEmail,
        resource_type: entry.resourceType,
        resource_id: entry.resourceId,
        action: `${entry.operation}_${entry.resourceType}`,
        description,
        success: entry.success,
        error_message: entry.errorMessage,
        metadata: {
          crud_operation: entry.operation,
          resource_title: entry.resourceTitle,
          changes: entry.changes,
          ...entry.metadata,
        },
        ...requestContext,
        // Override with provided values if available
        ip_address: entry.ipAddress || requestContext.ip_address,
        user_agent: entry.userAgent || requestContext.user_agent,
        session_id: entry.sessionId,
        request_id: entry.requestId || requestContext.request_id,
      });
    } catch (error) {
      console.error('Failed to log CRUD operation:', error);
    }
  }

  /**
   * Log a successful create operation
   */
  async logCreate(
    resourceType: CrudResourceType,
    resourceId: string,
    resourceTitle: string,
    userId: string,
    userEmail: string,
    data: Record<string, any>,
    request?: NextRequest
  ): Promise<void> {
    await this.logCrudOperation({
      operation: 'create',
      resourceType,
      resourceId,
      resourceTitle,
      userId,
      userEmail,
      success: true,
      metadata: {
        created_data: data,
        created_at: new Date().toISOString(),
      },
    }, request);
  }

  // Read operations are no longer logged to reduce audit log noise
  // Only Create, Update, Delete operations are tracked

  /**
   * Log a successful update operation
   */
  async logUpdate(
    resourceType: CrudResourceType,
    resourceId: string,
    resourceTitle: string,
    userId: string,
    userEmail: string,
    changes: Record<string, { old: any; new: any }>,
    request?: NextRequest
  ): Promise<void> {
    await this.logCrudOperation({
      operation: 'update',
      resourceType,
      resourceId,
      resourceTitle,
      userId,
      userEmail,
      changes,
      success: true,
      metadata: {
        updated_at: new Date().toISOString(),
        fields_changed: Object.keys(changes),
      },
    }, request);
  }

  /**
   * Log a successful delete operation
   */
  async logDelete(
    resourceType: CrudResourceType,
    resourceId: string,
    resourceTitle: string,
    userId: string,
    userEmail: string,
    request?: NextRequest
  ): Promise<void> {
    await this.logCrudOperation({
      operation: 'delete',
      resourceType,
      resourceId,
      resourceTitle,
      userId,
      userEmail,
      success: true,
      metadata: {
        deleted_at: new Date().toISOString(),
      },
    }, request);
  }

  /**
   * Log a failed CRUD operation
   */
  async logFailedOperation(
    operation: CrudOperation,
    resourceType: CrudResourceType,
    resourceId: string,
    userId: string,
    userEmail: string,
    errorMessage: string,
    request?: NextRequest
  ): Promise<void> {
    await this.logCrudOperation({
      operation,
      resourceType,
      resourceId,
      userId,
      userEmail,
      success: false,
      errorMessage,
      metadata: {
        failed_at: new Date().toISOString(),
      },
    }, request);
  }

  /**
   * Log bulk operations
   */
  async logBulkOperation(
    operation: 'bulk_delete' | 'bulk_update',
    resourceType: CrudResourceType,
    resourceIds: string[],
    userId: string,
    userEmail: string,
    successCount: number,
    errorCount: number,
    request?: NextRequest
  ): Promise<void> {
    await this.logCrudOperation({
      operation,
      resourceType,
      resourceId: `bulk_${resourceIds.length}_items`,
      userId,
      userEmail,
      success: errorCount === 0,
      metadata: {
        total_items: resourceIds.length,
        success_count: successCount,
        error_count: errorCount,
        resource_ids: resourceIds.slice(0, 10), // Log first 10 IDs to avoid huge logs
        bulk_operation_at: new Date().toISOString(),
      },
    }, request);
  }

  /**
   * Get severity level for operation
   */
  private getSeverityForOperation(operation: CrudOperation, resourceType: CrudResourceType): 'medium' | 'high' | 'critical' {
    // Critical operations
    if (operation === 'delete' || operation === 'bulk_delete') {
      return 'critical';
    }

    // High severity for sensitive resources (admin_user operations)
    if (resourceType === 'admin_user' && (operation === 'create' || operation === 'update' || operation === 'bulk_update')) {
      return 'high';
    }

    // Medium for all other create/update operations
    if (operation === 'create' || operation === 'update' || operation === 'bulk_update') {
      return 'medium';
    }

    // Default to medium (no more read operations)
    return 'medium';
  }

  /**
   * Calculate changes between original and updated data with deep comparison
   * This ensures only actually changed fields are logged
   */
  static calculateChanges(
    originalData: Record<string, any>,
    updatedData: Record<string, any>,
    excludeFields: string[] = ['updated_at', 'created_at']
  ): Record<string, { old: any; new: any }> {
    const changes: Record<string, { old: any; new: any }> = {};

    Object.keys(updatedData).forEach(key => {
      if (excludeFields.includes(key)) return;

      const oldValue = originalData[key];
      const newValue = updatedData[key];

      // Deep comparison for arrays and objects using JSON.stringify
      // This handles nested objects and arrays properly
      const hasChanged = JSON.stringify(oldValue) !== JSON.stringify(newValue);

      if (hasChanged) {
        changes[key] = { old: oldValue, new: newValue };
      }
    });

    return changes;
  }

  /**
   * Create human-readable description
   */
  private createDescription(entry: CrudAuditEntry): string {
    const operationText = {
      create: 'Created',
      update: 'Updated',
      delete: 'Deleted',
      bulk_delete: 'Bulk deleted',
      bulk_update: 'Bulk updated',
    }[entry.operation];

    const resourceText = entry.resourceType.replace('_', ' ');
    const titleText = entry.resourceTitle ? ` "${entry.resourceTitle}"` : '';
    const statusText = entry.success ? 'successfully' : 'with errors';

    if (entry.operation.startsWith('bulk_')) {
      const count = entry.metadata?.total_items || 'multiple';
      return `${operationText} ${count} ${resourceText} items ${statusText}`;
    }

    return `${operationText} ${resourceText}${titleText} ${statusText}`;
  }
}

// Singleton instance
export const crudAuditLogger = CrudAuditLogger.getInstance();

// Utility functions for common patterns (removed read operations)
export const logTripCrud = {
  create: (id: string, title: string, userId: string, userEmail: string, data: any, request?: NextRequest) =>
    crudAuditLogger.logCreate('trip', id, title, userId, userEmail, data, request),

  update: (id: string, title: string, userId: string, userEmail: string, changes: any, request?: NextRequest) =>
    crudAuditLogger.logUpdate('trip', id, title, userId, userEmail, changes, request),

  delete: (id: string, title: string, userId: string, userEmail: string, request?: NextRequest) =>
    crudAuditLogger.logDelete('trip', id, title, userId, userEmail, request),
};

export const logBlogCrud = {
  create: (id: string, title: string, userId: string, userEmail: string, data: any, request?: NextRequest) =>
    crudAuditLogger.logCreate('blog_post', id, title, userId, userEmail, data, request),

  update: (id: string, title: string, userId: string, userEmail: string, changes: any, request?: NextRequest) =>
    crudAuditLogger.logUpdate('blog_post', id, title, userId, userEmail, changes, request),

  delete: (id: string, title: string, userId: string, userEmail: string, request?: NextRequest) =>
    crudAuditLogger.logDelete('blog_post', id, title, userId, userEmail, request),
};

export const logGalleryCrud = {
  create: (id: string, title: string, userId: string, userEmail: string, data: any, request?: NextRequest) =>
    crudAuditLogger.logCreate('gallery', id, title, userId, userEmail, data, request),

  update: (id: string, title: string, userId: string, userEmail: string, changes: any, request?: NextRequest) =>
    crudAuditLogger.logUpdate('gallery', id, title, userId, userEmail, changes, request),

  delete: (id: string, title: string, userId: string, userEmail: string, request?: NextRequest) =>
    crudAuditLogger.logDelete('gallery', id, title, userId, userEmail, request),
};
