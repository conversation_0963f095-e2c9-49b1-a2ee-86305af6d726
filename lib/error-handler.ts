/**
 * Centralized error handling system for the application
 */

export enum ErrorCode {
  // Authentication errors
  UNAUTHORIZED = 'UNAUTHORIZED',
  FORBIDDEN = 'FORBIDDEN',
  INVALID_CREDENTIALS = 'INVALID_CREDENTIALS',
  SESSION_EXPIRED = 'SESSION_EXPIRED',
  
  // Validation errors
  VALIDATION_ERROR = 'VALIDATION_ERROR',
  INVALID_INPUT = 'INVALID_INPUT',
  MISSING_REQUIRED_FIELD = 'MISSING_REQUIRED_FIELD',
  
  // Database errors
  DATABASE_ERROR = 'DATABASE_ERROR',
  RECORD_NOT_FOUND = 'RECORD_NOT_FOUND',
  DUPLICATE_RECORD = 'DUPLICATE_RECORD',
  
  // External service errors
  GOOGLE_PHOTOS_ERROR = 'GOOGLE_PHOTOS_ERROR',
  CLOUDINARY_ERROR = 'CLOUDINARY_ERROR',
  OAUTH_ERROR = 'OAUTH_ERROR',
  
  // File upload errors
  FILE_TOO_LARGE = 'FILE_TOO_LARGE',
  INVALID_FILE_TYPE = 'INVALID_FILE_TYPE',
  UPLOAD_FAILED = 'UPLOAD_FAILED',
  
  // Rate limiting
  RATE_LIMIT_EXCEEDED = 'RATE_LIMIT_EXCEEDED',
  
  // Generic errors
  INTERNAL_SERVER_ERROR = 'INTERNAL_SERVER_ERROR',
  NETWORK_ERROR = 'NETWORK_ERROR',
  UNKNOWN_ERROR = 'UNKNOWN_ERROR',
}

export class AppError extends Error {
  public readonly code: ErrorCode;
  public readonly statusCode: number;
  public readonly isOperational: boolean;
  public readonly timestamp: string;
  public readonly context?: Record<string, any>;

  constructor(
    message: string,
    code: ErrorCode = ErrorCode.UNKNOWN_ERROR,
    statusCode: number = 500,
    isOperational: boolean = true,
    context?: Record<string, any>
  ) {
    super(message);
    
    this.name = 'AppError';
    this.code = code;
    this.statusCode = statusCode;
    this.isOperational = isOperational;
    this.timestamp = new Date().toISOString();
    this.context = context;

    // Capture stack trace
    Error.captureStackTrace(this, this.constructor);
  }
}

/**
 * Create specific error types for common scenarios
 */
export class ValidationError extends AppError {
  constructor(message: string, field?: string, value?: any) {
    super(
      message,
      ErrorCode.VALIDATION_ERROR,
      400,
      true,
      { field, value }
    );
  }
}

export class AuthenticationError extends AppError {
  constructor(message: string = 'Authentication required') {
    super(message, ErrorCode.UNAUTHORIZED, 401);
  }
}

export class AuthorizationError extends AppError {
  constructor(message: string = 'Insufficient permissions') {
    super(message, ErrorCode.FORBIDDEN, 403);
  }
}

export class NotFoundError extends AppError {
  constructor(resource: string = 'Resource') {
    super(`${resource} not found`, ErrorCode.RECORD_NOT_FOUND, 404);
  }
}

export class RateLimitError extends AppError {
  constructor(message: string = 'Rate limit exceeded') {
    super(message, ErrorCode.RATE_LIMIT_EXCEEDED, 429);
  }
}

export class ExternalServiceError extends AppError {
  constructor(service: string, originalError?: Error) {
    super(
      `External service error: ${service}`,
      ErrorCode.GOOGLE_PHOTOS_ERROR, // Default, can be overridden
      502,
      true,
      { service, originalError: originalError?.message }
    );
  }
}

/**
 * Error handler utility functions
 */
export const handleApiError = (error: unknown): AppError => {
  // If it's already an AppError, return as is
  if (error instanceof AppError) {
    return error;
  }

  // Handle specific error types
  if (error instanceof Error) {
    // Database errors
    if (error.message.includes('duplicate key')) {
      return new AppError(
        'Record already exists',
        ErrorCode.DUPLICATE_RECORD,
        409
      );
    }

    // Network errors
    if (error.message.includes('fetch') || error.message.includes('network')) {
      return new AppError(
        'Network error occurred',
        ErrorCode.NETWORK_ERROR,
        503
      );
    }

    // OAuth errors
    if (error.message.includes('oauth') || error.message.includes('token')) {
      return new AppError(
        'Authentication service error',
        ErrorCode.OAUTH_ERROR,
        401
      );
    }

    // Generic error
    return new AppError(
      error.message,
      ErrorCode.INTERNAL_SERVER_ERROR,
      500,
      true,
      { originalStack: error.stack }
    );
  }

  // Unknown error type
  return new AppError(
    'An unknown error occurred',
    ErrorCode.UNKNOWN_ERROR,
    500,
    false
  );
};

/**
 * Format error for API response
 */
export const formatErrorResponse = (error: AppError) => {
  const response: any = {
    success: false,
    error: {
      message: error.message,
      code: error.code,
      timestamp: error.timestamp,
    },
  };

  // Include additional context in development
  if (process.env.NODE_ENV === 'development') {
    response.error.stack = error.stack;
    response.error.context = error.context;
  }

  return response;
};

/**
 * Log error with appropriate level
 */
export const logError = (error: AppError, additionalContext?: Record<string, any>) => {
  const logData = {
    message: error.message,
    code: error.code,
    statusCode: error.statusCode,
    timestamp: error.timestamp,
    isOperational: error.isOperational,
    context: { ...error.context, ...additionalContext },
    stack: error.stack,
  };

  // Only log in development
  if (process.env.NODE_ENV === 'development') {
    if (error.statusCode >= 500) {
      console.error('🚨 Server Error:', logData);
    } else if (error.statusCode >= 400) {
      console.warn('⚠️ Client Error:', logData);
    } else {
      console.info('ℹ️ Info:', logData);
    }
  }

  // In production, send critical errors to external logging service
  if (process.env.NODE_ENV === 'production' && error.statusCode >= 500) {
    // Handle promise rejection properly to prevent unhandled promise rejection
    sendToExternalLoggingService(logData).catch((loggingError) => {
      // Don't let logging errors break the application
      console.error('Failed to send error to external logging service:', loggingError);
    });
  }
};

/**
 * Send error data to external logging service
 * This is a placeholder implementation - integrate with your preferred service
 */
async function sendToExternalLoggingService(logData: any): Promise<void> {
  // Example implementations for different services:

  // For Sentry:
  // import * as Sentry from '@sentry/nextjs';
  // Sentry.captureException(new Error(logData.message), { extra: logData });

  // For LogRocket:
  // import LogRocket from 'logrocket';
  // LogRocket.captureException(new Error(logData.message));

  // For custom webhook:
  // await fetch(process.env.ERROR_WEBHOOK_URL!, {
  //   method: 'POST',
  //   headers: { 'Content-Type': 'application/json' },
  //   body: JSON.stringify(logData)
  // });

  // For now, just log to console in production (can be picked up by log aggregators)
  console.error('🚨 PRODUCTION ERROR:', JSON.stringify(logData, null, 2));
}

/**
 * Async error wrapper for API routes
 */
export const asyncErrorHandler = (
  fn: (req: any, res: any, next?: any) => Promise<any>
) => {
  return (req: any, res: any, next?: any) => {
    Promise.resolve(fn(req, res, next)).catch((error) => {
      const appError = handleApiError(error);
      logError(appError, { url: req.url, method: req.method });

      return res.status(appError.statusCode).json(formatErrorResponse(appError));
    });
  };
};

/**
 * Client-side error handler
 */
export const handleClientError = (error: unknown, context?: string): string => {
  const appError = handleApiError(error);
  
  // Log error in development
  if (process.env.NODE_ENV === 'development') {
    console.error(`Client Error${context ? ` (${context})` : ''}:`, appError);
  }

  // Return user-friendly message
  switch (appError.code) {
    case ErrorCode.UNAUTHORIZED:
      return 'Please log in to continue';
    case ErrorCode.FORBIDDEN:
      return 'You do not have permission to perform this action';
    case ErrorCode.VALIDATION_ERROR:
      return appError.message;
    case ErrorCode.RATE_LIMIT_EXCEEDED:
      return 'Too many requests. Please try again later';
    case ErrorCode.NETWORK_ERROR:
      return 'Network error. Please check your connection';
    case ErrorCode.FILE_TOO_LARGE:
      return 'File is too large. Please choose a smaller file';
    case ErrorCode.INVALID_FILE_TYPE:
      return 'Invalid file type. Please choose a supported file format';
    default:
      return 'An unexpected error occurred. Please try again';
  }
};

/**
 * Error boundary helper for React components
 */
export const createErrorBoundaryError = (
  error: Error,
  errorInfo: { componentStack: string }
): AppError => {
  return new AppError(
    'Component error occurred',
    ErrorCode.INTERNAL_SERVER_ERROR,
    500,
    true,
    {
      originalError: error.message,
      componentStack: errorInfo.componentStack,
      stack: error.stack,
    }
  );
};

/**
 * Validation helper functions
 */
export const validateRequired = (value: any, fieldName: string): void => {
  if (value === undefined || value === null || value === '') {
    throw new ValidationError(`${fieldName} is required`, fieldName, value);
  }
};

export const validateEmail = (email: string): void => {
  const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
  if (!emailRegex.test(email)) {
    throw new ValidationError('Invalid email format', 'email', email);
  }
};

export const validateUrl = (url: string, fieldName: string = 'URL'): void => {
  try {
    new URL(url);
  } catch {
    throw new ValidationError(`Invalid ${fieldName} format`, fieldName, url);
  }
};

export const validateUUID = (id: string, fieldName: string = 'ID'): void => {
  const uuidRegex = /^[0-9a-f]{8}-[0-9a-f]{4}-[1-5][0-9a-f]{3}-[89ab][0-9a-f]{3}-[0-9a-f]{12}$/i;
  if (!uuidRegex.test(id)) {
    throw new ValidationError(`Invalid ${fieldName} format`, fieldName, id);
  }
};

/**
 * Extract IP address from Next.js request
 */
export const getClientIP = (request: any): string => {
  // Try different headers for IP address
  const forwarded = request.headers.get('x-forwarded-for');
  if (forwarded) {
    return forwarded.split(',')[0].trim();
  }

  const realIP = request.headers.get('x-real-ip');
  if (realIP) {
    return realIP;
  }

  const cfConnectingIP = request.headers.get('cf-connecting-ip');
  if (cfConnectingIP) {
    return cfConnectingIP;
  }

  return 'unknown';
};
