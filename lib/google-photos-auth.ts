import { google } from 'googleapis';

/**
 * Google Photos OAuth2 configuration and utilities
 */

export interface GooglePhotosAuthConfig {
  clientId: string;
  clientSecret: string;
  redirectUri: string;
}

export interface GooglePhotosTokens {
  access_token: string;
  refresh_token?: string;
  scope: string;
  token_type: string;
  expiry_date?: number;
}

/**
 * Get Google Photos OAuth2 configuration from environment variables
 */
export function getGooglePhotosConfig(): GooglePhotosAuthConfig {
  const clientId = process.env.GOOGLE_PHOTOS_CLIENT_ID;
  const clientSecret = process.env.GOOGLE_PHOTOS_CLIENT_SECRET;
  const redirectUri = process.env.GOOGLE_PHOTOS_REDIRECT_URI;

  if (!clientId || !clientSecret || !redirectUri) {
    throw new Error('Missing Google Photos OAuth2 configuration. Please check environment variables.');
  }

  return {
    clientId,
    clientSecret,
    redirectUri,
  };
}

/**
 * Create OAuth2 client for Google Photos
 */
export function createGooglePhotosOAuth2Client() {
  const config = getGooglePhotosConfig();
  
  return new google.auth.OAuth2(
    config.clientId,
    config.clientSecret,
    config.redirectUri
  );
}

/**
 * Generate authorization URL for Google Photos access
 */
export function generateAuthUrl(tripPhotoDetailsId: string): string {
  const oauth2Client = createGooglePhotosOAuth2Client();
  
  const scopes = [
    'https://www.googleapis.com/auth/photoslibrary.appendonly',
    'https://www.googleapis.com/auth/photoslibrary.readonly.appcreateddata',
    'https://www.googleapis.com/auth/userinfo.email',
    'https://www.googleapis.com/auth/userinfo.profile',
  ];

  console.log('[GOOGLE_PHOTOS_AUTH] Using scopes:', scopes);

  return oauth2Client.generateAuthUrl({
    access_type: 'offline', // Important: gets refresh token
    scope: scopes,
    state: tripPhotoDetailsId, // Pass trip ID to identify which trip this auth is for
    prompt: 'consent', // Force consent screen to ensure we get refresh token
  });
}

/**
 * Exchange authorization code for tokens
 */
export async function exchangeCodeForTokens(code: string): Promise<GooglePhotosTokens> {
  console.log('[GOOGLE_PHOTOS_AUTH] Exchanging authorization code for tokens...');
  console.log('[GOOGLE_PHOTOS_AUTH] Authorization code length:', code.length);

  const oauth2Client = createGooglePhotosOAuth2Client();

  try {
    console.log('[GOOGLE_PHOTOS_AUTH] Making token exchange request to Google...');
    const { tokens } = await oauth2Client.getToken(code);

    console.log('[GOOGLE_PHOTOS_AUTH] Token exchange response:', {
      hasAccessToken: !!tokens.access_token,
      hasRefreshToken: !!tokens.refresh_token,
      hasIdToken: !!tokens.id_token,
      expiryDate: tokens.expiry_date,
      scope: tokens.scope,
      tokenType: tokens.token_type,
    });

    if (!tokens.access_token) {
      throw new Error('No access token received from Google');
    }

    console.log('[GOOGLE_PHOTOS_AUTH] ✅ Token exchange successful');
    return tokens as GooglePhotosTokens;
  } catch (error) {
    console.error('[GOOGLE_PHOTOS_AUTH] ❌ Error exchanging code for tokens:', error);
    console.error('[GOOGLE_PHOTOS_AUTH] Error details:', JSON.stringify(error, null, 2));
    throw new Error(`Failed to exchange authorization code: ${error instanceof Error ? error.message : 'Unknown error'}`);
  }
}

/**
 * Create authenticated Google Photos client using refresh token
 */
export function createAuthenticatedPhotosClient(refreshToken: string) {
  const oauth2Client = createGooglePhotosOAuth2Client();

  oauth2Client.setCredentials({
    refresh_token: refreshToken,
  });

  // Note: Google Photos Library API is accessed through the main googleapis client
  // We'll use the oauth2Client directly for API calls
  return { auth: oauth2Client };
}

/**
 * Refresh access token using refresh token
 */
export async function refreshAccessToken(refreshToken: string): Promise<GooglePhotosTokens> {
  const oauth2Client = createGooglePhotosOAuth2Client();
  
  oauth2Client.setCredentials({
    refresh_token: refreshToken,
  });

  try {
    const { token } = await oauth2Client.getAccessToken();
    if (!token) {
      throw new Error('No access token received');
    }

    // Get the current credentials which include the refresh token
    const credentials = oauth2Client.credentials;
    return {
      access_token: token,
      refresh_token: credentials.refresh_token,
      scope: credentials.scope || '',
      token_type: credentials.token_type || 'Bearer',
      expiry_date: credentials.expiry_date,
    } as GooglePhotosTokens;
  } catch (error) {
    console.error('[GOOGLE_PHOTOS_AUTH] Error refreshing access token:', error);
    throw new Error(`Failed to refresh access token: ${error instanceof Error ? error.message : 'Unknown error'}`);
  }
}

/**
 * Validate if refresh token is still valid
 */
export async function validateRefreshToken(refreshToken: string): Promise<boolean> {
  try {
    await refreshAccessToken(refreshToken);
    return true;
  } catch (error) {
    console.error('[GOOGLE_PHOTOS_AUTH] Refresh token validation failed:', error);
    return false;
  }
}

/**
 * Get user info from Google Photos API using refresh token
 */
export async function getUserInfo(refreshToken: string) {
  try {
    const oauth2Client = createGooglePhotosOAuth2Client();
    oauth2Client.setCredentials({ refresh_token: refreshToken });

    const oauth2 = google.oauth2({ version: 'v2', auth: oauth2Client });
    const { data } = await oauth2.userinfo.get();

    return {
      email: data.email,
      name: data.name,
      picture: data.picture,
    };
  } catch (error) {
    console.error('[GOOGLE_PHOTOS_AUTH] Error getting user info:', error);
    throw new Error(`Failed to get user info: ${error instanceof Error ? error.message : 'Unknown error'}`);
  }
}

/**
 * Get user info from Google Photos API using tokens from OAuth callback
 */
export async function getUserInfoWithTokens(tokens: GooglePhotosTokens) {
  try {
    console.log('[GOOGLE_PHOTOS_AUTH] Getting user info with tokens...');

    const oauth2Client = createGooglePhotosOAuth2Client();

    // Set all the credentials we received
    oauth2Client.setCredentials({
      access_token: tokens.access_token,
      refresh_token: tokens.refresh_token,
      expiry_date: tokens.expiry_date,
    });

    console.log('[GOOGLE_PHOTOS_AUTH] Credentials set, making userinfo request...');

    const oauth2 = google.oauth2({ version: 'v2', auth: oauth2Client });
    const { data } = await oauth2.userinfo.get();

    console.log('[GOOGLE_PHOTOS_AUTH] User info retrieved successfully:', {
      email: data.email,
      name: data.name,
    });

    return {
      email: data.email,
      name: data.name,
      picture: data.picture,
    };
  } catch (error) {
    console.error('[GOOGLE_PHOTOS_AUTH] Error getting user info with tokens:', error);
    console.error('[GOOGLE_PHOTOS_AUTH] Error details:', JSON.stringify(error, null, 2));
    throw new Error(`Failed to get user info: ${error instanceof Error ? error.message : 'Unknown error'}`);
  }
}

/**
 * Revoke refresh token (logout)
 */
export async function revokeRefreshToken(refreshToken: string): Promise<void> {
  try {
    const oauth2Client = createGooglePhotosOAuth2Client();
    await oauth2Client.revokeToken(refreshToken);
  } catch (error) {
    console.error('[GOOGLE_PHOTOS_AUTH] Error revoking token:', error);
    throw new Error(`Failed to revoke token: ${error instanceof Error ? error.message : 'Unknown error'}`);
  }
}
