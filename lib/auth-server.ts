import { createServerClient } from '@supabase/ssr'
import { createClient } from '@supabase/supabase-js'
import { cookies } from 'next/headers'
import type { CookieOptions } from '@supabase/ssr'
import type { AdminUser, AdminRole } from '@/lib/auth'

// Import proper database types
import type { Database } from '@/types/supabase'

// Modern server-side auth client for API routes and server components
export async function createServerSupabaseClient() {
  const cookieStore = await cookies()

  return createServerClient<Database>(
    process.env.NEXT_PUBLIC_SUPABASE_URL!,
    process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!,
    {
      cookies: {
        get(name: string) {
          return cookieStore.get(name)?.value
        },
        set(name: string, value: string, options: CookieOptions) {
          try {
            cookieStore.set({ name, value, ...options })
          } catch (error) {
            // Server component context - ignore
          }
        },
        remove(name: string, options: CookieOptions) {
          try {
            cookieStore.set({ name, value: '', ...options })
          } catch (error) {
            // Server component context - ignore
          }
        },
      },
    }
  )
}

// Admin service client (for admin operations)
export function createAdminClient() {
  return createClient<Database>(
    process.env.NEXT_PUBLIC_SUPABASE_URL!,
    process.env.SUPABASE_SERVICE_ROLE_KEY!,
    {
      auth: {
        autoRefreshToken: false,
        persistSession: false
      }
    }
  )
}

// Get admin user with roles (server-side version)
export async function getAdminUser(userId: string): Promise<AdminUser | null> {
  const supabase = createAdminClient()

  const { data: profile, error: profileError } = await supabase
    .from('admin_profiles')
    .select('*')
    .eq('id', userId)
    .single()

  if (profileError || !profile) {
    return null
  }

  // Get email from auth.users table
  const { data: authUser } = await supabase.auth.admin.getUserById(userId)
  const email = authUser?.user?.email || null

  const { data: userRoles, error: rolesError } = await supabase
    .from('admin_user_roles')
    .select(`
      admin_roles (
        id,
        name,
        description,
        permissions,
        created_at,
        updated_at
      )
    `)
    .eq('user_id', userId)

  if (rolesError) {
    return null
  }

  const roles = userRoles
    .map((ur: any) => ur.admin_roles)
    .filter(Boolean) as AdminRole[]

  return {
    ...profile,
    email,
    roles
  }
}

// Modern admin access verification (server-side)
export async function verifyAdminAccess(
  resource?: string,
  action?: string
): Promise<{ user: AdminUser | null; hasAccess: boolean }> {
  const supabase = await createServerSupabaseClient()

  const { data: { user }, error } = await supabase.auth.getUser()

  if (error || !user) {
    return { user: null, hasAccess: false }
  }

  const adminUser = await getAdminUser(user.id)

  if (!adminUser || !adminUser.is_active) {
    return { user: null, hasAccess: false }
  }

  // If no specific resource/action required, just check if user is admin
  if (!resource || !action) {
    return { user: adminUser, hasAccess: adminUser.roles.length > 0 }
  }

  // Check specific permission
  const { hasPermission } = await import('@/lib/auth')
  const hasAccess = hasPermission(adminUser.roles, resource, action)

  return { user: adminUser, hasAccess }
}

// Verify owner access (server-side)
export async function verifyOwnerAccess(): Promise<{ user: AdminUser | null; hasAccess: boolean }> {
  const supabase = await createServerSupabaseClient()

  const { data: { user }, error } = await supabase.auth.getUser()

  if (error || !user) {
    return { user: null, hasAccess: false }
  }

  const adminUser = await getAdminUser(user.id)

  if (!adminUser || !adminUser.is_active) {
    return { user: null, hasAccess: false }
  }

  // Check if user has owner role
  const { isOwner } = await import('@/lib/auth')
  const hasAccess = isOwner(adminUser.roles)

  return { user: adminUser, hasAccess }
}
