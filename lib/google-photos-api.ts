import { createAuthenticatedPhotosClient } from './google-photos-auth';

/**
 * Google Photos API integration functions
 */

export interface GooglePhotosAlbum {
  id: string;
  title: string;
  productUrl: string;
  shareableUrl?: string;
  mediaItemsCount?: string;
  shareInfo?: {
    shareableUrl?: string;
    shareToken?: string;
    isJoined?: boolean;
    isOwned?: boolean;
    isJoinable?: boolean;
  };
}

export interface GooglePhotosMediaItem {
  id: string;
  productUrl: string;
  baseUrl: string;
  filename: string;
  mimeType: string;
}

/**
 * Create a new album in Google Photos using REST API
 * Note: As of March 2025, the albums:share method has been deprecated.
 * Albums are now created with sharing enabled by default when using the API.
 */
export async function createGooglePhotosAlbum(
  refreshToken: string,
  albumTitle: string
): Promise<GooglePhotosAlbum> {
  try {
    console.log(`[GOOGLE_PHOTOS_API] Creating album: ${albumTitle}`);

    const { auth } = createAuthenticatedPhotosClient(refreshToken);

    // Get access token
    const { token } = await auth.getAccessToken();

    if (!token) {
      throw new Error('Failed to get access token');
    }

    // Use REST API to create album
    const response = await fetch('https://photoslibrary.googleapis.com/v1/albums', {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${token}`,
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        album: {
          title: albumTitle,
        },
      }),
    });

    if (!response.ok) {
      const errorText = await response.text();
      throw new Error(`HTTP ${response.status}: ${errorText}`);
    }

    const albumData = await response.json();

    if (!albumData.id) {
      throw new Error('Failed to create album - no album data returned');
    }

    console.log(`[GOOGLE_PHOTOS_API] Album created successfully: ${albumData.id}`);
    console.log(`[GOOGLE_PHOTOS_API] Album data:`, JSON.stringify(albumData, null, 2));

    // Return album data without attempting to enable sharing
    // Sharing will be handled manually by admins in the Google Photos UI
    return {
      id: albumData.id,
      title: albumData.title || albumTitle,
      productUrl: albumData.productUrl || '',
      shareableUrl: albumData.shareInfo?.shareableUrl || albumData.shareableUrl,
      mediaItemsCount: albumData.mediaItemsCount,
    };
  } catch (error) {
    console.error('[GOOGLE_PHOTOS_API] Error creating album:', error);
    throw new Error(`Failed to create Google Photos album: ${error instanceof Error ? error.message : 'Unknown error'}`);
  }
}

/**
 * Core function to upload a buffer to Google Photos and create media item
 * This eliminates code duplication between file and buffer upload functions
 */
async function uploadBufferToGooglePhotosCore(
  refreshToken: string,
  fileBuffer: Buffer,
  fileName: string,
  mimeType: string
): Promise<string> {
  const { auth } = createAuthenticatedPhotosClient(refreshToken);

  // Get access token
  const { token } = await auth.getAccessToken();

  if (!token) {
    throw new Error('Failed to get access token');
  }

  // Step 1: Upload the raw bytes with proper headers
  const uploadResponse = await fetch('https://photoslibrary.googleapis.com/v1/uploads', {
    method: 'POST',
    headers: {
      'Authorization': `Bearer ${token}`,
      'Content-Type': 'application/octet-stream',
      'X-Goog-Upload-Content-Type': mimeType, // Added missing content type header
      'X-Goog-Upload-File-Name': fileName,
      'X-Goog-Upload-Protocol': 'raw',
    },
    body: fileBuffer,
  });

  if (!uploadResponse.ok) {
    const errorText = await uploadResponse.text();
    throw new Error(`Upload failed: HTTP ${uploadResponse.status}: ${errorText}`);
  }

  const uploadToken = await uploadResponse.text();
  console.log(`[GOOGLE_PHOTOS_API] Media bytes uploaded, creating media item`);

  // Step 2: Create the media item
  const createResponse = await fetch('https://photoslibrary.googleapis.com/v1/mediaItems:batchCreate', {
    method: 'POST',
    headers: {
      'Authorization': `Bearer ${token}`,
      'Content-Type': 'application/json',
    },
    body: JSON.stringify({
      newMediaItems: [
        {
          description: `Uploaded by Positive7 - ${fileName}`,
          simpleMediaItem: {
            fileName: fileName,
            uploadToken: uploadToken,
          },
        },
      ],
    }),
  });

  if (!createResponse.ok) {
    const errorText = await createResponse.text();
    throw new Error(`Create media item failed: HTTP ${createResponse.status}: ${errorText}`);
  }

  const createData = await createResponse.json();

  if (!createData.newMediaItemResults?.[0]?.mediaItem?.id) {
    throw new Error('Failed to create media item - no ID returned');
  }

  const mediaItemId = createData.newMediaItemResults[0].mediaItem.id;
  console.log(`[GOOGLE_PHOTOS_API] Media item created successfully: ${mediaItemId}`);

  return mediaItemId;
}

/**
 * Upload a media item to Google Photos using REST API (file path version)
 */
export async function uploadMediaToGooglePhotos(
  refreshToken: string,
  filePath: string,
  fileName: string,
  mimeType: string
): Promise<string> {
  try {
    console.log(`[GOOGLE_PHOTOS_API] Uploading media: ${fileName}`);

    // Read the file asynchronously
    const fileBuffer = await import('fs').then(fs => fs.promises.readFile(filePath));

    // Use the core upload function
    return await uploadBufferToGooglePhotosCore(refreshToken, fileBuffer, fileName, mimeType);
  } catch (error) {
    console.error('[GOOGLE_PHOTOS_API] Error uploading media:', error);
    throw new Error(`Failed to upload media to Google Photos: ${error instanceof Error ? error.message : 'Unknown error'}`);
  }
}

/**
 * Upload a media buffer to Google Photos using REST API (serverless-compatible)
 */
export async function uploadMediaBufferToGooglePhotos(
  refreshToken: string,
  fileBuffer: Buffer,
  fileName: string,
  mimeType: string
): Promise<string> {
  try {
    console.log(`[GOOGLE_PHOTOS_API] Uploading media buffer: ${fileName}`);
    console.log(`[GOOGLE_PHOTOS_API] Buffer size: ${fileBuffer.length} bytes`);

    // Use the core upload function to eliminate code duplication
    return await uploadBufferToGooglePhotosCore(refreshToken, fileBuffer, fileName, mimeType);
  } catch (error) {
    console.error('[GOOGLE_PHOTOS_API] Error uploading media buffer:', error);
    throw new Error(`Failed to upload media buffer to Google Photos: ${error instanceof Error ? error.message : 'Unknown error'}`);
  }
}

/**
 * Add media items to an album using REST API
 */
export async function addMediaToAlbum(
  refreshToken: string,
  albumId: string,
  mediaItemIds: string[]
): Promise<void> {
  try {
    console.log(`[GOOGLE_PHOTOS_API] Adding ${mediaItemIds.length} media items to album: ${albumId}`);

    const { auth } = createAuthenticatedPhotosClient(refreshToken);

    // Get access token
    const { token } = await auth.getAccessToken();

    if (!token) {
      throw new Error('Failed to get access token');
    }

    const response = await fetch(`https://photoslibrary.googleapis.com/v1/albums/${albumId}:batchAddMediaItems`, {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${token}`,
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        mediaItemIds: mediaItemIds,
      }),
    });

    if (!response.ok) {
      const errorText = await response.text();
      throw new Error(`Add to album failed: HTTP ${response.status}: ${errorText}`);
    }

    console.log(`[GOOGLE_PHOTOS_API] Successfully added media items to album`);
  } catch (error) {
    console.error('[GOOGLE_PHOTOS_API] Error adding media to album:', error);
    throw new Error(`Failed to add media to album: ${error instanceof Error ? error.message : 'Unknown error'}`);
  }
}

/**
 * Get album details using REST API
 */
export async function getAlbumDetails(
  refreshToken: string,
  albumId: string
): Promise<GooglePhotosAlbum> {
  try {
    console.log(`[GOOGLE_PHOTOS_API] Getting album details: ${albumId}`);

    const { auth } = createAuthenticatedPhotosClient(refreshToken);

    // Get access token
    const { token } = await auth.getAccessToken();

    if (!token) {
      throw new Error('Failed to get access token');
    }

    const response = await fetch(`https://photoslibrary.googleapis.com/v1/albums/${albumId}`, {
      method: 'GET',
      headers: {
        'Authorization': `Bearer ${token}`,
      },
    });

    if (!response.ok) {
      const errorText = await response.text();
      throw new Error(`Get album failed: HTTP ${response.status}: ${errorText}`);
    }

    const albumData = await response.json();

    if (!albumData) {
      throw new Error('Album not found');
    }

    return {
      id: albumData.id || albumId,
      title: albumData.title || 'Untitled Album',
      productUrl: albumData.productUrl || '',
      shareableUrl: albumData.shareableUrl,
      mediaItemsCount: albumData.mediaItemsCount,
      shareInfo: albumData.shareInfo,
    };
  } catch (error) {
    console.error('[GOOGLE_PHOTOS_API] Error getting album details:', error);
    throw new Error(`Failed to get album details: ${error instanceof Error ? error.message : 'Unknown error'}`);
  }
}

/**
 * Note: Google Photos API does not support album deletion
 * This function exists for API compatibility but will always throw an error
 * Albums must be manually deleted from the Google Photos web interface
 * See: https://issuetracker.google.com/issues/135714733
 */
export async function deleteGooglePhotosAlbum(
  refreshToken: string,
  albumId: string
): Promise<void> {
  console.log(`[GOOGLE_PHOTOS_API] Album deletion requested for: ${albumId}`);
  console.log(`[GOOGLE_PHOTOS_API] Google Photos API does not support album deletion`);

  // The Google Photos API does not support deleting albums
  // This is a known limitation documented at:
  // https://issuetracker.google.com/issues/135714733
  throw new Error(
    'Google Photos API does not support album deletion. ' +
    'Albums must be manually deleted from the Google Photos web interface at photos.google.com. ' +
    'This is a limitation of the Google Photos API, not our application.'
  );
}

/**
 * Create album wrapper function for callback compatibility
 */
export async function createAlbum(title: string, refreshToken: string): Promise<string> {
  const album = await createGooglePhotosAlbum(refreshToken, title);
  return album.id;
}

// Note: Removed deprecated sharing functions (enableAlbumSharing, shareAlbum)
// These used deprecated Google Photos API methods that were removed on March 31, 2025
// We now use manual shareable links created by admins in the Google Photos UI

// Note: generateShareableUrl function removed as we now use manual shareable links
// instead of the deprecated Google Photos API sharing methods

/**
 * Upload watermarked image to Google Photos album
 * This is the main function that combines upload and album addition
 */
export async function uploadToGooglePhotosAlbum(
  refreshToken: string,
  filePath: string,
  fileName: string,
  mimeType: string,
  albumId?: string,
  albumTitle?: string
): Promise<{ mediaItemId: string; albumId: string; albumUrl: string }> {
  try {
    console.log(`[GOOGLE_PHOTOS_API] Starting upload to Google Photos album`);

    let finalAlbumId = albumId;
    let albumUrl = '';

    // Create album if needed
    if (!finalAlbumId && albumTitle) {
      const album = await createGooglePhotosAlbum(refreshToken, albumTitle);
      finalAlbumId = album.id;
      albumUrl = album.productUrl;
    } else if (finalAlbumId) {
      const album = await getAlbumDetails(refreshToken, finalAlbumId);
      albumUrl = album.productUrl;
    }

    // Upload the media
    const mediaItemId = await uploadMediaToGooglePhotos(refreshToken, filePath, fileName, mimeType);

    // Add to album if we have an album ID
    if (finalAlbumId) {
      await addMediaToAlbum(refreshToken, finalAlbumId, [mediaItemId]);
    }

    console.log(`[GOOGLE_PHOTOS_API] Successfully uploaded to Google Photos album`);

    return {
      mediaItemId,
      albumId: finalAlbumId || '',
      albumUrl,
    };
  } catch (error) {
    console.error('[GOOGLE_PHOTOS_API] Error in upload workflow:', error);
    throw new Error(`Failed to upload to Google Photos album: ${error instanceof Error ? error.message : 'Unknown error'}`);
  }
}

/**
 * Upload watermarked image buffer to Google Photos album (serverless-compatible)
 * This is the main function that combines upload and album addition using buffers
 */
export async function uploadBufferToGooglePhotosAlbum(
  refreshToken: string,
  fileBuffer: Buffer,
  fileName: string,
  mimeType: string,
  albumId?: string,
  albumTitle?: string
): Promise<{ mediaItemId: string; albumId: string; albumUrl: string }> {
  try {
    console.log(`[GOOGLE_PHOTOS_API] Starting buffer upload to Google Photos album`);

    let finalAlbumId = albumId;
    let albumUrl = '';

    // Create album if needed
    if (!finalAlbumId && albumTitle) {
      const album = await createGooglePhotosAlbum(refreshToken, albumTitle);
      finalAlbumId = album.id;
      albumUrl = album.productUrl;
    } else if (finalAlbumId) {
      const album = await getAlbumDetails(refreshToken, finalAlbumId);
      albumUrl = album.productUrl;
    }

    // Upload the media using buffer
    const mediaItemId = await uploadMediaBufferToGooglePhotos(refreshToken, fileBuffer, fileName, mimeType);

    // Add to album if we have an album ID
    if (finalAlbumId) {
      await addMediaToAlbum(refreshToken, finalAlbumId, [mediaItemId]);
    }

    console.log(`[GOOGLE_PHOTOS_API] Successfully uploaded buffer to Google Photos album`);

    return {
      mediaItemId,
      albumId: finalAlbumId || '',
      albumUrl,
    };
  } catch (error) {
    console.error('[GOOGLE_PHOTOS_API] Error in buffer upload workflow:', error);
    throw new Error(`Failed to upload buffer to Google Photos album: ${error instanceof Error ? error.message : 'Unknown error'}`);
  }
}
