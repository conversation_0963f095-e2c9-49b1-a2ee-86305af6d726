/**
 * Advanced search and filtering system
 */

import { createServerSupabase } from './supabase-server';
import { measureDatabaseQuery } from './performance-monitor';
import { cacheInstances, withCache } from './cache-manager';
import { sanitizeSearchQuery, createSafeSearchCondition } from './input-sanitization';

// Search configuration
export interface SearchConfig {
  table: string;
  searchableFields: string[];
  filterableFields: string[];
  sortableFields: string[];
  defaultSort: { field: string; direction: 'asc' | 'desc' };
  maxResults: number;
}

// Search parameters
export interface SearchParams {
  query?: string;
  filters?: Record<string, any>;
  sort?: { field: string; direction: 'asc' | 'desc' };
  page?: number;
  limit?: number;
  includeCount?: boolean;
}

// Search result
export interface SearchResult<T> {
  data: T[];
  total?: number;
  page: number;
  limit: number;
  hasMore: boolean;
  searchTime: number;
  cacheHit: boolean;
}

// Predefined search configurations
export const SEARCH_CONFIGS: Record<string, SearchConfig> = {
  trips: {
    table: 'trips',
    searchableFields: ['title', 'destination', 'description'],
    filterableFields: ['is_active', 'destination', 'created_at', 'updated_at'],
    sortableFields: ['title', 'destination', 'created_at', 'updated_at'],
    defaultSort: { field: 'created_at', direction: 'desc' },
    maxResults: 1000,
  },
  blog_posts: {
    table: 'blog_posts',
    searchableFields: ['title', 'content', 'excerpt', 'tags'],
    filterableFields: ['is_published', 'author', 'created_at', 'updated_at'],
    sortableFields: ['title', 'created_at', 'updated_at'],
    defaultSort: { field: 'created_at', direction: 'desc' },
    maxResults: 1000,
  },
  inquiries: {
    table: 'inquiries',
    searchableFields: ['name', 'email', 'message', 'phone'],
    filterableFields: ['status', 'created_at', 'updated_at'],
    sortableFields: ['name', 'email', 'created_at', 'updated_at'],
    defaultSort: { field: 'created_at', direction: 'desc' },
    maxResults: 1000,
  },
  trip_photos: {
    table: 'trip_photos_details',
    searchableFields: ['trip_name', 'trip_description'],
    filterableFields: ['storage_type', 'created_at', 'updated_at'],
    sortableFields: ['trip_name', 'created_at', 'updated_at'],
    defaultSort: { field: 'created_at', direction: 'desc' },
    maxResults: 500,
  },
};

export class AdvancedSearch {
  private supabase = createServerSupabase();

  /**
   * Perform advanced search with caching
   */
  async search<T>(
    configKey: string,
    params: SearchParams = {}
  ): Promise<SearchResult<T>> {
    const config = SEARCH_CONFIGS[configKey];
    if (!config) {
      throw new Error(`Search configuration not found for: ${configKey}`);
    }

    const startTime = Date.now();
    
    // Generate cache key
    const cacheKey = this.generateCacheKey(configKey, params);
    
    // Try to get from cache first
    const cacheInstance = this.getCacheInstance(configKey);
    
    try {
      const result = await withCache(
        cacheKey,
        () => this.performSearch<T>(config, params),
        cacheInstance,
        5 * 60 * 1000 // 5 minutes TTL
      );

      const searchTime = Date.now() - startTime;

      return {
        ...result,
        searchTime,
        cacheHit: false, // Will be set by cache layer if hit
      };
    } catch (error) {
      console.error('Search error:', error);
      throw error;
    }
  }

  /**
   * Apply filters to a query builder
   */
  private applyFilters(queryBuilder: any, config: SearchConfig, filters: Record<string, any>): any {
    Object.entries(filters).forEach(([field, value]) => {
      if (config.filterableFields.includes(field) && value !== undefined && value !== '') {
        if (Array.isArray(value)) {
          queryBuilder = queryBuilder.in(field, value);
        } else if (typeof value === 'object' && value !== null) {
          // Handle range filters
          if (value.from !== undefined) {
            queryBuilder = queryBuilder.gte(field, value.from);
          }
          if (value.to !== undefined) {
            queryBuilder = queryBuilder.lte(field, value.to);
          }
        } else {
          queryBuilder = queryBuilder.eq(field, value);
        }
      }
    });
    return queryBuilder;
  }

  /**
   * Perform the actual search query
   */
  private async performSearch<T>(
    config: SearchConfig,
    params: SearchParams
  ): Promise<Omit<SearchResult<T>, 'searchTime' | 'cacheHit'>> {
    const {
      query = '',
      filters = {},
      sort = config.defaultSort,
      page = 1,
      limit = 20,
      includeCount = true,
    } = params;

    // Validate parameters
    const validatedLimit = Math.min(limit, config.maxResults);
    const offset = (page - 1) * validatedLimit;

    return await measureDatabaseQuery(
      `advanced_search_${config.table}`,
      async () => {
        // Type assertion needed for dynamic table names
        let queryBuilder = (this.supabase as any).from(config.table).select('*');

        // Apply text search with proper sanitization
        if (query.trim()) {
          const searchConditions = createSafeSearchCondition(query, config.searchableFields);
          if (searchConditions) {
            queryBuilder = queryBuilder.or(searchConditions);
          }
        }

        // Apply filters
        queryBuilder = this.applyFilters(queryBuilder, config, filters);

        // Apply sorting
        if (config.sortableFields.includes(sort.field)) {
          queryBuilder = queryBuilder.order(sort.field, { ascending: sort.direction === 'asc' });
        }

        // Apply pagination
        queryBuilder = queryBuilder.range(offset, offset + validatedLimit - 1);

        const { data, error } = await queryBuilder;

        if (error) {
          throw new Error(`Search query failed: ${error.message}`);
        }

        // Get total count if requested
        let total: number | undefined;
        if (includeCount) {
          try {
            let countQuery = (this.supabase as any).from(config.table).select('*', { count: 'exact', head: true });

            // Apply same search conditions for count
            if (query.trim()) {
              const searchConditions = createSafeSearchCondition(query, config.searchableFields);
              if (searchConditions) {
                countQuery = countQuery.or(searchConditions);
              }
            }

            // Apply same filters for count
            countQuery = this.applyFilters(countQuery, config, filters);

            const { count, error: countError } = await countQuery;
            if (countError) {
              throw new Error(`Count query failed: ${countError.message}`);
            }
            total = count || 0;
          } catch (countError) {
            console.error('Count query failed:', countError);
            // Don't fail the entire search, just omit count
            total = undefined;
          }
        }

        const hasMore = total !== undefined
          ? offset + validatedLimit < total
          : data.length === validatedLimit;

        return {
          data: data as T[],
          total,
          page,
          limit: validatedLimit,
          hasMore,
        };
      },
      'advanced_search'
    );
  }

  /**
   * Get search suggestions based on query
   */
  async getSuggestions(
    configKey: string,
    query: string,
    field: string,
    limit: number = 10
  ): Promise<string[]> {
    const config = SEARCH_CONFIGS[configKey];
    if (!config || !config.searchableFields.includes(field)) {
      return [];
    }

    const cacheKey = `suggestions_${configKey}_${field}_${query}_${limit}`;
    const cacheInstance = this.getCacheInstance(configKey);

    return await withCache(
      cacheKey,
      async () => {
        const sanitizedQuery = sanitizeSearchQuery(query);
        if (!sanitizedQuery) {
          return [];
        }

        const { data, error } = await (this.supabase as any)
          .from(config.table)
          .select(field)
          .ilike(field, `%${sanitizedQuery}%`)
          .not(field, 'is', null)
          .limit(limit);

        if (error) {
          console.error('Suggestions query failed:', error);
          return [];
        }

        // Extract unique values
        const uniqueValues = new Set(
          data
            .map((item: any) => item[field])
            .filter((value: any) => value && typeof value === 'string')
        );
        const suggestions = Array.from(uniqueValues) as string[];

        return suggestions.slice(0, limit);
      },
      cacheInstance,
      10 * 60 * 1000 // 10 minutes TTL for suggestions
    );
  }

  /**
   * Get faceted search results (aggregations)
   */
  async getFacets(
    configKey: string,
    field: string,
    filters: Record<string, any> = {}
  ): Promise<Array<{ value: string; count: number }>> {
    const config = SEARCH_CONFIGS[configKey];
    if (!config || !config.filterableFields.includes(field)) {
      return [];
    }

    const cacheKey = `facets_${configKey}_${field}_${JSON.stringify(filters)}`;
    const cacheInstance = this.getCacheInstance(configKey);

    return await withCache(
      cacheKey,
      async () => {
        let queryBuilder = (this.supabase as any)
          .from(config.table)
          .select(field)
          .not(field, 'is', null);

        // Apply existing filters (excluding the facet field)
        Object.entries(filters).forEach(([filterField, value]) => {
          if (filterField !== field && config.filterableFields.includes(filterField) && value !== undefined) {
            if (Array.isArray(value)) {
              queryBuilder = queryBuilder.in(filterField, value);
            } else {
              queryBuilder = queryBuilder.eq(filterField, value);
            }
          }
        });

        const { data, error } = await queryBuilder;

        if (error) {
          console.error('Facets query failed:', error);
          return [];
        }

        // Count occurrences
        const counts: Record<string, number> = {};
        data.forEach((item: any) => {
          const value = item[field];
          if (value) {
            counts[value] = (counts[value] || 0) + 1;
          }
        });

        // Convert to array and sort by count
        return Object.entries(counts)
          .map(([value, count]) => ({ value, count }))
          .sort((a, b) => b.count - a.count);
      },
      cacheInstance,
      15 * 60 * 1000 // 15 minutes TTL for facets
    );
  }

  /**
   * Generate cache key for search parameters
   */
  private generateCacheKey(configKey: string, params: SearchParams): string {
    // Create a stable, collision-resistant cache key
    const keyData = {
      config: configKey,
      query: params.query || '',
      filters: params.filters || {},
      sort: params.sort || {},
      page: params.page || 1,
      limit: params.limit || 20,
      includeCount: params.includeCount || false,
    };

    // Use base64 encoding of JSON to avoid delimiter conflicts
    const keyString = Buffer.from(JSON.stringify(keyData)).toString('base64');
    return `search_${configKey}_${keyString}`;
  }

  /**
   * Get appropriate cache instance for config
   */
  private getCacheInstance(configKey: string) {
    const cacheMap: Record<string, any> = {
      'trips': cacheInstances.trips,
      'blog_posts': cacheInstances.blogPosts,
      'galleries': cacheInstances.galleries,
      'inquiries': cacheInstances.adminDashboard, // Use admin dashboard cache for inquiries
      'trip_photos': cacheInstances.trips, // Use trips cache for trip photos
      'public_trips': cacheInstances.publicTrips,
      'public_blog': cacheInstances.publicBlog,
      'public_galleries': cacheInstances.publicGalleries,
    };

    return cacheMap[configKey] || cacheInstances.trips; // Default fallback
  }

  /**
   * Clear search cache for a specific configuration
   */
  clearCache(configKey: string): void {
    const cacheInstance = this.getCacheInstance(configKey);
    const stats = cacheInstance.getStats();
    
    let cleared = 0;
    stats.entries.forEach((entry: { key: string }) => {
      if (entry.key.includes(`search_${configKey}`) ||
          entry.key.includes(`suggestions_${configKey}`) ||
          entry.key.includes(`facets_${configKey}`)) {
        cacheInstance.delete(entry.key);
        cleared++;
      }
    });
    
    console.log(`Cleared ${cleared} search cache entries for ${configKey}`);
  }
}

// Singleton instance
export const advancedSearch = new AdvancedSearch();
