import bcrypt from 'bcryptjs';
import crypto from 'crypto';
import { sanitizeFilename } from './input-sanitization';

/**
 * Security utilities for password hashing, token encryption, and validation
 */

// Encryption configuration
const ENCRYPTION_KEY = process.env.ENCRYPTION_KEY;

// Validate encryption key (only at runtime, not during build)
function validateEncryptionKey() {
  if (!ENCRYPTION_KEY) {
    if (process.env.NODE_ENV === 'production' && typeof window === 'undefined') {
      // Only throw in production server runtime, not during build
      throw new Error('ENCRYPTION_KEY environment variable must be set in production');
    }
    if (process.env.NODE_ENV !== 'production') {
      console.warn('Warning: ENCRYPTION_KEY environment variable not set. Using development fallback.');
    }
  }
}

/**
 * Hash a password using bcrypt
 * @param password - Plain text password
 * @returns Promise<string> - Hashed password
 */
export async function hashPassword(password: string): Promise<string> {
  try {
    const saltRounds = 12;
    return await bcrypt.hash(password, saltRounds);
  } catch (error) {
    console.error('Error hashing password:', error);
    throw new Error('Failed to hash password');
  }
}

/**
 * Verify a password against its hash
 * @param password - Plain text password
 * @param hash - Hashed password
 * @returns Promise<boolean> - True if password matches
 */
export async function verifyPassword(password: string, hash: string): Promise<boolean> {
  try {
    return await bcrypt.compare(password, hash);
  } catch (error) {
    console.error('Error verifying password:', error);
    return false;
  }
}

/**
 * Encrypt sensitive data using authenticated encryption (AES-256-GCM)
 * @param text - Text to encrypt
 * @param key - Optional encryption key (uses env var if not provided)
 * @returns string - Encrypted text with IV and auth tag
 */
export function encrypt(text: string, key?: string): string {
  try {
    validateEncryptionKey();
    const encryptionKey = key || ENCRYPTION_KEY;

    if (!encryptionKey) {
      throw new Error('Encryption key is required');
    }

    // Create a 32-byte key from the provided key
    const keyBuffer = crypto.createHash('sha256').update(encryptionKey).digest();

    // Generate a random IV for each encryption
    const iv = crypto.randomBytes(12); // 12 bytes for GCM

    // Use AES-256-GCM for authenticated encryption
    const cipher = crypto.createCipheriv('aes-256-gcm', keyBuffer, iv);

    let encrypted = cipher.update(text, 'utf8', 'hex');
    encrypted += cipher.final('hex');

    // Get the authentication tag
    const authTag = cipher.getAuthTag();

    // Combine IV, auth tag, and encrypted text
    return iv.toString('hex') + ':' + authTag.toString('hex') + ':' + encrypted;
  } catch (error) {
    console.error('Error encrypting data:', error);
    throw new Error('Failed to encrypt data');
  }
}

/**
 * Decrypt sensitive data using authenticated decryption (AES-256-GCM)
 * Includes backward compatibility for old CBC format
 * @param encryptedText - Encrypted text with IV and auth tag (or old CBC format)
 * @param key - Optional encryption key (uses env var if not provided)
 * @returns string - Decrypted text
 */
export function decrypt(encryptedText: string, key?: string): string {
  try {
    validateEncryptionKey();
    const encryptionKey = key || ENCRYPTION_KEY;

    if (!encryptionKey) {
      throw new Error('Encryption key is required');
    }

    // Split the encrypted text
    const parts = encryptedText.split(':');

    // Check if this is the new GCM format (3 parts: IV:authTag:encrypted)
    if (parts.length === 3) {
      const iv = Buffer.from(parts[0], 'hex');
      const authTag = Buffer.from(parts[1], 'hex');
      const encrypted = parts[2];

      // Create a 32-byte key from the provided key
      const keyBuffer = crypto.createHash('sha256').update(encryptionKey).digest();

      // Use AES-256-GCM for authenticated decryption
      const decipher = crypto.createDecipheriv('aes-256-gcm', keyBuffer, iv);
      decipher.setAuthTag(authTag);

      let decrypted = decipher.update(encrypted, 'hex', 'utf8');
      decrypted += decipher.final('utf8');

      return decrypted;
    }
    // Check if this is the old CBC format (2 parts: IV:encrypted)
    else if (parts.length === 2) {
      console.warn('Decrypting data using legacy CBC format. Consider re-encrypting with new format.');

      const iv = Buffer.from(parts[0], 'hex');
      const encrypted = parts[1];

      // Create a 32-byte key from the provided key
      const keyBuffer = crypto.createHash('sha256').update(encryptionKey).digest();

      // Use legacy AES-256-CBC for backward compatibility
      const decipher = crypto.createDecipheriv('aes-256-cbc', keyBuffer, iv);

      let decrypted = decipher.update(encrypted, 'hex', 'utf8');
      decrypted += decipher.final('utf8');

      return decrypted;
    }
    else {
      throw new Error('Invalid encrypted text format');
    }
  } catch (error) {
    console.error('Error decrypting data:', error);
    if (error instanceof Error && error.message.includes('Invalid encrypted text format')) {
      throw error;
    }
    throw new Error('Failed to decrypt data');
  }
}

/**
 * Generate a secure random token
 * @param length - Token length in bytes (default: 32)
 * @returns string - Random token in hex format
 */
export function generateSecureToken(length: number = 32): string {
  return crypto.randomBytes(length).toString('hex');
}

/**
 * Validate password strength
 * @param password - Password to validate
 * @returns object - Validation result with strength score and requirements
 */
export function validatePasswordStrength(password: string): {
  isValid: boolean;
  score: number;
  requirements: {
    minLength: boolean;
    hasUppercase: boolean;
    hasLowercase: boolean;
    hasNumbers: boolean;
    hasSpecialChars: boolean;
  };
  suggestions: string[];
} {
  const requirements = {
    minLength: password.length >= 8,
    hasUppercase: /[A-Z]/.test(password),
    hasLowercase: /[a-z]/.test(password),
    hasNumbers: /\d/.test(password),
    hasSpecialChars: /[!@#$%^&*()_+\-=[\]{};':"\\|,.<>/?]/.test(password),
  };

  const score = Object.values(requirements).filter(Boolean).length;
  const isValid = score >= 4; // At least 4 out of 5 requirements

  const suggestions: string[] = [];
  if (!requirements.minLength) suggestions.push('Use at least 8 characters');
  if (!requirements.hasUppercase) suggestions.push('Include uppercase letters');
  if (!requirements.hasLowercase) suggestions.push('Include lowercase letters');
  if (!requirements.hasNumbers) suggestions.push('Include numbers');
  if (!requirements.hasSpecialChars) suggestions.push('Include special characters');

  return {
    isValid,
    score,
    requirements,
    suggestions,
  };
}

/**
 * Sanitize input to prevent XSS attacks
 * @param input - User input to sanitize
 * @returns string - Sanitized input
 */
export function sanitizeInput(input: string): string {
  if (typeof input !== 'string') return '';

  return input
    .replace(/<[^>]*>/g, '') // Remove HTML tags
    .replace(/javascript:/gi, '') // Remove javascript: protocol
    .replace(/on\w+=/gi, '') // Remove event handlers
    .trim();
}

/**
 * Sanitize HTML content for safe display
 * @param html - HTML content to sanitize
 * @returns string - Sanitized HTML
 */
export function sanitizeHtml(html: string): string {
  if (typeof html !== 'string') return '';

  // Basic HTML sanitization - remove dangerous tags and attributes
  return html
    .replace(/<script[^>]*>.*?<\/script>/gi, '') // Remove script tags
    .replace(/<iframe[^>]*>.*?<\/iframe>/gi, '') // Remove iframe tags
    .replace(/<object[^>]*>.*?<\/object>/gi, '') // Remove object tags
    .replace(/<embed[^>]*>/gi, '') // Remove embed tags
    .replace(/on\w+\s*=\s*["'][^"']*["']/gi, '') // Remove event handlers
    .replace(/javascript:/gi, '') // Remove javascript: protocol
    .replace(/vbscript:/gi, '') // Remove vbscript: protocol
    .replace(/data:/gi, '') // Remove data: protocol
    .trim();
}

/**
 * Rate limiting configuration for different endpoints
 */
export const RATE_LIMITS = {
  // Authentication endpoints
  auth: {
    windowMs: 15 * 60 * 1000, // 15 minutes
    max: 5, // 5 attempts per window
    message: 'Too many authentication attempts, please try again later.',
  },
  
  // General API endpoints
  api: {
    windowMs: 15 * 60 * 1000, // 15 minutes
    max: 100, // 100 requests per window
    message: 'Too many requests, please try again later.',
  },
  
  // Admin file upload endpoints - Very generous for batch operations
  adminUpload: {
    windowMs: 60 * 60 * 1000, // 1 hour
    max: 1000, // 1000 uploads per hour for admins (batch gallery uploads)
    message: 'Admin upload limit reached, please try again later.',
  },

  // Public file upload endpoints - More restrictive
  publicUpload: {
    windowMs: 60 * 60 * 1000, // 1 hour
    max: 50, // 50 uploads per hour for public users
    message: 'Too many upload attempts, please try again later.',
  },
  
  // Password verification
  passwordVerify: {
    windowMs: 15 * 60 * 1000, // 15 minutes
    max: 10, // 10 attempts per window
    message: 'Too many password attempts, please try again later.',
  },
} as const;

/**
 * Validate and sanitize file uploads with enhanced security
 * @param file - File object to validate
 * @param options - Validation options
 * @returns object - Validation result
 */
export function validateFileUpload(
  file: File,
  options: {
    maxSize?: number; // in bytes
    allowedTypes?: string[];
    allowedExtensions?: string[];
    checkMagicBytes?: boolean;
  } = {}
): {
  isValid: boolean;
  errors: string[];
  sanitizedName: string;
} {
  const errors: string[] = [];

  const {
    maxSize = 10 * 1024 * 1024, // 10MB default
    allowedTypes = ['image/jpeg', 'image/png', 'image/webp', 'image/gif'],
    allowedExtensions = ['.jpg', '.jpeg', '.png', '.webp', '.gif'],
    checkMagicBytes = true,
  } = options;

  // Sanitize filename
  const sanitizedName = sanitizeFilename(file.name);

  // Check file size
  if (file.size === 0) {
    errors.push('File is empty');
  } else if (file.size > maxSize) {
    errors.push(`File size must be less than ${Math.round(maxSize / 1024 / 1024)}MB`);
  }

  // Check file name length
  if (file.name.length > 255) {
    errors.push('Filename is too long');
  }

  // Check for dangerous file names
  const dangerousPatterns = [
    /^(con|prn|aux|nul|com[1-9]|lpt[1-9])$/i,
    /\.(exe|bat|cmd|scr|pif|com|vbs|js|jar|app|dmg)$/i,
    /[<>:"|?*]/,
    /^\./,
    /\.$/
  ];

  if (dangerousPatterns.some(pattern => pattern.test(file.name))) {
    errors.push('Filename contains invalid characters or is a reserved name');
  }

  // Check file type
  if (!allowedTypes.includes(file.type)) {
    errors.push(`File type ${file.type} is not allowed`);
  }

  // Check file extension
  const extension = '.' + file.name.split('.').pop()?.toLowerCase();
  if (!allowedExtensions.includes(extension)) {
    errors.push(`File extension ${extension} is not allowed`);
  }

  // Validate MIME type matches extension
  const mimeExtensionMap: Record<string, string[]> = {
    'image/jpeg': ['.jpg', '.jpeg'],
    'image/png': ['.png'],
    'image/gif': ['.gif'],
    'image/webp': ['.webp'],
  };

  if (mimeExtensionMap[file.type] && !mimeExtensionMap[file.type].includes(extension)) {
    errors.push('File extension does not match file type');
  }

  // Check for suspicious file names
  if (file.name.includes('..') || file.name.includes('/') || file.name.includes('\\')) {
    errors.push('Invalid file name');
  }

  return {
    isValid: errors.length === 0,
    errors,
    sanitizedName,
  };
}

/**
 * Validate file content by checking magic bytes (file signatures)
 * @param file - File to validate
 * @returns Promise<boolean> - Whether file content matches expected type
 */
export async function validateFileContent(file: File): Promise<boolean> {
  return new Promise((resolve) => {
    const reader = new FileReader();

    reader.onload = () => {
      const arrayBuffer = reader.result as ArrayBuffer;
      const bytes = new Uint8Array(arrayBuffer);

      // Check magic bytes for common image formats
      const signatures = {
        jpeg: [0xFF, 0xD8, 0xFF],
        png: [0x89, 0x50, 0x4E, 0x47],
        gif: [0x47, 0x49, 0x46],
        webp: [0x52, 0x49, 0x46, 0x46], // RIFF header for WebP
      };

      // Check if file starts with any valid image signature
      const isValidImage = Object.values(signatures).some(signature => {
        return signature.every((byte, index) => bytes[index] === byte);
      });

      resolve(isValidImage);
    };

    reader.onerror = () => resolve(false);

    // Read only the first 12 bytes to check signatures
    reader.readAsArrayBuffer(file.slice(0, 12));
  });
}

/**
 * Generate CSRF token
 * @returns string - CSRF token
 */
export function generateCSRFToken(): string {
  return generateSecureToken(32);
}

/**
 * Verify CSRF token
 * @param token - Token to verify
 * @param sessionToken - Token stored in session
 * @returns boolean - True if tokens match
 */
export function verifyCSRFToken(token: string, sessionToken: string): boolean {
  try {
    const tokenBuffer = Buffer.from(token, 'hex');
    const sessionBuffer = Buffer.from(sessionToken, 'hex');

    // Check if lengths match before using timingSafeEqual
    if (tokenBuffer.length !== sessionBuffer.length) {
      return false;
    }

    return crypto.timingSafeEqual(tokenBuffer, sessionBuffer);
  } catch (error) {
    // Handle invalid hex strings or other errors
    return false;
  }
}
