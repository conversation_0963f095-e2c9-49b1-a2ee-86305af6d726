/**
 * Component Registry and Caching Strategy
 * 
 * This file manages all components in the application with proper caching strategies
 * to ensure smooth operation and prevent hydration mismatches.
 */

import { ComponentType, ReactNode } from 'react';

// Component types for better organization
export type ComponentCategory = 
  | 'provider' 
  | 'error-handling' 
  | 'security' 
  | 'performance' 
  | 'ui' 
  | 'analytics'
  | 'accessibility';

export interface ComponentConfig {
  name: string;
  category: ComponentCategory;
  isClientOnly: boolean;
  requiresAuth: boolean;
  cacheable: boolean;
  priority: number; // Lower number = higher priority
  dependencies?: string[];
}

// Component registry with caching strategies
export const COMPONENT_REGISTRY: Record<string, ComponentConfig> = {
  // Core Providers (Highest Priority)
  'ErrorBoundary': {
    name: 'ErrorBoundary',
    category: 'error-handling',
    isClientOnly: false,
    requiresAuth: false,
    cacheable: false,
    priority: 1,
  },
  'QueryProvider': {
    name: 'QueryProvider',
    category: 'provider',
    isClientOnly: true,
    requiresAuth: false,
    cacheable: true,
    priority: 2,
  },
  'AuthProviderWrapper': {
    name: 'AuthProviderWrapper',
    category: 'provider',
    isClientOnly: true,
    requiresAuth: false,
    cacheable: true,
    priority: 3,
  },
  'SplashProvider': {
    name: 'SplashProvider',
    category: 'provider',
    isClientOnly: true,
    requiresAuth: false,
    cacheable: true,
    priority: 4,
  },
  'ToastProvider': {
    name: 'ToastProvider',
    category: 'provider',
    isClientOnly: true,
    requiresAuth: false,
    cacheable: true,
    priority: 5,
  },

  // Error Handling & Monitoring
  'WebpackErrorLogger': {
    name: 'WebpackErrorLogger',
    category: 'error-handling',
    isClientOnly: true,
    requiresAuth: false,
    cacheable: false,
    priority: 6,
  },
  'ErrorMonitor': {
    name: 'ErrorMonitor',
    category: 'error-handling',
    isClientOnly: true,
    requiresAuth: false,
    cacheable: false,
    priority: 7,
  },

  // Security Components
  'SecurityHeaders': {
    name: 'SecurityHeaders',
    category: 'security',
    isClientOnly: false,
    requiresAuth: false,
    cacheable: true,
    priority: 8,
  },
  'SecurityStatus': {
    name: 'SecurityStatus',
    category: 'security',
    isClientOnly: true,
    requiresAuth: false,
    cacheable: true,
    priority: 15,
  },

  // Accessibility
  'SkipToContent': {
    name: 'SkipToContent',
    category: 'accessibility',
    isClientOnly: false,
    requiresAuth: false,
    cacheable: true,
    priority: 9,
  },
  'AccessibilityToolbar': {
    name: 'AccessibilityToolbar',
    category: 'accessibility',
    isClientOnly: true,
    requiresAuth: false,
    cacheable: true,
    priority: 16,
  },
  'AriaLiveRegion': {
    name: 'AriaLiveRegion',
    category: 'accessibility',
    isClientOnly: false,
    requiresAuth: false,
    cacheable: true,
    priority: 17,
  },

  // UI Components
  'FloatingActionButton': {
    name: 'FloatingActionButton',
    category: 'ui',
    isClientOnly: true,
    requiresAuth: false,
    cacheable: true,
    priority: 10,
  },

  // Performance
  'PerformanceBudget': {
    name: 'PerformanceBudget',
    category: 'performance',
    isClientOnly: true,
    requiresAuth: false,
    cacheable: true,
    priority: 18,
  },

  // Analytics (Lowest Priority)
  'Analytics': {
    name: 'Analytics',
    category: 'analytics',
    isClientOnly: true,
    requiresAuth: false,
    cacheable: true,
    priority: 19,
  },
  'SpeedInsights': {
    name: 'SpeedInsights',
    category: 'analytics',
    isClientOnly: true,
    requiresAuth: false,
    cacheable: true,
    priority: 20,
  },
};

// Cache configuration
export const CACHE_CONFIG = {
  // Component cache TTL (in milliseconds)
  COMPONENT_CACHE_TTL: 5 * 60 * 1000, // 5 minutes
  
  // Service Worker cache names
  SW_CACHE_NAMES: {
    STATIC: 'positive7-static-v4.0.0',
    DYNAMIC: 'positive7-dynamic-v4.0.0',
    IMAGES: 'positive7-images-v4.0.0',
    API: 'positive7-api-v4.0.0',
  },
  
  // Cache strategies
  STRATEGIES: {
    CACHE_FIRST: 'cache-first',
    NETWORK_FIRST: 'network-first',
    STALE_WHILE_REVALIDATE: 'stale-while-revalidate',
    NETWORK_ONLY: 'network-only',
    CACHE_ONLY: 'cache-only',
  },
};

// Component loading utilities
export function getComponentsByCategory(category: ComponentCategory): ComponentConfig[] {
  return Object.values(COMPONENT_REGISTRY)
    .filter(config => config.category === category)
    .sort((a, b) => a.priority - b.priority);
}

export function getComponentsByPriority(): ComponentConfig[] {
  return Object.values(COMPONENT_REGISTRY)
    .sort((a, b) => a.priority - b.priority);
}

export function isClientOnlyComponent(componentName: string): boolean {
  return COMPONENT_REGISTRY[componentName]?.isClientOnly ?? false;
}

export function isCacheableComponent(componentName: string): boolean {
  return COMPONENT_REGISTRY[componentName]?.cacheable ?? false;
}

// Cache key generators
export function generateCacheKey(componentName: string, props?: any): string {
  const baseKey = `component:${componentName}`;
  if (!props) return baseKey;
  
  const propsHash = JSON.stringify(props);
  return `${baseKey}:${btoa(propsHash)}`;
}

// Component wrapper for caching
export function withCaching<P extends object>(
  Component: ComponentType<P>,
  config: ComponentConfig
): ComponentType<P> {
  if (!config.cacheable) {
    return Component;
  }

  // Return the component type for caching - actual implementation would be in a React component file
  return Component;
}
