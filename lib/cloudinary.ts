// For server-side operations, we need the Node.js SDK
let cloudinary: any = null;

// Initialize Cloudinary on server-side
async function getCloudinary() {
  if (typeof window !== 'undefined') {
    throw new Error('Cloudinary should only be used on server-side');
  }

  if (!cloudinary) {
    try {
      const cloudinaryModule = await import('cloudinary');
      cloudinary = cloudinaryModule.v2;

      // Configure Cloudinary
      cloudinary.config({
        cloud_name: process.env.NEXT_PUBLIC_CLOUDINARY_CLOUD_NAME,
        api_key: process.env.NEXT_PUBLIC_CLOUDINARY_API_KEY,
        api_secret: process.env.CLOUDINARY_API_SECRET,
        secure: true,
      });
    } catch (error) {
      console.warn('Cloudinary not available:', error instanceof Error ? error.message : 'Unknown error');
      throw error;
    }
  }

  return cloudinary;
}

export default getCloudinary;

// Upload image to Cloudinary
export async function uploadToCloudinary(
  file: any, // Use any to handle File objects from FormData
  options: {
    folder?: string;
    public_id?: string;
    transformation?: any;
    resource_type?: 'image' | 'video' | 'raw' | 'auto';
    format?: string;
    quality?: string | number;
  } = {}
) {
  try {
    const cloudinaryModule = await import('cloudinary');
    const cloudinary = cloudinaryModule.v2;

    // Configure Cloudinary
    cloudinary.config({
      cloud_name: process.env.NEXT_PUBLIC_CLOUDINARY_CLOUD_NAME,
      api_key: process.env.NEXT_PUBLIC_CLOUDINARY_API_KEY,
      api_secret: process.env.CLOUDINARY_API_SECRET,
      secure: true,
    });

    const uploadOptions = {
      folder: options.folder || 'positive7',
      resource_type: options.resource_type || 'auto',
      quality: options.quality || 'auto',
      ...options,
    };

    // Remove format: 'auto' as it's not valid
    if (uploadOptions.format === 'auto') {
      delete uploadOptions.format;
    }

    let result;

    // Check if it's a File-like object (has arrayBuffer method)
    if (file && typeof file.arrayBuffer === 'function' && file.type) {
      // Convert File to buffer
      const arrayBuffer = await file.arrayBuffer();
      const buffer = Buffer.from(arrayBuffer);
      result = await cloudinary.uploader.upload(
        `data:${file.type};base64,${buffer.toString('base64')}`,
        uploadOptions
      );
    } else if (Buffer.isBuffer(file)) {
      // Upload buffer directly
      result = await cloudinary.uploader.upload(
        `data:image/jpeg;base64,${file.toString('base64')}`,
        uploadOptions
      );
    } else if (typeof file === 'string') {
      // Upload from URL or file path
      result = await cloudinary.uploader.upload(file, uploadOptions);
    } else {
      throw new Error('Invalid file type provided');
    }

    return {
      success: true,
      url: result.secure_url,
      public_id: result.public_id,
      width: result.width,
      height: result.height,
      format: result.format,
      bytes: result.bytes,
      version: result.version,
      resource_type: result.resource_type,
    };
  } catch (error) {
    console.error('Cloudinary upload error:', error);
    return {
      success: false,
      error: error instanceof Error ? error.message : 'Upload failed',
    };
  }
}

// Delete image from Cloudinary with enhanced logging
export async function deleteFromCloudinary(
  publicId: string,
  resourceType: 'image' | 'video' | 'raw' = 'image',
  context?: string // Additional context for logging (e.g., 'trip-delete', 'blog-replace', 'incomplete-cleanup')
) {
  const startTime = Date.now();
  const logContext = context ? `[${context}]` : '';

  console.log(`${logContext} Starting Cloudinary deletion for publicId: ${publicId}, resourceType: ${resourceType}`);

  try {
    const cloudinaryModule = await import('cloudinary');
    const cloudinary = cloudinaryModule.v2;

    cloudinary.config({
      cloud_name: process.env.NEXT_PUBLIC_CLOUDINARY_CLOUD_NAME,
      api_key: process.env.NEXT_PUBLIC_CLOUDINARY_API_KEY,
      api_secret: process.env.CLOUDINARY_API_SECRET,
      secure: true,
    });

    const result = await cloudinary.uploader.destroy(publicId, {
      resource_type: resourceType,
    });

    const duration = Date.now() - startTime;
    const success = result.result === 'ok';

    if (success) {
      console.log(`${logContext} ✅ Successfully deleted from Cloudinary: ${publicId} (${duration}ms)`);
    } else {
      console.warn(`${logContext} ⚠️ Cloudinary deletion returned: ${result.result} for ${publicId} (${duration}ms)`);
    }

    return {
      success,
      result: result.result,
      duration,
    };
  } catch (error) {
    const duration = Date.now() - startTime;
    console.error(`${logContext} ❌ Cloudinary delete error for ${publicId} (${duration}ms):`, error);
    return {
      success: false,
      error: error instanceof Error ? error.message : 'Delete failed',
      duration,
    };
  }
}

// Generate optimized image URL
export function getCloudinaryUrl(
  publicId: string,
  options: {
    width?: number;
    height?: number;
    crop?: string;
    quality?: string | number;
    format?: string;
    gravity?: string;
    transformation?: string[];
  } = {}
) {
  const transformations = [];
  
  if (options.width) transformations.push(`w_${options.width}`);
  if (options.height) transformations.push(`h_${options.height}`);
  if (options.crop) transformations.push(`c_${options.crop}`);
  if (options.quality) transformations.push(`q_${options.quality}`);
  if (options.format) transformations.push(`f_${options.format}`);
  if (options.gravity) transformations.push(`g_${options.gravity}`);
  if (options.transformation) transformations.push(...options.transformation);
  
  const transformationString = transformations.length > 0 ? `/${transformations.join(',')}` : '';
  
  return `https://res.cloudinary.com/${process.env.NEXT_PUBLIC_CLOUDINARY_CLOUD_NAME}/image/upload${transformationString}/${publicId}`;
}

// Get image info from Cloudinary
export async function getCloudinaryImageInfo(publicId: string) {
  try {
    const cloudinary = await getCloudinary();
    const result = await cloudinary.api.resource(publicId);
    return {
      success: true,
      data: {
        public_id: result.public_id,
        url: result.secure_url,
        width: result.width,
        height: result.height,
        format: result.format,
        bytes: result.bytes,
        created_at: result.created_at,
        version: result.version,
      },
    };
  } catch (error) {
    console.error('Cloudinary info error:', error);
    return {
      success: false,
      error: error instanceof Error ? error.message : 'Failed to get image info',
    };
  }
}

// List images in a folder
export async function listCloudinaryImages(folder: string = 'positive7', maxResults: number = 100) {
  try {
    const cloudinary = await getCloudinary();
    const result = await cloudinary.api.resources({
      type: 'upload',
      prefix: folder,
      max_results: maxResults,
      resource_type: 'image',
    });
    
    return {
      success: true,
      images: result.resources.map((resource: any) => ({
        public_id: resource.public_id,
        url: resource.secure_url,
        width: resource.width,
        height: resource.height,
        format: resource.format,
        bytes: resource.bytes,
        created_at: resource.created_at,
      })),
      total_count: result.total_count,
    };
  } catch (error) {
    console.error('Cloudinary list error:', error);
    return {
      success: false,
      error: error instanceof Error ? error.message : 'Failed to list images',
    };
  }
}

// Delete folder from Cloudinary with pagination support for large galleries
export async function deleteCloudinaryFolder(folderPath: string) {
  try {
    const cloudinaryModule = await import('cloudinary');
    const cloudinary = cloudinaryModule.v2;

    cloudinary.config({
      cloud_name: process.env.NEXT_PUBLIC_CLOUDINARY_CLOUD_NAME,
      api_key: process.env.NEXT_PUBLIC_CLOUDINARY_API_KEY,
      api_secret: process.env.CLOUDINARY_API_SECRET,
      secure: true,
    });

    let totalDeleted = 0;
    let nextCursor: string | undefined;
    const deletionPromises: Promise<any>[] = [];

    // Paginate through all resources in the folder
    do {
      const resources = await cloudinary.api.resources({
        type: 'upload',
        prefix: folderPath,
        max_results: 500, // Cloudinary's max
        next_cursor: nextCursor,
      });

      // Parallelize deletions for better performance
      if (resources.resources && resources.resources.length > 0) {
        const publicIds = resources.resources.map((resource: any) => resource.public_id);

        // Split into chunks of 100 (Cloudinary's delete limit)
        const chunks = [];
        for (let i = 0; i < publicIds.length; i += 100) {
          chunks.push(publicIds.slice(i, i + 100));
        }

        // Create deletion promises for each chunk
        for (const chunk of chunks) {
          deletionPromises.push(cloudinary.api.delete_resources(chunk));
        }

        totalDeleted += resources.resources.length;
      }

      nextCursor = resources.next_cursor;
    } while (nextCursor);

    // Wait for all deletions to complete
    await Promise.all(deletionPromises);

    // Try to delete the folder itself (this may not always work if there are subfolders)
    try {
      await cloudinary.api.delete_folder(folderPath);
    } catch (folderError) {
      console.warn('Could not delete folder (may have subfolders):', folderError);
    }

    return {
      success: true,
      deletedCount: totalDeleted,
    };
  } catch (error) {
    console.error('Cloudinary folder delete error:', error);
    return {
      success: false,
      error: error instanceof Error ? error.message : 'Delete failed',
    };
  }
}

// List all folders in Cloudinary with pagination and sub-folder handling
export async function listCloudinaryFolders(
  prefix: string = 'positive7/galleries',
  options: {
    maxResults?: number;
    nextCursor?: string;
    includeSubfolders?: boolean;
  } = {}
) {
  try {
    const cloudinaryModule = await import('cloudinary');
    const cloudinary = cloudinaryModule.v2;

    cloudinary.config({
      cloud_name: process.env.NEXT_PUBLIC_CLOUDINARY_CLOUD_NAME,
      api_key: process.env.NEXT_PUBLIC_CLOUDINARY_API_KEY,
      api_secret: process.env.CLOUDINARY_API_SECRET,
      secure: true,
    });

    const { maxResults = 500, nextCursor, includeSubfolders = false } = options;

    const result = await cloudinary.api.sub_folders(prefix, {
      max_results: maxResults,
      next_cursor: nextCursor,
    });

    let allFolders = result.folders.map((folder: any) => ({
      name: folder.name,
      path: folder.path,
    }));

    // Recursively get subfolders if requested
    if (includeSubfolders) {
      const subfolderPromises = result.folders.map(async (folder: any) => {
        try {
          const subResult = await listCloudinaryFolders(folder.path, {
            maxResults,
            includeSubfolders: true,
          });
          return subResult.success ? subResult.folders : [];
        } catch {
          return [];
        }
      });

      const subfolders = await Promise.all(subfolderPromises);
      allFolders = allFolders.concat(subfolders.flat());
    }

    return {
      success: true,
      folders: allFolders,
      nextCursor: result.next_cursor,
      totalCount: result.total_count,
    };
  } catch (error) {
    console.error('Cloudinary list folders error:', error);
    return {
      success: false,
      error: error instanceof Error ? error.message : 'Failed to list folders',
      folders: [],
    };
  }
}

// Generate upload signature for client-side uploads
export async function generateUploadSignature(params: Record<string, any>) {
  const cloudinaryModule = await import('cloudinary');
  const cloudinary = cloudinaryModule.v2;

  cloudinary.config({
    cloud_name: process.env.NEXT_PUBLIC_CLOUDINARY_CLOUD_NAME,
    api_key: process.env.NEXT_PUBLIC_CLOUDINARY_API_KEY,
    api_secret: process.env.CLOUDINARY_API_SECRET,
    secure: true,
  });
  const timestamp = Math.round(new Date().getTime() / 1000);
  const paramsToSign = {
    timestamp,
    ...params,
  };

  const signature = cloudinary.utils.api_sign_request(
    paramsToSign,
    process.env.CLOUDINARY_API_SECRET!
  );

  return {
    signature,
    timestamp,
    api_key: process.env.NEXT_PUBLIC_CLOUDINARY_API_KEY,
    cloud_name: process.env.NEXT_PUBLIC_CLOUDINARY_CLOUD_NAME,
  };
}

// Cloudinary transformation presets
export const CLOUDINARY_PRESETS = {
  thumbnail: {
    width: 300,
    height: 300,
    crop: 'fill',
    gravity: 'auto',
    quality: 'auto',
    format: 'auto',
  },
  hero: {
    width: 1920,
    height: 1080,
    crop: 'fill',
    gravity: 'auto',
    quality: 'auto',
    format: 'auto',
  },
  card: {
    width: 600,
    height: 400,
    crop: 'fill',
    gravity: 'auto',
    quality: 'auto',
    format: 'auto',
  },
  avatar: {
    width: 200,
    height: 200,
    crop: 'fill',
    gravity: 'face',
    quality: 'auto',
    format: 'auto',
  },
} as const;

export type CloudinaryPreset = keyof typeof CLOUDINARY_PRESETS;

// Enhanced cleanup function for immediate deletion with folder cleanup
export async function cleanupSingleImage(publicId: string, context: string = 'cleanup', shouldCleanupFolder: boolean = false) {
  console.log(`🧹 Cleaning up single image: ${publicId} (${context})`);

  try {
    // Extract folder path from publicId for potential folder cleanup
    const folderPath = publicId.substring(0, publicId.lastIndexOf('/'));

    const result = await deleteFromCloudinary(publicId, 'image', context);

    if (result.success) {
      console.log(`✅ Successfully cleaned up image: ${publicId}`);

      // Enhanced folder cleanup logic for various contexts
      if (shouldCleanupFolder && folderPath) {
        console.log(`📁 Checking if folder should be cleaned up: ${folderPath} (context: ${context})`);

        // Define contexts that should trigger folder cleanup
        const folderCleanupContexts = [
          'form-cancelled',
          'user-removed',
          'image-replaced',
          'page-unload',
          'component-unmount',
          'form-cancelled-delayed'
        ];

        if (folderCleanupContexts.includes(context)) {
          // Check if this looks like an individual entity folder that's safe to delete
          // Use more precise regex patterns for better accuracy and safety
          const isIndividualEntityFolder = folderPath.includes('/new') ||
                                         folderPath.includes('-featured') ||
                                         folderPath.includes('/untitled-') ||
                                         /\/(trip|blog|trips|gallery|team)-[\w-]+$/.test(folderPath) ||
                                         /\/[a-f0-9]{8}-[a-f0-9]{4}-[a-f0-9]{4}-[a-f0-9]{4}-[a-f0-9]{12}$/.test(folderPath) || // Precise UUID pattern
                                         /\/temp-[\w-]+$/.test(folderPath); // Temp folders with word boundaries

          if (isIndividualEntityFolder) {
            console.log(`🗂️ Attempting to cleanup individual entity folder: ${folderPath}`);

            try {
              const folderResult = await deleteCloudinaryFolder(folderPath);
              if (folderResult.success) {
                console.log(`✅ Successfully cleaned up folder: ${folderPath} (${folderResult.deletedCount} items)`);
              } else {
                console.warn(`⚠️ Could not cleanup folder: ${folderPath} - ${folderResult.error}`);
              }
            } catch (folderError) {
              console.error(`❌ Error during folder cleanup: ${folderPath}`, folderError);
            }
          } else {
            console.log(`📁 Skipping folder cleanup for shared folder: ${folderPath}`);
          }
        } else {
          console.log(`📁 Skipping folder cleanup for context: ${context}`);
        }
      }

      return { success: true };
    } else {
      console.error(`❌ Failed to cleanup: ${publicId} - ${result.error}`);
      return { success: false, error: result.error };
    }
  } catch (error) {
    const errorMsg = error instanceof Error ? error.message : 'Unknown error';
    console.error(`❌ Error cleaning up ${publicId}: ${errorMsg}`);
    return { success: false, error: errorMsg };
  }
}

// Extract Cloudinary public_id from URL
export function extractCloudinaryPublicId(url: string): string | null {
  if (!url || !url.includes('cloudinary.com')) {
    return null;
  }

  try {
    // Cloudinary URL format: https://res.cloudinary.com/{cloud_name}/image/upload/{transformations}/{public_id}.{format}
    // or: https://res.cloudinary.com/{cloud_name}/image/upload/{public_id}.{format}
    const urlParts = url.split('/');

    // Find the 'upload' part to locate the public_id
    const uploadIndex = urlParts.findIndex(part => part === 'upload');
    if (uploadIndex === -1) return null;

    // Get everything after 'upload' and before the file extension
    const pathAfterUpload = urlParts.slice(uploadIndex + 1);

    // Remove transformations (they start with letters like 'w_', 'h_', 'c_', etc.)
    const publicIdParts = [];
    let foundPublicId = false;

    for (const part of pathAfterUpload) {
      // Skip transformation parameters (contain underscores and start with letters)
      // Also skip version folders (start with 'v' followed by numbers like 'v123456789')
      if (!foundPublicId && (
        (part.includes('_') && /^[a-z]/.test(part)) ||
        /^v\d+$/.test(part)
      )) {
        continue;
      }
      foundPublicId = true;
      publicIdParts.push(part);
    }

    if (publicIdParts.length === 0) return null;

    // Join the parts and remove the file extension from the last part
    const publicIdWithExtension = publicIdParts.join('/');
    const lastDotIndex = publicIdWithExtension.lastIndexOf('.');

    if (lastDotIndex === -1) {
      return publicIdWithExtension;
    }

    return publicIdWithExtension.substring(0, lastDotIndex);
  } catch (error) {
    console.error('Error extracting Cloudinary public_id from URL:', error);
    return null;
  }
}
