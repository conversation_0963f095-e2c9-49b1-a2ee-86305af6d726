/* eslint-disable no-useless-escape */
import { type ClassValue, clsx } from 'clsx';
import { twMerge } from 'tailwind-merge';

export function cn(...inputs: ClassValue[]) {
  return twMerge(clsx(inputs));
}

export function formatCurrency(amount: number, currency: string = 'INR'): string {
  return new Intl.NumberFormat('en-IN', {
    style: 'currency',
    currency,
    minimumFractionDigits: 0,
    maximumFractionDigits: 0,
  }).format(amount);
}

export function formatDate(date: string | Date, options?: Intl.DateTimeFormatOptions): string {
  const defaultOptions: Intl.DateTimeFormatOptions = {
    year: 'numeric',
    month: 'long',
    day: 'numeric',
  };
  
  return new Intl.DateTimeFormat('en-IN', { ...defaultOptions, ...options }).format(
    typeof date === 'string' ? new Date(date) : date
  );
}

export function formatRelativeTime(date: string | Date): string {
  const now = new Date();
  const targetDate = typeof date === 'string' ? new Date(date) : date;
  const diffInSeconds = Math.floor((now.getTime() - targetDate.getTime()) / 1000);
  
  if (diffInSeconds < 60) return 'just now';
  if (diffInSeconds < 3600) return `${Math.floor(diffInSeconds / 60)} minutes ago`;
  if (diffInSeconds < 86400) return `${Math.floor(diffInSeconds / 3600)} hours ago`;
  if (diffInSeconds < 2592000) return `${Math.floor(diffInSeconds / 86400)} days ago`;
  
  return formatDate(targetDate);
}

export function slugify(text: string): string {
  return text
    .toLowerCase()
    .replace(/[^a-z0-9]+/g, '-')
    .replace(/^-|-$/g, '');
}

export function truncateText(text: string, maxLength: number): string {
  if (text.length <= maxLength) return text;
  return text.slice(0, maxLength).replace(/\s+\S*$/, '') + '...';
}

export function generateBookingReference(): string {
  const prefix = 'P7';
  const timestamp = Date.now().toString().slice(-6);
  const random = Math.random().toString(36).substring(2, 6).toUpperCase();
  return `${prefix}${timestamp}${random}`;
}

export function validateEmail(email: string): boolean {
  const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
  return emailRegex.test(email);
}

export function validatePhone(phone: string): boolean {
  const phoneRegex = /^[+]?[\d\s\-\(\)]{10,}$/;
  return phoneRegex.test(phone);
}

export function debounce<T extends (...args: any[]) => any>(
  func: T,
  wait: number
): (...args: Parameters<T>) => void {
  let timeout: NodeJS.Timeout;
  return (...args: Parameters<T>) => {
    clearTimeout(timeout);
    timeout = setTimeout(() => func(...args), wait);
  };
}

export function throttle<T extends (...args: any[]) => any>(
  func: T,
  limit: number
): (...args: Parameters<T>) => void {
  let inThrottle: boolean;
  return (...args: Parameters<T>) => {
    if (!inThrottle) {
      func(...args);
      inThrottle = true;
      setTimeout(() => (inThrottle = false), limit);
    }
  };
}

export function getImageUrl(path: string, fallback?: string): string {
  if (!path) return fallback || '/images/placeholder.jpg';
  
  // If it's already a full URL, return as is
  if (path.startsWith('http')) return path;
  
  // If it's a Supabase storage path
  if (path.startsWith('/storage/')) {
    return `${process.env.NEXT_PUBLIC_SUPABASE_URL}${path}`;
  }
  
  // If it's a relative path, make it absolute
  if (path.startsWith('/')) return path;
  
  return `/${path}`;
}

export function calculateTripDuration(startDate: string, endDate: string): number {
  const start = new Date(startDate);
  const end = new Date(endDate);
  const diffTime = Math.abs(end.getTime() - start.getTime());
  return Math.ceil(diffTime / (1000 * 60 * 60 * 24));
}

export function getDifficultyColor(difficulty: string): string {
  switch (difficulty.toLowerCase()) {
    case 'easy':
      return 'text-green-600 bg-green-100';
    case 'moderate':
      return 'text-yellow-600 bg-yellow-100';
    case 'challenging':
      return 'text-orange-600 bg-orange-100';
    case 'difficult':
      return 'text-red-600 bg-red-100';
    default:
      return 'text-gray-600 bg-gray-100';
  }
}

export function getStatusColor(status: string): string {
  switch (status.toLowerCase()) {
    case 'confirmed':
    case 'completed':
    case 'approved':
      return 'text-green-600 bg-green-100';
    case 'pending':
      return 'text-yellow-600 bg-yellow-100';
    case 'cancelled':
    case 'rejected':
      return 'text-red-600 bg-red-100';
    case 'in_progress':
      return 'text-blue-600 bg-blue-100';
    default:
      return 'text-gray-600 bg-gray-100';
  }
}

export function generateSEOTitle(title: string, suffix?: string): string {
  const baseSuffix = suffix || 'Positive7 - Educational Tours & Student Travel';
  return `${title} | ${baseSuffix}`;
}

export function generateSEODescription(description: string, maxLength: number = 160): string {
  return truncateText(description, maxLength);
}

export function isValidUrl(url: string): boolean {
  try {
    new URL(url);
    return true;
  } catch {
    return false;
  }
}

export function getInitials(name: string): string {
  return name
    .split(' ')
    .map(word => word.charAt(0))
    .join('')
    .toUpperCase()
    .slice(0, 2);
}

export function sleep(ms: number): Promise<void> {
  return new Promise(resolve => setTimeout(resolve, ms));
}

/**
 * Convert cron syntax to human-readable natural language
 * @param cronExpression - Standard cron expression (5 fields: minute hour day month weekday)
 * @returns Human-readable description of the schedule
 */
export function cronToNaturalLanguage(cronExpression: string): string {
  if (!cronExpression || typeof cronExpression !== 'string') {
    return 'Invalid schedule';
  }

  const parts = cronExpression.trim().split(/\s+/);

  // Standard cron has 5 fields: minute hour day month weekday
  if (parts.length !== 5) {
    return 'Invalid cron format';
  }

  const [minute, hour, day, month, weekday] = parts;

  // Handle common patterns
  if (cronExpression === '*/5 * * * *') {
    return 'Every 5 minutes';
  }

  if (cronExpression === '*/15 * * * *') {
    return 'Every 15 minutes';
  }

  if (cronExpression === '*/30 * * * *') {
    return 'Every 30 minutes';
  }

  if (cronExpression === '0 * * * *') {
    return 'Every hour';
  }

  if (cronExpression === '0 0 * * *') {
    return 'Daily at midnight';
  }

  if (cronExpression === '0 12 * * *') {
    return 'Daily at noon';
  }

  if (cronExpression === '0 0 * * 0') {
    return 'Weekly on Sunday at midnight';
  }

  if (cronExpression === '0 0 1 * *') {
    return 'Monthly on the 1st at midnight';
  }

  // Handle minute patterns
  let minuteText = '';
  if (minute === '*') {
    minuteText = 'every minute';
  } else if (minute.startsWith('*/')) {
    const interval = minute.substring(2);
    minuteText = `every ${interval} minutes`;
  } else if (minute === '0') {
    minuteText = 'at the top of the hour';
  } else if (minute === '30') {
    minuteText = 'at 30 minutes past the hour';
  } else {
    minuteText = `at ${minute} minutes past the hour`;
  }

  // Handle hour patterns
  let hourText = '';
  if (hour === '*') {
    hourText = 'every hour';
  } else if (hour.startsWith('*/')) {
    const interval = hour.substring(2);
    hourText = `every ${interval} hours`;
  } else {
    const hourNum = parseInt(hour);
    if (hourNum === 0) {
      hourText = 'at midnight';
    } else if (hourNum === 12) {
      hourText = 'at noon';
    } else if (hourNum < 12) {
      hourText = `at ${hourNum}:00 AM`;
    } else {
      hourText = `at ${hourNum - 12}:00 PM`;
    }
  }

  // Combine minute and hour
  if (minute === '*' && hour === '*') {
    return 'Every minute';
  } else if (minute.startsWith('*/') && hour === '*') {
    return minuteText.charAt(0).toUpperCase() + minuteText.slice(1);
  } else if (minute === '0' && hour.startsWith('*/')) {
    return hourText.charAt(0).toUpperCase() + hourText.slice(1);
  } else if (minute === '0' && hour !== '*') {
    return hourText.charAt(0).toUpperCase() + hourText.slice(1);
  } else {
    return `${hourText} ${minuteText}`.charAt(0).toUpperCase() + `${hourText} ${minuteText}`.slice(1);
  }
}

// Clean and format a phone number
export function formatPhoneNumber(phoneNumber: string): string {
  // Remove all non-digit characters except + for country code
  const digits = phoneNumber.replace(/[^\d+]/g, '');
  
  // Format as per needs - this is a simple example
  if (digits.length === 10) {
    return `${digits.substring(0, 3)}-${digits.substring(3, 6)}-${digits.substring(6)}`;
  }
  
  // Return as is if doesn't match expected format
  return phoneNumber;
}

// Format phone number with international format
export function formatPhoneNumberIntl(phone: string): string {
  // Remove all non-digit characters
  const digits = phone.replace(/\D/g, '');
  
  // Format based on length
  if (digits.length === 10) {
    return `+1 (${digits.substring(0, 3)}) ${digits.substring(3, 6)}-${digits.substring(6)}`;
  } else if (digits.length > 10) {
    return `+${digits.substring(0, digits.length - 10)} (${digits.substring(digits.length - 10, digits.length - 7)}) ${digits.substring(digits.length - 7, digits.length - 4)}-${digits.substring(digits.length - 4)}`;
  }
  
  // Return as is if doesn't match expected format
  return phone;
}

/**
 * Optimize image URLs with size and quality parameters for better performance
 * @param url The original image URL
 * @param width The desired width of the image
 * @param quality The desired quality of the image (1-100)
 * @returns The optimized image URL
 */
export function optimizeImageUrl(url: string, width = 800, quality = 80): string {
  // Don't try to optimize data URIs, blob URLs, or relative URLs
  if (!url || url.startsWith('data:') || url.startsWith('blob:') || url.startsWith('/')) {
    return url;
  }

  // Check if the URL already has query parameters
  const hasParams = url.includes('?');
  
  // Add width and quality parameters
  return `${url}${hasParams ? '&' : '?'}w=${width}&q=${quality}`;
}

/**
 * Convert a file size in bytes to a human-readable string
 * @param bytes File size in bytes
 * @param decimals Number of decimal places
 * @returns Human-readable file size
 */
export function formatFileSize(bytes: number, decimals = 2): string {
  if (bytes === 0) return '0 Bytes';

  const k = 1024;
  const dm = decimals < 0 ? 0 : decimals;
  const sizes = ['Bytes', 'KB', 'MB', 'GB', 'TB', 'PB', 'EB', 'ZB', 'YB'];

  const i = Math.floor(Math.log(bytes) / Math.log(k));

  return parseFloat((bytes / Math.pow(k, i)).toFixed(dm)) + ' ' + sizes[i];
}
