/**
 * Comprehensive monitoring and logging system
 */

import { AppError } from './error-handler';

// Performance monitoring types
export interface PerformanceMetric {
  name: string;
  value: number;
  unit: 'ms' | 'bytes' | 'count' | 'percentage';
  timestamp: string;
  context?: Record<string, any>;
}

export interface ApiMetrics {
  endpoint: string;
  method: string;
  statusCode: number;
  responseTime: number;
  requestSize?: number;
  responseSize?: number;
  userAgent?: string;
  ip?: string;
  timestamp: string;
}

export interface SecurityEvent {
  type: 'authentication_failure' | 'rate_limit_exceeded' | 'suspicious_activity' | 'data_breach_attempt';
  severity: 'low' | 'medium' | 'high' | 'critical';
  description: string;
  ip?: string;
  userAgent?: string;
  userId?: string;
  metadata?: Record<string, any>;
  timestamp: string;
}

export interface SystemHealth {
  status: 'healthy' | 'degraded' | 'unhealthy';
  uptime: number;
  memoryUsage: {
    used: number;
    total: number;
    percentage: number;
  };
  responseTime: {
    avg: number;
    p95: number;
    p99: number;
  };
  errorRate: number;
  timestamp: string;
}

// In-memory storage for metrics (in production, use Redis or external service)
class MetricsStore {
  private metrics: PerformanceMetric[] = [];
  private apiMetrics: ApiMetrics[] = [];
  private securityEvents: SecurityEvent[] = [];
  private maxEntries = 10000; // Prevent memory leaks
  private startTime = Date.now(); // Fallback for environments without process.uptime

  addMetric(metric: PerformanceMetric) {
    this.metrics.push(metric);
    this.cleanup(this.metrics);
  }

  addApiMetric(metric: ApiMetrics) {
    this.apiMetrics.push(metric);
    this.cleanup(this.apiMetrics);
  }

  addSecurityEvent(event: SecurityEvent) {
    this.securityEvents.push(event);
    this.cleanup(this.securityEvents);
    
    // Log critical security events immediately
    if (event.severity === 'critical') {
      console.error('🚨 CRITICAL SECURITY EVENT:', event);
    }
  }

  private cleanup<T>(array: T[]) {
    if (array.length > this.maxEntries) {
      array.splice(0, array.length - this.maxEntries);
    }
  }

  getMetrics(name?: string, since?: Date): PerformanceMetric[] {
    let filtered = this.metrics;
    
    if (name) {
      filtered = filtered.filter(m => m.name === name);
    }
    
    if (since) {
      filtered = filtered.filter(m => new Date(m.timestamp) >= since);
    }
    
    return filtered;
  }

  getApiMetrics(endpoint?: string, since?: Date): ApiMetrics[] {
    let filtered = this.apiMetrics;
    
    if (endpoint) {
      filtered = filtered.filter(m => m.endpoint === endpoint);
    }
    
    if (since) {
      filtered = filtered.filter(m => new Date(m.timestamp) >= since);
    }
    
    return filtered;
  }

  getSecurityEvents(severity?: SecurityEvent['severity'], since?: Date): SecurityEvent[] {
    let filtered = this.securityEvents;
    
    if (severity) {
      filtered = filtered.filter(e => e.severity === severity);
    }
    
    if (since) {
      filtered = filtered.filter(e => new Date(e.timestamp) >= since);
    }
    
    return filtered;
  }

  private getUptime(): number {
    // Edge Runtime compatible uptime calculation
    // Always use Date.now() based calculation for consistency across environments
    return Math.floor((Date.now() - this.startTime) / 1000);
  }

  getSystemHealth(): SystemHealth {
    const now = new Date();
    const oneHourAgo = new Date(now.getTime() - 60 * 60 * 1000);
    
    const recentApiMetrics = this.getApiMetrics(undefined, oneHourAgo);
    const recentErrors = recentApiMetrics.filter(m => m.statusCode >= 400);
    
    const responseTimes = recentApiMetrics.map(m => m.responseTime);
    const avgResponseTime = responseTimes.length > 0 
      ? responseTimes.reduce((a, b) => a + b, 0) / responseTimes.length 
      : 0;
    
    const sortedResponseTimes = responseTimes.sort((a, b) => a - b);
    const p95Index = Math.floor(sortedResponseTimes.length * 0.95);
    const p99Index = Math.floor(sortedResponseTimes.length * 0.99);
    
    const errorRate = recentApiMetrics.length > 0 
      ? (recentErrors.length / recentApiMetrics.length) * 100 
      : 0;

    // Memory usage (Edge Runtime compatible)
    const memoryUsage = (() => {
      // Use performance.memory if available (Chrome/Edge)
      if (typeof performance !== 'undefined' && 'memory' in performance) {
        const mem = (performance as any).memory;
        return {
          used: mem.usedJSHeapSize || 0,
          total: mem.totalJSHeapSize || 0,
          percentage: mem.totalJSHeapSize > 0 ? (mem.usedJSHeapSize / mem.totalJSHeapSize) * 100 : 0,
        };
      }
      // Fallback for environments without memory info
      return {
        used: 0,
        total: 0,
        percentage: 0,
      };
    })();

    let status: SystemHealth['status'] = 'healthy';
    if (errorRate > 10 || avgResponseTime > 2000) {
      status = 'degraded';
    }
    if (errorRate > 25 || avgResponseTime > 5000) {
      status = 'unhealthy';
    }

    return {
      status,
      uptime: this.getUptime(),
      memoryUsage,
      responseTime: {
        avg: avgResponseTime,
        p95: sortedResponseTimes[p95Index] || 0,
        p99: sortedResponseTimes[p99Index] || 0,
      },
      errorRate,
      timestamp: now.toISOString(),
    };
  }
}

// Global metrics store
const metricsStore = new MetricsStore();

// Performance monitoring utilities
export class PerformanceMonitor {
  private startTime: number;
  private name: string;
  private context?: Record<string, any>;

  constructor(name: string, context?: Record<string, any>) {
    this.name = name;
    this.context = context;
    this.startTime = performance.now();
  }

  end(): PerformanceMetric {
    const duration = performance.now() - this.startTime;
    const metric: PerformanceMetric = {
      name: this.name,
      value: duration,
      unit: 'ms',
      timestamp: new Date().toISOString(),
      context: this.context,
    };

    metricsStore.addMetric(metric);
    return metric;
  }
}

// API monitoring middleware
export function trackApiCall(
  endpoint: string,
  method: string,
  statusCode: number,
  responseTime: number,
  options: {
    requestSize?: number;
    responseSize?: number;
    userAgent?: string;
    ip?: string;
  } = {}
) {
  const metric: ApiMetrics = {
    endpoint,
    method,
    statusCode,
    responseTime,
    timestamp: new Date().toISOString(),
    ...options,
  };

  metricsStore.addApiMetric(metric);

  // No console logging - admin will check performance tab instead
}

// Security monitoring
export function trackSecurityEvent(event: Omit<SecurityEvent, 'timestamp'>) {
  const securityEvent: SecurityEvent = {
    ...event,
    timestamp: new Date().toISOString(),
  };

  metricsStore.addSecurityEvent(securityEvent);

  // Send alerts for high severity events
  if (event.severity === 'high' || event.severity === 'critical') {
    sendSecurityAlert(securityEvent);
  }
}

// Alert system (placeholder - integrate with real alerting service)
function sendSecurityAlert(event: SecurityEvent) {
  console.error('🚨 SECURITY ALERT:', {
    type: event.type,
    severity: event.severity,
    description: event.description,
    timestamp: event.timestamp,
    metadata: event.metadata,
  });

  // In production, integrate with:
  // - Email alerts
  // - Slack notifications
  // - PagerDuty
  // - SMS alerts
}

// Error tracking
export function trackError(error: AppError, context?: Record<string, any>) {
  const metric: PerformanceMetric = {
    name: 'error_count',
    value: 1,
    unit: 'count',
    timestamp: new Date().toISOString(),
    context: {
      errorCode: error.code,
      statusCode: error.statusCode,
      message: error.message,
      isOperational: error.isOperational,
      ...context,
    },
  };

  metricsStore.addMetric(metric);

  // Track security-related errors
  if (error.statusCode === 401 || error.statusCode === 403) {
    trackSecurityEvent({
      type: 'authentication_failure',
      severity: 'medium',
      description: `Authentication/Authorization error: ${error.message}`,
      metadata: context,
    });
  }
}

// Business metrics
export function trackBusinessMetric(name: string, value: number, unit: PerformanceMetric['unit'], context?: Record<string, any>) {
  const metric: PerformanceMetric = {
    name,
    value,
    unit,
    timestamp: new Date().toISOString(),
    context,
  };

  metricsStore.addMetric(metric);
}

// Health check endpoint data
export function getSystemHealth(): SystemHealth {
  return metricsStore.getSystemHealth();
}

// Analytics and reporting
export function getMetricsReport(since?: Date) {
  const sinceDate = since || new Date(Date.now() - 24 * 60 * 60 * 1000); // Last 24 hours

  const apiMetrics = metricsStore.getApiMetrics(undefined, sinceDate);
  const securityEvents = metricsStore.getSecurityEvents(undefined, sinceDate);
  const performanceMetrics = metricsStore.getMetrics(undefined, sinceDate);

  // Calculate aggregated metrics
  const totalRequests = apiMetrics.length;
  const errorRequests = apiMetrics.filter(m => m.statusCode >= 400).length;
  const errorRate = totalRequests > 0 ? (errorRequests / totalRequests) * 100 : 0;

  const responseTimes = apiMetrics.map(m => m.responseTime);
  const avgResponseTime = responseTimes.length > 0 
    ? responseTimes.reduce((a, b) => a + b, 0) / responseTimes.length 
    : 0;

  const endpointStats = apiMetrics.reduce((acc, metric) => {
    const key = `${metric.method} ${metric.endpoint}`;
    if (!acc[key]) {
      acc[key] = { count: 0, totalTime: 0, errors: 0 };
    }
    acc[key].count++;
    acc[key].totalTime += metric.responseTime;
    if (metric.statusCode >= 400) {
      acc[key].errors++;
    }
    return acc;
  }, {} as Record<string, { count: number; totalTime: number; errors: number }>);

  return {
    period: {
      start: sinceDate.toISOString(),
      end: new Date().toISOString(),
    },
    summary: {
      totalRequests,
      errorRequests,
      errorRate,
      avgResponseTime,
      securityEvents: securityEvents.length,
      criticalSecurityEvents: securityEvents.filter(e => e.severity === 'critical').length,
    },
    endpoints: Object.entries(endpointStats).map(([endpoint, stats]) => ({
      endpoint,
      requests: stats.count,
      avgResponseTime: stats.totalTime / stats.count,
      errorRate: (stats.errors / stats.count) * 100,
    })),
    securityEvents: securityEvents.map(e => ({
      type: e.type,
      severity: e.severity,
      timestamp: e.timestamp,
    })),
    systemHealth: getSystemHealth(),
  };
}

// Export the metrics store for testing
export { metricsStore };
