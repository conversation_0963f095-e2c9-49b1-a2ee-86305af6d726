/**
 * Caching strategies and cache management system
 */

import { performanceMonitor } from './performance-monitor';

// Cache configuration
export interface CacheConfig {
  ttl: number; // Time to live in milliseconds
  maxSize: number; // Maximum number of items
  strategy: 'lru' | 'fifo' | 'ttl'; // Cache eviction strategy
}

// Cache entry interface
interface CacheEntry<T> {
  value: T;
  timestamp: number;
  ttl: number;
  accessCount: number;
  lastAccessed: number;
}

// Default cache configurations
export const CACHE_CONFIGS = {
  TRIPS: {
    ttl: 5 * 60 * 1000, // 5 minutes
    maxSize: 100,
    strategy: 'lru' as const,
  },
  BLOG_POSTS: {
    ttl: 10 * 60 * 1000, // 10 minutes
    maxSize: 50,
    strategy: 'lru' as const,
  },
  GALLERIES: {
    ttl: 15 * 60 * 1000, // 15 minutes
    maxSize: 30,
    strategy: 'lru' as const,
  },
  PUBLIC_TRIPS: {
    ttl: 5 * 60 * 1000, // 5 minutes - more reasonable for production
    maxSize: 200,
    strategy: 'lru' as const,
  },
  PUBLIC_BLOG: {
    ttl: 5 * 60 * 1000, // 5 minutes - more reasonable for production
    maxSize: 100,
    strategy: 'lru' as const,
  },
  PUBLIC_GALLERIES: {
    ttl: 5 * 60 * 1000, // 5 minutes - more reasonable for production
    maxSize: 50,
    strategy: 'lru' as const,
  },
  ADMIN_DASHBOARD: {
    ttl: 2 * 60 * 1000, // 2 minutes
    maxSize: 10,
    strategy: 'ttl' as const,
  },
  AUDIT_LOGS: {
    ttl: 1 * 60 * 1000, // 1 minute
    maxSize: 20,
    strategy: 'fifo' as const,
  },
  USER_SESSIONS: {
    ttl: 30 * 60 * 1000, // 30 minutes
    maxSize: 1000,
    strategy: 'lru' as const,
  },
} as const;

export class CacheManager<T = any> {
  private cache: Map<string, CacheEntry<T>> = new Map();
  private config: CacheConfig;
  private name: string;

  constructor(name: string, config: CacheConfig) {
    this.name = name;
    this.config = config;
  }

  /**
   * Get value from cache
   */
  get(key: string): T | null {
    const operationId = `cache_get_${this.name}_${key}`;
    performanceMonitor.startTimer(operationId);

    const entry = this.cache.get(key);
    
    if (!entry) {
      performanceMonitor.endTimer(operationId, 'database_query', { 
        cache_name: this.name,
        cache_hit: false,
        operation: 'get'
      });
      return null;
    }

    // Check if entry has expired
    if (this.isExpired(entry)) {
      this.cache.delete(key);
      performanceMonitor.endTimer(operationId, 'database_query', { 
        cache_name: this.name,
        cache_hit: false,
        cache_expired: true,
        operation: 'get'
      });
      return null;
    }

    // Update access statistics
    entry.accessCount++;
    entry.lastAccessed = Date.now();

    performanceMonitor.endTimer(operationId, 'database_query', { 
      cache_name: this.name,
      cache_hit: true,
      operation: 'get'
    });

    return entry.value;
  }

  /**
   * Set value in cache
   */
  set(key: string, value: T, customTtl?: number): void {
    const operationId = `cache_set_${this.name}_${key}`;
    performanceMonitor.startTimer(operationId);

    const ttl = customTtl || this.config.ttl;
    const entry: CacheEntry<T> = {
      value,
      timestamp: Date.now(),
      ttl,
      accessCount: 0,
      lastAccessed: Date.now(),
    };

    // Check if we need to evict entries to stay under capacity
    while (this.cache.size >= this.config.maxSize) {
      this.evict();
    }

    this.cache.set(key, entry);

    performanceMonitor.endTimer(operationId, 'database_query', { 
      cache_name: this.name,
      operation: 'set',
      cache_size: this.cache.size
    });
  }

  /**
   * Delete value from cache
   */
  delete(key: string): boolean {
    return this.cache.delete(key);
  }

  /**
   * Clear entire cache
   */
  clear(): void {
    this.cache.clear();
  }

  /**
   * Check if key exists and is not expired
   */
  has(key: string): boolean {
    const entry = this.cache.get(key);
    if (!entry) return false;
    
    if (this.isExpired(entry)) {
      this.cache.delete(key);
      return false;
    }
    
    return true;
  }

  /**
   * Get cache statistics
   */
  getStats(): {
    size: number;
    maxSize: number;
    hitRate: number;
    entries: Array<{
      key: string;
      age: number;
      accessCount: number;
      lastAccessed: number;
    }>;
  } {
    const entries: Array<{
      key: string;
      age: number;
      accessCount: number;
      lastAccessed: number;
    }> = [];

    this.cache.forEach((entry, key) => {
      entries.push({
        key,
        age: Date.now() - entry.timestamp,
        accessCount: entry.accessCount,
        lastAccessed: entry.lastAccessed,
      });
    });

    const totalHits = entries.reduce((sum, entry) => sum + entry.accessCount, 0);
    const totalRequests = totalHits + this.cache.size; // Approximate total requests
    const hitRate = totalRequests > 0 ? (totalHits / totalRequests) : 0;

    return {
      size: this.cache.size,
      maxSize: this.config.maxSize,
      hitRate,
      entries,
    };
  }

  /**
   * Get or set pattern (cache-aside)
   */
  async getOrSet<R>(
    key: string,
    fetcher: () => Promise<R>,
    customTtl?: number
  ): Promise<R> {
    // Try to get from cache first
    const cached = this.get(key);
    if (cached !== null) {
      return cached as R;
    }

    // Fetch from source
    const operationId = `cache_fetch_${this.name}_${key}`;
    performanceMonitor.startTimer(operationId);

    try {
      const value = await fetcher();
      this.set(key, value as T, customTtl);
      
      performanceMonitor.endTimer(operationId, 'database_query', { 
        cache_name: this.name,
        operation: 'fetch_and_set',
        cache_miss: true
      });

      return value;
    } catch (error) {
      performanceMonitor.endTimer(operationId, 'database_query', { 
        cache_name: this.name,
        operation: 'fetch_and_set',
        cache_miss: true,
        error: true,
        error_message: error instanceof Error ? error.message : 'Unknown error'
      });
      throw error;
    }
  }

  /**
   * Check if cache entry is expired
   */
  private isExpired(entry: CacheEntry<T>): boolean {
    return Date.now() - entry.timestamp > entry.ttl;
  }

  /**
   * Evict entries based on strategy
   */
  private evict(): void {
    if (this.cache.size === 0) return;

    let keyToEvict: string | null = null;

    switch (this.config.strategy) {
      case 'lru': { // Least Recently Used
        let oldestAccess = Date.now();
        this.cache.forEach((entry, key) => {
          if (entry.lastAccessed < oldestAccess) {
            oldestAccess = entry.lastAccessed;
            keyToEvict = key;
          }
        });
        break;
      }

      case 'fifo': { // First In, First Out
        let oldestTimestamp = Date.now();
        this.cache.forEach((entry, key) => {
          if (entry.timestamp < oldestTimestamp) {
            oldestTimestamp = entry.timestamp;
            keyToEvict = key;
          }
        });
        break;
      }

      case 'ttl': { // Time To Live (evict expired first)
        this.cache.forEach((entry, key) => {
          if (this.isExpired(entry) && !keyToEvict) {
            keyToEvict = key;
          }
        });
        // If no expired entries, fall back to LRU
        if (!keyToEvict) {
          let oldestAccess = Date.now();
          this.cache.forEach((entry, key) => {
            if (entry.lastAccessed < oldestAccess) {
              oldestAccess = entry.lastAccessed;
              keyToEvict = key;
            }
          });
        }
        break;
      }
    }

    if (keyToEvict) {
      this.cache.delete(keyToEvict);
    }
  }

  /**
   * Clean up expired entries
   */
  cleanup(): number {
    const expiredKeys: string[] = [];

    this.cache.forEach((entry, key) => {
      if (this.isExpired(entry)) {
        expiredKeys.push(key);
      }
    });

    expiredKeys.forEach(key => this.cache.delete(key));
    return expiredKeys.length;
  }
}

// Global cache instances
export const cacheInstances = {
  trips: new CacheManager('trips', CACHE_CONFIGS.TRIPS),
  blogPosts: new CacheManager('blog_posts', CACHE_CONFIGS.BLOG_POSTS),
  galleries: new CacheManager('galleries', CACHE_CONFIGS.GALLERIES),
  publicTrips: new CacheManager('public_trips', CACHE_CONFIGS.PUBLIC_TRIPS),
  publicBlog: new CacheManager('public_blog', CACHE_CONFIGS.PUBLIC_BLOG),
  publicGalleries: new CacheManager('public_galleries', CACHE_CONFIGS.PUBLIC_GALLERIES),
  adminDashboard: new CacheManager('admin_dashboard', CACHE_CONFIGS.ADMIN_DASHBOARD),
  auditLogs: new CacheManager('audit_logs', CACHE_CONFIGS.AUDIT_LOGS),
  userSessions: new CacheManager('user_sessions', CACHE_CONFIGS.USER_SESSIONS),
};

// Cache manager for coordinating all caches
export class GlobalCacheManager {
  private static instance: GlobalCacheManager;
  private cleanupInterval: NodeJS.Timeout | null = null;

  private constructor() {
    this.startCleanupInterval();
  }

  static getInstance(): GlobalCacheManager {
    if (!GlobalCacheManager.instance) {
      GlobalCacheManager.instance = new GlobalCacheManager();
    }
    return GlobalCacheManager.instance;
  }

  /**
   * Get all cache statistics
   */
  getAllStats(): Record<string, any> {
    const stats: Record<string, any> = {};
    
    Object.entries(cacheInstances).forEach(([name, cache]) => {
      stats[name] = cache.getStats();
    });

    return stats;
  }

  /**
   * Clear all caches
   */
  clearAll(): void {
    Object.values(cacheInstances).forEach(cache => cache.clear());
  }

  /**
   * Cleanup all caches
   */
  cleanupAll(): Record<string, number> {
    const results: Record<string, number> = {};
    
    Object.entries(cacheInstances).forEach(([name, cache]) => {
      results[name] = cache.cleanup();
    });

    return results;
  }

  /**
   * Start automatic cleanup interval
   */
  private startCleanupInterval(): void {
    // Cleanup every 5 minutes
    this.cleanupInterval = setInterval(() => {
      const results = this.cleanupAll();
      const totalCleaned = Object.values(results).reduce((sum, count) => sum + count, 0);
      
      if (totalCleaned > 0) {
        console.log(`[Cache] Cleaned up ${totalCleaned} expired entries:`, results);
      }
    }, 5 * 60 * 1000);
  }

  /**
   * Stop cleanup interval
   */
  stopCleanupInterval(): void {
    if (this.cleanupInterval) {
      clearInterval(this.cleanupInterval);
      this.cleanupInterval = null;
    }
  }
}

// Global cache manager instance
export const globalCacheManager = GlobalCacheManager.getInstance();

// Utility functions for common caching patterns
export const withCache = async <T>(
  cacheKey: string,
  fetcher: () => Promise<T>,
  cacheInstance: CacheManager<T>,
  ttl?: number
): Promise<T> => {
  return cacheInstance.getOrSet(cacheKey, fetcher, ttl);
};

// Cache invalidation helpers
export const invalidateCache = (pattern: string, cacheInstance: CacheManager): number => {
  const stats = cacheInstance.getStats();
  let invalidated = 0;
  
  stats.entries.forEach(entry => {
    if (entry.key.includes(pattern)) {
      cacheInstance.delete(entry.key);
      invalidated++;
    }
  });
  
  return invalidated;
};

// Cache warming helpers
export const warmCache = async <T>(
  keys: string[],
  fetcher: (key: string) => Promise<T>,
  cacheInstance: CacheManager<T>
): Promise<void> => {
  const promises = keys.map(async (key) => {
    try {
      const value = await fetcher(key);
      cacheInstance.set(key, value);
    } catch (error) {
      console.warn(`Failed to warm cache for key ${key}:`, error);
    }
  });
  
  await Promise.allSettled(promises);
};
