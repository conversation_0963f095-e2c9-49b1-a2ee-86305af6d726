/**
 * Comprehensive audit logging system for security and compliance
 */

import { getClientIP } from './error-handler';
import { NextRequest } from 'next/server';
import { createServerSupabase } from './supabase-server';

// Audit event types
export type AuditEventType = 
  | 'user_login'
  | 'user_logout'
  | 'user_login_failed'
  | 'user_created'
  | 'user_updated'
  | 'user_deleted'
  | 'password_changed'
  | 'role_assigned'
  | 'role_removed'
  | 'admin_access'
  | 'data_export'
  | 'data_import'
  | 'sensitive_data_access'
  | 'configuration_changed'
  | 'security_event'
  | 'api_key_created'
  | 'api_key_revoked'
  | 'file_upload'
  | 'file_delete'
  | 'database_query'
  | 'system_error'
  | 'rate_limit_exceeded'
  | 'suspicious_activity'
  | 'data_breach_attempt'
  | 'privilege_escalation'
  | 'unauthorized_access';

export type AuditSeverity = 'low' | 'medium' | 'high' | 'critical';

export interface AuditLogEntry {
  id?: string;
  event_type: AuditEventType;
  severity: AuditSeverity;
  user_id?: string;
  user_email?: string;
  ip_address?: string;
  user_agent?: string;
  resource_type?: string;
  resource_id?: string;
  action: string;
  description: string;
  metadata?: Record<string, any>;
  timestamp: string;
  session_id?: string;
  request_id?: string;
  success: boolean;
  error_message?: string;
}

export interface AuditQueryParams {
  user_id?: string;
  event_type?: AuditEventType;
  severity?: AuditSeverity;
  start_date?: string;
  end_date?: string;
  resource_type?: string;
  success?: boolean;
  limit?: number;
  offset?: number;
}

// Audit logger class
export class AuditLogger {
  private static instance: AuditLogger;

  private constructor() {}

  public static getInstance(): AuditLogger {
    if (!AuditLogger.instance) {
      AuditLogger.instance = new AuditLogger();
    }
    return AuditLogger.instance;
  }

  // Create fresh Supabase client for each operation to avoid auth context leaks
  private getSupabaseClient() {
    return createServerSupabase();
  }

  // Log an audit event
  async log(entry: Omit<AuditLogEntry, 'id' | 'timestamp'>): Promise<void> {
    try {
      const auditEntry: AuditLogEntry = {
        ...entry,
        timestamp: new Date().toISOString(),
      };

      // Store in database using the function we created
      const supabase = this.getSupabaseClient();
      const { error } = await (supabase as any).rpc('insert_security_audit_log', {
        p_event_type: auditEntry.event_type,
        p_severity: auditEntry.severity,
        p_user_id: auditEntry.user_id || null,
        p_user_email: auditEntry.user_email || null,
        p_ip_address: auditEntry.ip_address || null,
        p_user_agent: auditEntry.user_agent || null,
        p_resource_type: auditEntry.resource_type || null,
        p_resource_id: auditEntry.resource_id || null,
        p_action: auditEntry.action,
        p_description: auditEntry.description,
        p_metadata: auditEntry.metadata || null,
        p_session_id: auditEntry.session_id || null,
        p_request_id: auditEntry.request_id || null,
        p_success: auditEntry.success,
        p_error_message: auditEntry.error_message || null,
      });

      if (error) {
        console.error('Failed to store audit log in database:', error);
        // Fallback to console logging
        this.logToConsole(auditEntry);
      } else {
        // Also log to console for immediate visibility
        this.logToConsole(auditEntry);
      }

    } catch (error) {
      console.error('Audit logging failed:', error);
      this.logToConsole(entry);
    }
  }

  // Fallback console logging (development only)
  private logToConsole(entry: Partial<AuditLogEntry>): void {
    if (process.env.NODE_ENV !== 'development') return;

    const logEntry = {
      timestamp: new Date().toISOString(),
      ...entry,
    };

    switch (entry.severity) {
      case 'critical':
        console.error('🚨 AUDIT [CRITICAL]:', logEntry);
        break;
      case 'high':
        console.warn('⚠️ AUDIT [HIGH]:', logEntry);
        break;
      case 'medium':
        console.info('ℹ️ AUDIT [MEDIUM]:', logEntry);
        break;
      default:
        console.log('📝 AUDIT [LOW]:', logEntry);
    }
  }

  // Query audit logs
  async query(params: AuditQueryParams = {}): Promise<AuditLogEntry[]> {
    try {
      const supabase = this.getSupabaseClient();
      const { data, error } = await (supabase as any).rpc('query_security_audit_logs', {
        p_user_id: params.user_id || null,
        p_event_type: params.event_type || null,
        p_severity: params.severity || null,
        p_resource_type: params.resource_type || null,
        p_success: params.success !== undefined ? params.success : null,
        p_start_date: params.start_date || null,
        p_end_date: params.end_date || null,
        p_limit: params.limit || 50,
        p_offset: params.offset || 0,
      });

      if (error) {
        if (process.env.NODE_ENV === 'development') {
          console.error('Failed to query audit logs:', error);
        }
        return [];
      }

      // Transform the database results to match our interface
      return (data || []).map((row: any) => ({
        id: row.id,
        event_type: row.event_type,
        severity: row.severity,
        user_id: row.user_id,
        user_email: row.user_email,
        ip_address: row.ip_address,
        user_agent: row.user_agent,
        resource_type: row.resource_type,
        resource_id: row.resource_id,
        action: row.action,
        description: row.description,
        metadata: row.metadata,
        timestamp: row.log_timestamp,
        session_id: row.session_id,
        request_id: row.request_id,
        success: row.success,
        error_message: row.error_message,
      }));
    } catch (error) {
      console.error('Audit query failed:', error);
      return [];
    }
  }

  // Get audit statistics
  async getStatistics(days: number = 30): Promise<{
    totalEvents: number;
    eventsByType: Record<AuditEventType, number>;
    eventsBySeverity: Record<AuditSeverity, number>;
    failedEvents: number;
    topUsers: Array<{ user_email: string; count: number }>;
    topIPs: Array<{ ip_address: string; count: number }>;
  }> {
    try {
      const startDate = new Date();
      startDate.setDate(startDate.getDate() - days);

      // Query logs for the time period with reasonable limit
      const logs = await this.query({
        start_date: startDate.toISOString(),
        limit: 1000, // Reasonable limit for statistics to prevent OOM
      });

      // Calculate statistics
      const totalEvents = logs.length;
      const failedEvents = logs.filter(log => !log.success).length;

      const eventsByType = logs.reduce((acc, log) => {
        acc[log.event_type] = (acc[log.event_type] || 0) + 1;
        return acc;
      }, {} as Record<AuditEventType, number>);

      const eventsBySeverity = logs.reduce((acc, log) => {
        acc[log.severity] = (acc[log.severity] || 0) + 1;
        return acc;
      }, {} as Record<AuditSeverity, number>);

      // Top users by activity
      const userCounts = logs.reduce((acc, log) => {
        if (log.user_email) {
          acc[log.user_email] = (acc[log.user_email] || 0) + 1;
        }
        return acc;
      }, {} as Record<string, number>);

      const topUsers = Object.entries(userCounts)
        .map(([user_email, count]) => ({ user_email, count }))
        .sort((a, b) => b.count - a.count)
        .slice(0, 10);

      // Top IPs by activity
      const ipCounts = logs.reduce((acc, log) => {
        if (log.ip_address) {
          acc[log.ip_address] = (acc[log.ip_address] || 0) + 1;
        }
        return acc;
      }, {} as Record<string, number>);

      const topIPs = Object.entries(ipCounts)
        .map(([ip_address, count]) => ({ ip_address, count }))
        .sort((a, b) => b.count - a.count)
        .slice(0, 10);

      return {
        totalEvents,
        eventsByType,
        eventsBySeverity,
        failedEvents,
        topUsers,
        topIPs,
      };
    } catch (error) {
      console.error('Failed to calculate audit statistics:', error);
      return this.getEmptyStatistics();
    }
  }

  private getEmptyStatistics() {
    return {
      totalEvents: 0,
      eventsByType: {} as Record<AuditEventType, number>,
      eventsBySeverity: {} as Record<AuditSeverity, number>,
      failedEvents: 0,
      topUsers: [],
      topIPs: [],
    };
  }

  // Clean up old audit logs (for compliance and storage management)
  async cleanup(retentionDays: number = 365): Promise<number> {
    try {
      // Use the database function for cleanup
      const supabase = this.getSupabaseClient();
      const { data, error } = await (supabase as any).rpc('cleanup_security_audit_logs', {
        p_retention_days: retentionDays,
      });

      if (error) {
        console.error('Failed to cleanup audit logs:', error);
        return 0;
      }

      const deletedCount = data || 0;
      console.log(`Cleaned up ${deletedCount} audit log entries older than ${retentionDays} days`);

      return deletedCount;
    } catch (error) {
      console.error('Audit cleanup failed:', error);
      return 0;
    }
  }
}

// Convenience functions for common audit events
export const auditLogger = AuditLogger.getInstance();

// Helper function to extract request context
export function getRequestContext(request?: NextRequest): {
  ip_address?: string;
  user_agent?: string;
  request_id?: string;
} {
  if (!request) return {};

  return {
    ip_address: getClientIP(request),
    user_agent: request.headers.get('user-agent') || undefined,
    request_id: request.headers.get('x-request-id') || undefined,
  };
}

// Specific audit logging functions
export async function logUserLogin(
  userId: string,
  userEmail: string,
  success: boolean,
  context?: Partial<AuditLogEntry>
): Promise<void> {
  await auditLogger.log({
    event_type: success ? 'user_login' : 'user_login_failed',
    severity: success ? 'low' : 'medium',
    user_id: userId,
    user_email: userEmail,
    action: success ? 'User logged in successfully' : 'User login failed',
    description: success 
      ? `User ${userEmail} logged in successfully`
      : `Failed login attempt for user ${userEmail}`,
    success,
    ...context,
  });
}

export async function logAdminAccess(
  userId: string,
  userEmail: string,
  resource: string,
  action: string,
  context?: Partial<AuditLogEntry>
): Promise<void> {
  await auditLogger.log({
    event_type: 'admin_access',
    severity: 'medium',
    user_id: userId,
    user_email: userEmail,
    resource_type: 'admin',
    action,
    description: `Admin ${userEmail} accessed ${resource}: ${action}`,
    success: true,
    ...context,
  });
}

export async function logSecurityEvent(
  eventType: AuditEventType,
  severity: AuditSeverity,
  description: string,
  context?: Partial<AuditLogEntry>
): Promise<void> {
  await auditLogger.log({
    event_type: eventType,
    severity,
    action: 'Security event detected',
    description,
    success: false,
    ...context,
  });
}

export async function logDataAccess(
  userId: string,
  userEmail: string,
  resourceType: string,
  resourceId: string,
  action: string,
  success: boolean = true,
  context?: Partial<AuditLogEntry>
): Promise<void> {
  await auditLogger.log({
    event_type: 'sensitive_data_access',
    severity: 'low',
    user_id: userId,
    user_email: userEmail,
    resource_type: resourceType,
    resource_id: resourceId,
    action,
    description: `User ${userEmail} ${action} ${resourceType} ${resourceId}`,
    success,
    ...context,
  });
}
