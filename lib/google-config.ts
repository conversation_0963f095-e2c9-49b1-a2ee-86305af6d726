/**
 * Google configuration utilities
 * Provides access to Google service configuration from environment variables
 */

/**
 * Get the Google Drive service account email from environment variables
 * This is used in UI components to show which email needs folder access
 */
export function getGoogleDriveServiceAccountEmail(): string {
  // Try different possible environment variable names for backward compatibility
  const email = process.env.GOOGLE_CLIENT_EMAIL || 
                process.env.GOOGLE_DRIVE_CLIENT_EMAIL || 
                process.env.GOOGLE_SERVICE_ACCOUNT_EMAIL;
  
  if (!email) {
    console.warn('[GOOGLE_CONFIG] Google Drive service account email not found in environment variables');
    return '<EMAIL>';
  }
  
  return email;
}

/**
 * Get Google Photos OAuth configuration
 */
export function getGooglePhotosOAuthConfig() {
  return {
    clientId: process.env.GOOGLE_PHOTOS_CLIENT_ID,
    clientSecret: process.env.GOOGLE_PHOTOS_CLIENT_SECRET,
    redirectUri: process.env.GOOGLE_PHOTOS_REDIRECT_URI,
  };
}

/**
 * Check if Google Drive is properly configured
 */
export function isGoogleDriveConfigured(): boolean {
  const email = process.env.GOOGLE_CLIENT_EMAIL ||
                process.env.GOOGLE_DRIVE_CLIENT_EMAIL ||
                process.env.GOOGLE_SERVICE_ACCOUNT_EMAIL;
  const privateKey = process.env.GOOGLE_PRIVATE_KEY || process.env.GOOGLE_DRIVE_PRIVATE_KEY;

  return !!(email && privateKey);
}

/**
 * Check if Google Photos OAuth is properly configured
 */
export function isGooglePhotosConfigured(): boolean {
  const config = getGooglePhotosOAuthConfig();
  return !!(config.clientId && config.clientSecret && config.redirectUri);
}
