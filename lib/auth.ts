import { createBrowserClient } from '@supabase/ssr'
import { createClient as createSupabaseClient } from '@supabase/supabase-js'
import type { Database } from '@/types/supabase'

// Modern client-side auth client
export function createClient() {
  return createBrowserClient<Database>(
    process.env.NEXT_PUBLIC_SUPABASE_URL!,
    process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!
  )
}

// Admin service client (for admin operations)
export function createAdminClient() {
  return createSupabaseClient<Database>(
    process.env.NEXT_PUBLIC_SUPABASE_URL!,
    process.env.SUPABASE_SERVICE_ROLE_KEY!,
    {
      auth: {
        autoRefreshToken: false,
        persistSession: false
      }
    }
  )
}

// Types for modern auth system
export interface AdminRole {
  id: string
  name: string
  description: string | null
  permissions: Record<string, string[]>
  created_at: string
  updated_at: string
}

export interface AdminProfile {
  id: string
  username: string | null
  email?: string | null
  full_name: string | null
  avatar_url: string | null
  is_active: boolean
  last_login_at: string | null
  created_at: string
  updated_at: string
}

export interface AdminUser extends AdminProfile {
  roles: AdminRole[]
}

// Modern auth utilities
export function hasPermission(
  userRoles: AdminRole[],
  resource: string,
  action: string
): boolean {
  return userRoles.some(role => {
    const resourcePermissions = role.permissions[resource]
    return resourcePermissions && resourcePermissions.includes(action)
  })
}

export function hasRole(userRoles: AdminRole[], roleName: string): boolean {
  return userRoles.some(role => role.name === roleName)
}

export function isSuperAdmin(userRoles: AdminRole[]): boolean {
  return hasRole(userRoles, 'super_admin')
}

export function isOwner(userRoles: AdminRole[]): boolean {
  return hasRole(userRoles, 'owner')
}




