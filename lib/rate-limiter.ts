import { NextRequest } from 'next/server';

// In-memory rate limiter (for production, use Redis)
const rateLimitMap = new Map<string, { count: number; timestamp: number }>();

interface RateLimitConfig {
  maxRequests: number;
  windowMs: number;
  skipSuccessfulRequests?: boolean;
  skipFailedRequests?: boolean;
}

export class RateLimiter {
  private config: RateLimitConfig;

  constructor(config: RateLimitConfig) {
    this.config = config;
  }

  private getKey(request: NextRequest): string {
    // Use IP address as the key, with fallbacks
    const forwarded = request.headers.get('x-forwarded-for');
    const realIp = request.headers.get('x-real-ip');
    const ip = forwarded?.split(',')[0] || realIp || 'unknown';
    
    // Include user agent for additional uniqueness
    const userAgent = request.headers.get('user-agent') || 'unknown';
    const hashedUA = this.simpleHash(userAgent);
    
    return `${ip}:${hashedUA}`;
  }

  private simpleHash(str: string): string {
    let hash = 0;
    for (let i = 0; i < str.length; i++) {
      const char = str.charCodeAt(i);
      hash = ((hash << 5) - hash) + char;
      hash = hash & hash; // Convert to 32-bit integer
    }
    return Math.abs(hash).toString(36);
  }

  private cleanupExpiredEntries(): void {
    const now = Date.now();
    const entries = Array.from(rateLimitMap.entries());
    for (const [key, data] of entries) {
      if (now - data.timestamp > this.config.windowMs) {
        rateLimitMap.delete(key);
      }
    }
  }

  public check(request: NextRequest): { allowed: boolean; remaining: number; resetTime: number } {
    this.cleanupExpiredEntries();
    
    const key = this.getKey(request);
    const now = Date.now();
    const windowStart = now - this.config.windowMs;
    
    const current = rateLimitMap.get(key);
    
    if (!current || current.timestamp < windowStart) {
      // First request in window or window has expired
      rateLimitMap.set(key, { count: 1, timestamp: now });
      return {
        allowed: true,
        remaining: this.config.maxRequests - 1,
        resetTime: now + this.config.windowMs
      };
    }
    
    if (current.count >= this.config.maxRequests) {
      // Rate limit exceeded
      return {
        allowed: false,
        remaining: 0,
        resetTime: current.timestamp + this.config.windowMs
      };
    }
    
    // Increment count
    current.count++;
    rateLimitMap.set(key, current);
    
    return {
      allowed: true,
      remaining: this.config.maxRequests - current.count,
      resetTime: current.timestamp + this.config.windowMs
    };
  }

  public static createHeaders(result: { remaining: number; resetTime: number }) {
    return {
      'X-RateLimit-Limit': String(result.remaining + 1),
      'X-RateLimit-Remaining': String(result.remaining),
      'X-RateLimit-Reset': String(Math.ceil(result.resetTime / 1000)),
    };
  }
}

// Predefined rate limiters for different endpoints
export const rateLimiters = {
  // Strict rate limiting for authentication endpoints
  auth: new RateLimiter({
    maxRequests: 5,
    windowMs: 15 * 60 * 1000, // 15 minutes
  }),
  
  // Moderate rate limiting for API endpoints
  api: new RateLimiter({
    maxRequests: 100,
    windowMs: 15 * 60 * 1000, // 15 minutes
  }),
  
  // Lenient rate limiting for contact forms
  contact: new RateLimiter({
    maxRequests: 10,
    windowMs: 60 * 60 * 1000, // 1 hour
  }),
  
  // Very strict for password reset
  passwordReset: new RateLimiter({
    maxRequests: 3,
    windowMs: 60 * 60 * 1000, // 1 hour
  }),
  
  // General rate limiting
  general: new RateLimiter({
    maxRequests: 1000,
    windowMs: 15 * 60 * 1000, // 15 minutes
  }),

  // Admin upload rate limiting - Very generous for batch operations
  adminUpload: new RateLimiter({
    maxRequests: 1000, // 1000 uploads per hour for admins
    windowMs: 60 * 60 * 1000, // 1 hour
  }),

  // Public upload rate limiting - More restrictive
  publicUpload: new RateLimiter({
    maxRequests: 50, // 50 uploads per hour for public users
    windowMs: 60 * 60 * 1000, // 1 hour
  }),
};

// Helper function to apply rate limiting to API routes
export function withRateLimit(
  handler: (request: NextRequest, ...args: any[]) => Promise<Response>,
  limiter: RateLimiter
) {
  return async (request: NextRequest, ...args: any[]): Promise<Response> => {
    const result = limiter.check(request);
    
    if (!result.allowed) {
      return new Response(
        JSON.stringify({
          error: 'Too many requests',
          message: 'Rate limit exceeded. Please try again later.',
          retryAfter: Math.ceil((result.resetTime - Date.now()) / 1000),
        }),
        {
          status: 429,
          headers: {
            'Content-Type': 'application/json',
            ...RateLimiter.createHeaders(result),
            'Retry-After': String(Math.ceil((result.resetTime - Date.now()) / 1000)),
          },
        }
      );
    }
    
    const response = await handler(request, ...args);
    
    // Add rate limit headers to successful responses
    const headers = RateLimiter.createHeaders(result);
    Object.entries(headers).forEach(([key, value]) => {
      response.headers.set(key, value);
    });
    
    return response;
  };
}
