// Debug utilities for Cloudinary operations
// Provides logging and comparison functions for image URL handling

/**
 * Log blog image replacement operations for debugging
 */
export function logBlogImageReplacement(
  blogId: string,
  oldUrl: string | null,
  newUrl: string | null,
  context: string = 'blog-update'
) {
  console.log(`🔄 [${context}] Blog ${blogId} image replacement:`);
  console.log(`  Old URL: ${oldUrl || 'none'}`);
  console.log(`  New URL: ${newUrl || 'none'}`);
  
  if (oldUrl && newUrl) {
    const sameImage = areCloudinaryUrlsSameImage(oldUrl, newUrl);
    console.log(`  Same image: ${sameImage ? '✅ Yes' : '❌ No'}`);
  }
}

/**
 * Compare two Cloudinary URLs to determine if they point to the same image
 * Handles different transformations of the same base image
 */
export function areCloudinaryUrlsSameImage(url1: string | null, url2: string | null): boolean {
  // If either URL is null/empty, they're not the same
  if (!url1 || !url2) {
    return false;
  }

  // If URLs are identical, they're the same
  if (url1 === url2) {
    return true;
  }

  // Extract public IDs from both URLs for comparison
  const publicId1 = extractCloudinaryPublicIdFromUrl(url1);
  const publicId2 = extractCloudinaryPublicIdFromUrl(url2);

  // If we couldn't extract public IDs, fall back to URL comparison
  if (!publicId1 || !publicId2) {
    return url1 === url2;
  }

  // Compare the public IDs (this ignores transformations)
  return publicId1 === publicId2;
}

/**
 * Extract Cloudinary public ID from URL with improved regex patterns for better accuracy and safety
 */
function extractCloudinaryPublicIdFromUrl(url: string): string | null {
  if (!url || !url.includes('cloudinary.com')) {
    return null;
  }

  try {
    // Use more precise regex patterns for better accuracy and safety
    // Cloudinary URL format: https://res.cloudinary.com/{cloud_name}/image/upload/{transformations}/{public_id}.{format}
    
    // Match the upload path and capture everything after it
    const uploadMatch = url.match(/\/upload\/(.+)$/);
    if (!uploadMatch) {
      return null;
    }

    const pathAfterUpload = uploadMatch[1];
    
    // Split by forward slashes to handle nested folders
    const pathParts = pathAfterUpload.split('/');
    
    // Remove transformation parameters using precise regex patterns
    const publicIdParts: string[] = [];
    let foundPublicId = false;

    for (const part of pathParts) {
      // Skip transformation parameters with improved pattern matching
      // Transformations typically follow patterns like: w_300, h_200, c_fill, q_auto, f_webp, etc.
      const isTransformation = /^[a-z]_[\w.]+$/.test(part);
      
      // Skip version folders (v followed by numbers)
      const isVersion = /^v\d+$/.test(part);
      
      if (!foundPublicId && (isTransformation || isVersion)) {
        continue;
      }
      
      foundPublicId = true;
      publicIdParts.push(part);
    }

    if (publicIdParts.length === 0) {
      return null;
    }

    // Join the parts and remove file extension from the last part
    const publicIdWithExtension = publicIdParts.join('/');
    
    // Remove file extension using precise regex
    const publicId = publicIdWithExtension.replace(/\.[a-zA-Z0-9]+$/, '');
    
    return publicId || null;
  } catch (error) {
    console.error('Error extracting Cloudinary public ID from URL:', error);
    return null;
  }
}

/**
 * Log trip image replacement operations for debugging
 */
export function logTripImageReplacement(
  tripId: string,
  oldUrl: string | null,
  newUrl: string | null,
  context: string = 'trip-update'
) {
  console.log(`🔄 [${context}] Trip ${tripId} image replacement:`);
  console.log(`  Old URL: ${oldUrl || 'none'}`);
  console.log(`  New URL: ${newUrl || 'none'}`);
  
  if (oldUrl && newUrl) {
    const sameImage = areCloudinaryUrlsSameImage(oldUrl, newUrl);
    console.log(`  Same image: ${sameImage ? '✅ Yes' : '❌ No'}`);
  }
}

/**
 * Log trips-photos image replacement operations for debugging
 */
export function logTripsPhotosImageReplacement(
  albumId: string,
  oldUrl: string | null,
  newUrl: string | null,
  context: string = 'trips-photos-update'
) {
  console.log(`🔄 [${context}] Trips-Photos Album ${albumId} image replacement:`);
  console.log(`  Old URL: ${oldUrl || 'none'}`);
  console.log(`  New URL: ${newUrl || 'none'}`);
  
  if (oldUrl && newUrl) {
    const sameImage = areCloudinaryUrlsSameImage(oldUrl, newUrl);
    console.log(`  Same image: ${sameImage ? '✅ Yes' : '❌ No'}`);
  }
}
