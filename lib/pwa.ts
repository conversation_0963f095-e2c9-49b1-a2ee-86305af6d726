'use client'

// Types for TypeScript
interface BeforeInstallPromptEvent extends Event {
  prompt(): Promise<void>
  userChoice: Promise<{ outcome: 'accepted' | 'dismissed' }>
}

// Extended notification options
interface ExtendedNotificationOptions extends NotificationOptions {
  vibrate?: number[]
}

// Extended ServiceWorkerRegistration
interface ExtendedServiceWorkerRegistration extends ServiceWorkerRegistration {
  sync: {
    register(tag: string): Promise<void>;
  };
}

// Service Worker Registration and PWA utilities
export class PWAManager {
  private static instance: PWAManager
  private registration: ServiceWorkerRegistration | null = null
  private isOnline = true
  private updateAvailable = false

  private constructor() {
    if (typeof window !== 'undefined') {
      this.isOnline = navigator.onLine
      this.setupEventListeners()
    }
  }

  static getInstance(): PWAManager {
    if (!PWAManager.instance) {
      PWAManager.instance = new PWAManager()
    }
    return PWAManager.instance
  }

  // Register service worker
  async registerServiceWorker(): Promise<void> {
    if ('serviceWorker' in navigator) {
      try {
        this.registration = await navigator.serviceWorker.register('/sw.js', {
          scope: '/'
        })

        console.log('Service Worker registered successfully:', this.registration)

        // Check for updates
        this.registration.addEventListener('updatefound', () => {
          const newWorker = this.registration?.installing
          if (newWorker) {
            newWorker.addEventListener('statechange', () => {
              if (newWorker.state === 'installed' && navigator.serviceWorker.controller) {
                this.updateAvailable = true
                this.notifyUpdateAvailable()
              }
            })
          }
        })

        // Handle controller change
        navigator.serviceWorker.addEventListener('controllerchange', () => {
          window.location.reload()
        })

      } catch (error) {
        console.error('Service Worker registration failed:', error)
      }
    }
  }

  // Update service worker
  async updateServiceWorker(): Promise<void> {
    if (this.registration?.waiting) {
      this.registration.waiting.postMessage({ type: 'SKIP_WAITING' })
    }
  }

  // Setup event listeners
  private setupEventListeners(): void {
    // Online/offline status
    window.addEventListener('online', () => {
      this.isOnline = true
      this.notifyOnlineStatus(true)
    })

    window.addEventListener('offline', () => {
      this.isOnline = false
      this.notifyOnlineStatus(false)
    })

    // Before install prompt
    window.addEventListener('beforeinstallprompt', (e) => {
      e.preventDefault()
      this.showInstallPrompt(e as any)
    })

    // App installed
    window.addEventListener('appinstalled', () => {
      console.log('PWA was installed')
      this.notifyAppInstalled()
    })
  }

  // Show install prompt
  private showInstallPrompt(event: BeforeInstallPromptEvent): void {
    const installButton = document.createElement('button')
    installButton.textContent = 'Install App'
    installButton.className = 'fixed bottom-4 right-4 bg-blue-600 text-white px-4 py-2 rounded-lg shadow-lg z-50'
    
    installButton.addEventListener('click', async () => {
      event.prompt()
      const { outcome } = await event.userChoice
      
      if (outcome === 'accepted') {
        console.log('User accepted the install prompt')
      } else {
        console.log('User dismissed the install prompt')
      }
      
      installButton.remove()
    })

    document.body.appendChild(installButton)

    // Auto-remove after 10 seconds
    setTimeout(() => {
      if (installButton.parentNode) {
        installButton.remove()
      }
    }, 10000)
  }

  // Notify update available
  private notifyUpdateAvailable(): void {
    if (typeof window !== 'undefined' && (window as any).announceToScreenReader) {
      (window as any).announceToScreenReader('App update available', true)
    }

    const updateBanner = document.createElement('div')
    updateBanner.className = 'fixed top-0 left-0 right-0 bg-blue-600 text-white p-4 z-50 text-center'
    updateBanner.innerHTML = `
      <p class="mb-2">A new version is available!</p>
      <button id="update-btn" class="bg-white text-blue-600 px-4 py-1 rounded mr-2">Update</button>
      <button id="dismiss-btn" class="border border-white px-4 py-1 rounded">Later</button>
    `

    document.body.appendChild(updateBanner)

    document.getElementById('update-btn')?.addEventListener('click', () => {
      this.updateServiceWorker()
      updateBanner.remove()
    })

    document.getElementById('dismiss-btn')?.addEventListener('click', () => {
      updateBanner.remove()
    })
  }

  // Notify online status
  private notifyOnlineStatus(isOnline: boolean): void {
    if (typeof window !== 'undefined' && (window as any).announceToScreenReader) {
      (window as any).announceToScreenReader(
        isOnline ? 'Connection restored' : 'Connection lost',
        true
      )
    }

    const statusBanner = document.createElement('div')
    statusBanner.className = `fixed top-0 left-0 right-0 p-2 z-50 text-center text-white ${
      isOnline ? 'bg-green-600' : 'bg-red-600'
    }`
    statusBanner.textContent = isOnline ? 'Connection restored' : 'You are offline'

    document.body.appendChild(statusBanner)

    setTimeout(() => {
      statusBanner.remove()
    }, 3000)
  }

  // Notify app installed
  private notifyAppInstalled(): void {
    if (typeof window !== 'undefined' && (window as any).announceToScreenReader) {
      (window as any).announceToScreenReader('App installed successfully', false)
    }
  }

  // Get online status
  getOnlineStatus(): boolean {
    return this.isOnline
  }

  // Check if update is available
  isUpdateAvailable(): boolean {
    return this.updateAvailable
  }

  // Cache important resources
  async cacheResources(urls: string[]): Promise<void> {
    if ('caches' in window) {
      try {
        const cache = await caches.open('positive7-dynamic-v1.0.0')
        await cache.addAll(urls)
        console.log('Resources cached successfully')
      } catch (error) {
        console.error('Failed to cache resources:', error)
      }
    }
  }

  // Clear old caches
  async clearOldCaches(): Promise<void> {
    if ('caches' in window) {
      try {
        const cacheNames = await caches.keys()
        const oldCaches = cacheNames.filter(name => 
          !name.includes('v1.0.0') && name.startsWith('positive7')
        )
        
        await Promise.all(oldCaches.map(name => caches.delete(name)))
        console.log('Old caches cleared')
      } catch (error) {
        console.error('Failed to clear old caches:', error)
      }
    }
  }

  // Request notification permission
  async requestNotificationPermission(): Promise<NotificationPermission> {
    if ('Notification' in window) {
      const permission = await Notification.requestPermission()
      console.log('Notification permission:', permission)
      return permission
    }
    return 'denied'
  }

  // Show notification
  async showNotification(title: string, options: ExtendedNotificationOptions = {}): Promise<void> {
    if ('Notification' in window && Notification.permission === 'granted') {
      if (this.registration) {
        await this.registration.showNotification(title, {
          icon: '/icons/icon-192x192.png',
          badge: '/icons/badge-72x72.png',
          vibrate: [100, 50, 100],
          ...options
        } as any)
      } else {
        new Notification(title, {
          icon: '/icons/icon-192x192.png',
          ...options
        })
      }
    }
  }

  // Background sync
  async requestBackgroundSync(tag: string): Promise<void> {
    if ('serviceWorker' in navigator && 'sync' in window.ServiceWorkerRegistration.prototype) {
      try {
        const reg = this.registration as ExtendedServiceWorkerRegistration | null;
        await reg?.sync.register(tag);
        console.log('Background sync registered:', tag);
      } catch (error) {
        console.error('Background sync registration failed:', error);
      }
    }
  }

  // Check if app is installed
  isAppInstalled(): boolean {
    return window.matchMedia('(display-mode: standalone)').matches ||
           (window.navigator as any).standalone === true
  }

  // Get app info
  getAppInfo(): { isInstalled: boolean; isOnline: boolean; updateAvailable: boolean } {
    return {
      isInstalled: this.isAppInstalled(),
      isOnline: this.isOnline,
      updateAvailable: this.updateAvailable
    }
  }
}

// Initialize PWA manager
export const pwaManager = PWAManager.getInstance()

// Auto-register service worker on load
if (typeof window !== 'undefined') {
  window.addEventListener('load', () => {
    pwaManager.registerServiceWorker()
  })
}
