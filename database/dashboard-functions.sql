-- Dashboard optimization functions for better performance
-- These functions provide aggregated statistics in a single query

-- Function to get trips dashboard statistics
CREATE OR REPLACE FUNCTION get_trips_dashboard_stats()
RETURNS TABLE (
  total_trips bigint,
  active_trips bigint,
  draft_trips bigint
) AS $$
  SELECT
    COUNT(*) as total_trips,
    COUNT(*) FILTER (WHERE is_active = true) as active_trips,
    COUNT(*) FILTER (WHERE is_active = false) as draft_trips
  FROM trips;
$$ LANGUAGE SQL STABLE SECURITY DEFINER;

-- Function to get blogs dashboard statistics
CREATE OR REPLACE FUNCTION get_blogs_dashboard_stats()
RETURNS TABLE (
  total_blogs bigint,
  published_blogs bigint,
  draft_blogs bigint
) AS $$
  SELECT
    COUNT(*) as total_blogs,
    COUNT(*) FILTER (WHERE is_published = true) as published_blogs,
    COUNT(*) FILTER (WHERE is_published = false) as draft_blogs
  FROM blog_posts;
$$ LANGUAGE SQL STABLE SECURITY DEFINER;

-- Function to get inquiries dashboard statistics
CREATE OR REPLACE FUNCTION get_inquiries_dashboard_stats()
RETURNS TABLE (
  total_inquiries bigint,
  new_inquiries bigint,
  responded_inquiries bigint
) AS $$
  SELECT
    COUNT(*) as total_inquiries,
    COUNT(*) FILTER (WHERE status = 'new') as new_inquiries,
    COUNT(*) FILTER (WHERE status = 'resolved') as responded_inquiries
  FROM inquiries;
$$ LANGUAGE SQL STABLE SECURITY DEFINER;

-- Grant execute permissions to authenticated users
GRANT EXECUTE ON FUNCTION get_trips_dashboard_stats() TO authenticated;
GRANT EXECUTE ON FUNCTION get_blogs_dashboard_stats() TO authenticated;
GRANT EXECUTE ON FUNCTION get_inquiries_dashboard_stats() TO authenticated;

-- Create indexes for better performance if they don't exist
CREATE INDEX IF NOT EXISTS idx_trips_is_active ON trips(is_active);
CREATE INDEX IF NOT EXISTS idx_trips_created_at ON trips(created_at DESC);
CREATE INDEX IF NOT EXISTS idx_blog_posts_is_published ON blog_posts(is_published);
CREATE INDEX IF NOT EXISTS idx_blog_posts_created_at ON blog_posts(created_at DESC);
CREATE INDEX IF NOT EXISTS idx_inquiries_status ON inquiries(status);
CREATE INDEX IF NOT EXISTS idx_inquiries_created_at ON inquiries(created_at DESC);
