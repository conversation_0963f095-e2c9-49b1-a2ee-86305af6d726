import { useStandardMutation } from '@/hooks/useStandardQuery';

/**
 * Modern error logging hook using standardized patterns
 */

export interface ErrorLogData {
  type: string;
  message: string;
  timestamp: string;
  url?: string;
  errorId?: string;
  userAgent?: string;
  stack?: string;
  componentStack?: string;
}

// API function for logging errors
const logError = async (errorData: ErrorLogData): Promise<void> => {
  const response = await fetch('/api/dev/log-error', {
    method: 'POST',
    headers: { 'Content-Type': 'application/json' },
    body: JSON.stringify(errorData),
  });

  if (!response.ok) {
    const errorInfo = await response.json().catch(() => ({}));
    throw new Error(errorInfo.error || `HTTP ${response.status}: Failed to log error`);
  }
};

// API function for logging general errors
const logGeneralError = async (errorData: ErrorLogData): Promise<void> => {
  const response = await fetch('/api/errors', {
    method: 'POST',
    headers: { 'Content-Type': 'application/json' },
    body: JSON.stringify(errorData),
  });

  if (!response.ok) {
    const errorInfo = await response.json().catch(() => ({}));
    throw new Error(errorInfo.error || `HTTP ${response.status}: Failed to log error`);
  }
};

/**
 * Hook for logging development errors (webpack, console errors, etc.)
 */
export const useDevErrorLogger = () => {
  return useStandardMutation<void, Error, ErrorLogData>(
    logError,
    {
      showSuccessToast: false, // Don't show success toast for error logging
      showErrorToast: false, // Don't show error toast for error logging (would cause loops)
      errorMessage: 'Failed to log development error',
    }
  );
};

/**
 * Hook for logging general application errors
 */
export const useGeneralErrorLogger = () => {
  return useStandardMutation<void, Error, ErrorLogData>(
    logGeneralError,
    {
      showSuccessToast: false, // Don't show success toast for error logging
      showErrorToast: false, // Don't show error toast for error logging (would cause loops)
      errorMessage: 'Failed to log application error',
    }
  );
};

/**
 * Utility function to create error log data
 */
export const createErrorLogData = (
  type: string,
  message: string,
  additionalData: Partial<ErrorLogData> = {}
): ErrorLogData => {
  return {
    type,
    message,
    timestamp: new Date().toISOString(),
    url: typeof window !== 'undefined' ? window.location.href : undefined,
    errorId: Math.random().toString(36).substring(2, 15),
    userAgent: typeof navigator !== 'undefined' ? navigator.userAgent : undefined,
    ...additionalData,
  };
};
