import { useStandardQuery } from '@/hooks/useStandardQuery';

/**
 * React Query hooks for audit logs management
 */

// Query keys for consistent caching
export const AUDIT_LOG_QUERY_KEYS = {
  auditLogs: ['audit-logs'] as const,
  auditLogsPaginated: (params: AuditLogQueryParams) => {
    // Create deterministic cache key by sorting object keys
    const sortedParams = Object.keys(params)
      .sort()
      .reduce((result, key) => {
        result[key] = params[key as keyof AuditLogQueryParams];
        return result;
      }, {} as Record<string, any>);
    return ['audit-logs', 'paginated', sortedParams] as const;
  },
  auditLogStatistics: (days: string) => ['audit-logs', 'statistics', days] as const,
} as const;

// Types for query parameters
export interface AuditLogQueryParams {
  page?: number;
  limit?: number;
  event_type?: string;
  severity?: string;
  success?: boolean; // Fix: should be boolean not string
  days?: string;
  start_date?: string;
}

export interface AuditLogEntry {
  id: string;
  event_type: string;
  severity: 'low' | 'medium' | 'high' | 'critical';
  user_id?: string;
  user_email?: string;
  ip_address?: string;
  user_agent?: string;
  resource_type?: string;
  resource_id?: string;
  action: string;
  description: string;
  metadata?: any;
  timestamp: string;
  session_id?: string;
  request_id?: string;
  success: boolean;
  error_message?: string;
}

export interface AuditStatistics {
  totalEvents: number;
  eventsByType: Record<string, number>;
  eventsBySeverity: Record<string, number>;
  failedEvents: number;
  topUsers: Array<{ user_email: string; count: number }>;
  topIPs: Array<{ ip_address: string; count: number }>;
}

export interface PaginatedAuditLogsResponse {
  logs: AuditLogEntry[];
  total: number;
  pagination: {
    page: number;
    limit: number;
    total: number;
    totalPages: number;
  };
}

// API functions
const fetchAuditLogs = async (params: AuditLogQueryParams = {}): Promise<PaginatedAuditLogsResponse> => {
  const searchParams = new URLSearchParams();
  
  if (params.page) searchParams.set('offset', ((params.page - 1) * (params.limit || 50)).toString());
  if (params.limit) searchParams.set('limit', params.limit.toString());
  if (params.event_type) searchParams.set('event_type', params.event_type);
  if (params.severity) searchParams.set('severity', params.severity);
  if (params.success !== undefined) searchParams.set('success', params.success.toString());
  if (params.days) {
    const startDate = new Date();
    startDate.setDate(startDate.getDate() - parseInt(params.days));
    searchParams.set('start_date', startDate.toISOString());
  }

  const response = await fetch(`/api/admin/audit-logs?${searchParams}`);

  if (!response.ok) {
    const errorData = await response.json().catch(() => ({}));
    throw new Error(errorData.error || `HTTP ${response.status}: Failed to fetch audit logs`);
  }

  const data = await response.json();
  
  return {
    logs: data.logs || [],
    total: data.total || 0,
    pagination: {
      page: params.page || 1,
      limit: params.limit || 50,
      total: data.total || 0,
      totalPages: Math.ceil((data.total || 0) / (params.limit || 50)),
    },
  };
};

const fetchAuditLogStatistics = async (days: string = '7'): Promise<AuditStatistics> => {
  const response = await fetch(`/api/admin/audit-logs/statistics?days=${days}`);

  if (!response.ok) {
    const errorData = await response.json().catch(() => ({}));
    throw new Error(errorData.error || `HTTP ${response.status}: Failed to fetch audit log statistics`);
  }

  return response.json();
};

// React Query hooks using modern patterns
export const useAuditLogs = (params: AuditLogQueryParams = {}) => {
  return useStandardQuery<PaginatedAuditLogsResponse>(
    [...AUDIT_LOG_QUERY_KEYS.auditLogsPaginated(params)],
    () => fetchAuditLogs(params),
    {
      errorMessage: 'Failed to fetch audit logs',
      placeholderData: (previousData) => previousData,
      staleTime: 1 * 60 * 1000, // 1 minute (audit logs should be fresh)
      gcTime: 5 * 60 * 1000, // 5 minutes
    }
  );
};

export const useAuditLogStatistics = (days: string = '7') => {
  return useStandardQuery<AuditStatistics>(
    [...AUDIT_LOG_QUERY_KEYS.auditLogStatistics(days)],
    () => fetchAuditLogStatistics(days),
    {
      errorMessage: 'Failed to fetch audit log statistics',
      staleTime: 2 * 60 * 1000, // 2 minutes
      gcTime: 10 * 60 * 1000, // 10 minutes
    }
  );
};

// Export logs function
export const exportAuditLogs = async (params: AuditLogQueryParams = {}): Promise<void> => {
  const searchParams = new URLSearchParams();
  
  if (params.event_type) searchParams.set('event_type', params.event_type);
  if (params.severity) searchParams.set('severity', params.severity);
  if (params.success !== undefined) searchParams.set('success', params.success.toString());
  if (params.days) {
    const startDate = new Date();
    startDate.setDate(startDate.getDate() - parseInt(params.days));
    searchParams.set('start_date', startDate.toISOString());
  }

  const response = await fetch(`/api/admin/audit-logs/export?${searchParams}`);
  
  if (!response.ok) {
    const errorData = await response.json().catch(() => ({}));
    throw new Error(errorData.error || 'Failed to export logs');
  }

  const blob = await response.blob();
  const url = window.URL.createObjectURL(blob);
  const a = document.createElement('a');
  a.href = url;
  a.download = `audit-logs-${new Date().toISOString().split('T')[0]}.csv`;
  document.body.appendChild(a);
  a.click();
  window.URL.revokeObjectURL(url);
  document.body.removeChild(a);
};
