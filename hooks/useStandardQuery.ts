/**
 * Standardized React Query Hook Pattern
 * 
 * This hook provides a consistent interface for all data fetching operations
 * with built-in error handling, loading states, and caching strategies.
 */

import React from 'react';
import { useQuery, useMutation, useQueryClient, useInfiniteQuery, UseQueryOptions, UseMutationOptions, UseInfiniteQueryOptions } from '@tanstack/react-query';
import { handleClientError } from '@/lib/error-handler';
import { useToast } from '@/hooks/useToast';
import { getQueryConfig, cacheInvalidation } from './useQueryConfig';

// Import types for better type safety
type ResourceType = 'trips' | 'blogs' | 'inquiries' | 'photos' | 'team-members' | 'admin-users';
type MutationOperation = 'create' | 'update' | 'delete';

// Standard query configuration - optimized for admin panels
const defaultQueryConfig = {
  staleTime: 2 * 60 * 1000, // 2 minutes - more reasonable for admin data
  gcTime: 15 * 60 * 1000, // 15 minutes - longer cache retention
  retry: (failureCount: number, error: unknown) => {
    const errorWithStatus = error as { status?: number };
    // Don't retry on 4xx errors
    if (errorWithStatus?.status && errorWithStatus.status >= 400 && errorWithStatus.status < 500) {
      return false;
    }
    // Retry up to 2 times for other errors (less aggressive)
    return failureCount < 2;
  },
  refetchOnWindowFocus: false, // Disabled for admin panels - use manual refresh instead
  refetchOnMount: true,
  refetchInterval: false, // Disabled - use smart real-time updates instead
  refetchOnReconnect: true,
  retryDelay: (attemptIndex: number) => Math.min(1000 * 2 ** attemptIndex, 30000),
} as const;

// Standard mutation configuration
const defaultMutationConfig = {
  retry: false,
} as const;

// Interface for standard query options
export interface StandardQueryOptions<TData, TError = Error> extends Omit<UseQueryOptions<TData, TError>, 'queryKey' | 'queryFn'> {
  showErrorToast?: boolean;
  errorMessage?: string;
  dataType?: 'dashboard' | 'list' | 'detail' | 'static' | 'public' | 'realtime';
}

// Interface for standard mutation options
export interface StandardMutationOptions<TData, TError = Error, TVariables = void> extends Omit<UseMutationOptions<TData, TError, TVariables>, 'mutationFn'> {
  showSuccessToast?: boolean;
  showErrorToast?: boolean;
  successMessage?: string;
  errorMessage?: string;
  invalidateQueries?: string[][];
  optimisticUpdate?: (variables: TVariables) => void;
  rollback?: (error: TError, variables: TVariables, context?: unknown) => void;
  // Smart cache invalidation options
  resourceType?: ResourceType;
  operation?: MutationOperation;
  useSmartInvalidation?: boolean;
}

/**
 * Standard query hook with consistent error handling and caching
 */
export function useStandardQuery<TData = unknown, TError = Error>(
  queryKey: (string | Record<string, any>)[],
  queryFn: () => Promise<TData>,
  options: StandardQueryOptions<TData, TError> = {}
) {
  const toast = useToast();
  const {
    showErrorToast = true,
    errorMessage = 'Failed to fetch data',
    dataType = 'list', // Default to list configuration
    ...queryOptions
  } = options;

  // Get optimized configuration based on data type
  const optimizedConfig = getQueryConfig(dataType);

  const result = useQuery<TData, TError>({
    queryKey: queryKey as readonly unknown[],
    queryFn: queryFn as any,
    ...defaultQueryConfig,
    ...optimizedConfig,
    ...queryOptions, // User options override defaults
  });

  // Handle errors using useEffect since onError is not available in v5
  React.useEffect(() => {
    if (result.error && showErrorToast) {
      const errorMsg = handleClientError(result.error);
      toast.error(errorMessage || errorMsg);
    }
  }, [result.error, showErrorToast, errorMessage, toast]);

  return result;
}

/**
 * Standard mutation hook with consistent error handling and success feedback
 */
export function useStandardMutation<TData = unknown, TError = Error, TVariables = void>(
  mutationFn: (variables: TVariables) => Promise<TData>,
  options: StandardMutationOptions<TData, TError, TVariables> = {}
) {
  const toast = useToast();
  const queryClient = useQueryClient();

  const {
    showSuccessToast = true,
    showErrorToast = true,
    successMessage = 'Operation completed successfully',
    errorMessage = 'Operation failed',
    invalidateQueries = [],
    optimisticUpdate,
    rollback,
    resourceType,
    operation = 'update',
    useSmartInvalidation = true,
    ...mutationOptions
  } = options;

  const result = useMutation<TData, TError, TVariables>({
    mutationFn,
    ...defaultMutationConfig,
    ...mutationOptions,
    onMutate: async (variables) => {
      // Apply optimistic update immediately
      optimisticUpdate?.(variables);
      return mutationOptions.onMutate?.(variables);
    },
    onSuccess: (data, variables, context) => {
      // Immediately invalidate cache when mutation succeeds
      if (useSmartInvalidation && resourceType) {
        cacheInvalidation.invalidateAfterMutation(queryClient, resourceType, operation);
      }

      // Also invalidate any manually specified queries
      invalidateQueries.forEach(queryKey => {
        queryClient.invalidateQueries({ queryKey });
      });

      // Call user's onSuccess callback
      mutationOptions.onSuccess?.(data, variables, context);
    },
    onError: (error, variables, context) => {
      // Rollback optimistic update on error - updated signature with context
      rollback?.(error, variables, context);
      mutationOptions.onError?.(error, variables, context);
    },
  });

  // Use refs to track if callbacks have been called to prevent duplicate invocations
  const successCallbackRef = React.useRef<boolean>(false);
  const errorCallbackRef = React.useRef<boolean>(false);
  const lastMutationIdRef = React.useRef<string>('');

  // Generate a unique ID for each mutation to track state properly
  const currentMutationId = React.useMemo(() => {
    return `${result.status}-${Date.now()}-${Math.random()}`;
  }, [result.status, result.submittedAt]);

  // Reset callback flags when a new mutation starts
  React.useEffect(() => {
    if (currentMutationId !== lastMutationIdRef.current) {
      successCallbackRef.current = false;
      errorCallbackRef.current = false;
      lastMutationIdRef.current = currentMutationId;
    }
  }, [currentMutationId]);

  // Handle success toast using useEffect (cache invalidation now happens immediately in onSuccess)
  React.useEffect(() => {
    if (result.isSuccess && !successCallbackRef.current) {
      successCallbackRef.current = true;

      // Show toast only if requested
      if (showSuccessToast) {
        toast.success(successMessage);
      }
    }
  }, [result.isSuccess, showSuccessToast, successMessage, toast]);

  React.useEffect(() => {
    if (result.error && showErrorToast && !errorCallbackRef.current) {
      errorCallbackRef.current = true;
      const errorMsg = handleClientError(result.error);
      toast.error(errorMessage || errorMsg);
    }
  }, [result.error, showErrorToast, errorMessage, toast]);

  return result;
}

/**
 * Hook for paginated queries with consistent interface
 */
export function usePaginatedQuery<TData = unknown, TError = Error>(
  baseQueryKey: (string | Record<string, any>)[],
  queryFn: (page: number, limit: number) => Promise<{ data: TData[]; total: number; totalPages: number }>,
  options: StandardQueryOptions<{ data: TData[]; total: number; totalPages: number }, TError> & {
    page?: number;
    limit?: number;
  } = {}
) {
  const { page = 1, limit = 10, ...queryOptions } = options;
  const queryKey = [...baseQueryKey, 'paginated', `page-${page}`, `limit-${limit}`];

  return useStandardQuery(
    queryKey,
    () => queryFn(page, limit),
    {
      ...queryOptions,
      placeholderData: (previousData) => previousData,
    }
  );
}

/**
 * Hook for infinite queries (load more pattern)
 */
export function useInfiniteStandardQuery<TData = unknown, TError = Error>(
  queryKey: (string | Record<string, any>)[],
  queryFn: ({ pageParam }: { pageParam: number }) => Promise<{ data: TData[]; nextPage?: number }>,
  options: Omit<UseInfiniteQueryOptions<{ data: TData[]; nextPage?: number }, TError>, 'queryKey' | 'queryFn' | 'getNextPageParam' | 'initialPageParam'> & {
    showErrorToast?: boolean;
    errorMessage?: string;
    initialPageParam?: number;
  } = {}
) {
  const toast = useToast();
  const {
    showErrorToast = true,
    errorMessage = 'Failed to fetch data',
    initialPageParam = 1,
    ...queryOptions
  } = options;

  const result = useInfiniteQuery({
    queryKey,
    queryFn: ({ pageParam }) => queryFn({ pageParam: pageParam as number }),
    getNextPageParam: (lastPage) => lastPage.nextPage,
    initialPageParam,
    ...defaultQueryConfig,
    ...queryOptions,
  });

  // Handle errors using useEffect
  React.useEffect(() => {
    if (result.error && showErrorToast) {
      const errorMsg = handleClientError(result.error);
      toast.error(errorMessage || errorMsg);
    }
  }, [result.error, showErrorToast, errorMessage, toast]);

  return result;
}

/**
 * Hook for optimistic updates
 */
export function useOptimisticMutation<TData = unknown, TError = Error, TVariables = void>(
  mutationFn: (variables: TVariables) => Promise<TData>,
  options: StandardMutationOptions<TData, TError, TVariables> & {
    optimisticUpdate?: (variables: TVariables) => void;
    rollback?: (error: TError, variables: TVariables, context?: unknown) => void;
  } = {}
) {
  const { optimisticUpdate, rollback, ...mutationOptions } = options;

  return useStandardMutation(mutationFn, {
    ...mutationOptions,
    onMutate: async (variables) => {
      // Apply optimistic update
      optimisticUpdate?.(variables);
      return mutationOptions.onMutate?.(variables);
    },
    onError: (error, variables, context) => {
      // Rollback optimistic update - updated signature with context
      rollback?.(error, variables, context);
      mutationOptions.onError?.(error, variables, context);
    },
  });
}

/**
 * Utility function to create query keys consistently
 */
export function createQueryKey(resource: string, params?: Record<string, any>): (string | Record<string, any>)[] {
  const key: (string | Record<string, any>)[] = [resource];
  if (params) {
    key.push(params);
  }
  return key;
}

/**
 * Utility function to prefetch data
 */
export function usePrefetchQuery<TData = unknown>(
  queryKey: (string | Record<string, any>)[],
  queryFn: () => Promise<TData>,
  options: { staleTime?: number } = {}
) {
  const queryClient = useQueryClient();
  
  const prefetch = () => {
    queryClient.prefetchQuery({
      queryKey,
      queryFn,
      staleTime: options.staleTime || defaultQueryConfig.staleTime,
    });
  };

  return { prefetch };
}

/**
 * Hook for dependent queries (queries that depend on other queries)
 */
export function useDependentQuery<TData = unknown, TError = Error>(
  queryKey: (string | Record<string, any>)[],
  queryFn: () => Promise<TData>,
  dependency: boolean,
  options: StandardQueryOptions<TData, TError> = {}
) {
  return useStandardQuery(queryKey, queryFn, {
    ...options,
    enabled: dependency && (options.enabled !== false),
  });
}
