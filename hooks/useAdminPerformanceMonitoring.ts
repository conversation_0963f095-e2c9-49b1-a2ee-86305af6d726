import { useEffect, useRef, useState, useCallback } from 'react';
import { useQueryClient } from '@tanstack/react-query';

/**
 * Enhanced performance monitoring specifically for admin panels
 * Tracks metrics, detects performance issues, and provides optimization suggestions
 */

interface PerformanceMetrics {
  // Page load metrics
  loadTime: number;
  domContentLoaded: number;
  firstPaint: number;
  largestContentfulPaint: number;
  firstInputDelay: number;
  cumulativeLayoutShift: number;
  
  // React Query metrics
  queryCount: number;
  cacheSize: number;
  staleCacheEntries: number;
  
  // Memory metrics
  memoryUsage: number;
  
  // Network metrics
  networkRequests: number;
  failedRequests: number;
  
  // User interaction metrics
  timeToInteractive: number;
  
  // Custom admin metrics
  adminPageLoadTime: number;
  listRenderTime: number;
  formSubmissionTime: number;
}

interface PerformanceAlert {
  type: 'warning' | 'error' | 'info';
  message: string;
  metric: keyof PerformanceMetrics;
  value: number;
  threshold: number;
  suggestion: string;
}

interface UseAdminPerformanceMonitoringOptions {
  enabled?: boolean;
  alertThresholds?: Partial<Record<keyof PerformanceMetrics, number>>;
  onAlert?: (alert: PerformanceAlert) => void;
  collectInterval?: number; // How often to collect metrics (ms)
  reportInterval?: number; // How often to report to server (ms)
  reportingEndpoint?: string; // Configurable endpoint for metrics reporting
  enableReporting?: boolean; // Whether to report metrics to server
  authToken?: string; // Authentication token for metrics reporting
}

// Default performance thresholds for admin panels (updated to July 2025 standards)
const DEFAULT_THRESHOLDS: Record<keyof PerformanceMetrics, number> = {
  loadTime: 3000, // 3 seconds - general page load time
  domContentLoaded: 2000, // 2 seconds - DOM ready time
  firstPaint: 1000, // 1 second - first paint time
  largestContentfulPaint: 2500, // 2.5 seconds - Core Web Vitals 2025 standard (good ≤ 2.5s)
  firstInputDelay: 200, // 200ms - Updated to match INP standards (INP replaced FID)
  cumulativeLayoutShift: 0.1, // 0.1 CLS score - Core Web Vitals 2025 standard (good ≤ 0.1)
  queryCount: 50, // Max 50 active queries
  cacheSize: 10 * 1024 * 1024, // 10MB cache size
  staleCacheEntries: 20, // Max 20 stale entries
  memoryUsage: 100 * 1024 * 1024, // 100MB memory usage
  networkRequests: 100, // Max 100 concurrent requests
  failedRequests: 5, // Max 5 failed requests
  timeToInteractive: 3500, // 3.5 seconds
  adminPageLoadTime: 2000, // 2 seconds for admin pages
  listRenderTime: 500, // 500ms for list rendering
  formSubmissionTime: 1000, // 1 second for form submission
};

export function useAdminPerformanceMonitoring({
  enabled = true,
  alertThresholds = {},
  onAlert,
  collectInterval = 5000, // 5 seconds
  reportInterval = 60000, // 1 minute
  reportingEndpoint = '/api/admin/performance/client-metrics',
  enableReporting = false, // Disabled by default for security
  authToken,
}: UseAdminPerformanceMonitoringOptions = {}) {
  const queryClient = useQueryClient();
  const [metrics, setMetrics] = useState<PerformanceMetrics>({} as PerformanceMetrics);
  const [alerts, setAlerts] = useState<PerformanceAlert[]>([]);
  const metricsRef = useRef<PerformanceMetrics>({} as PerformanceMetrics);
  const collectIntervalRef = useRef<number | null>(null);
  const reportIntervalRef = useRef<number | null>(null);
  
  const thresholds = { ...DEFAULT_THRESHOLDS, ...alertThresholds };

  // Collect performance metrics
  const collectMetrics = useCallback(async () => {
    if (!enabled) return;

    try {
      const newMetrics: Partial<PerformanceMetrics> = {};

      // Web Vitals and Navigation Timing
      if (typeof window !== 'undefined' && window.performance) {
        const navigation = performance.getEntriesByType('navigation')[0] as PerformanceNavigationTiming;
        const paint = performance.getEntriesByType('paint');
        
        if (navigation) {
          newMetrics.loadTime = navigation.loadEventEnd - navigation.loadEventStart;
          newMetrics.domContentLoaded = navigation.domContentLoadedEventEnd - navigation.domContentLoadedEventStart;
          newMetrics.timeToInteractive = navigation.domInteractive - navigation.fetchStart;
        }

        if (paint.length > 0) {
          const firstPaint = paint.find(entry => entry.name === 'first-paint');

          if (firstPaint) newMetrics.firstPaint = firstPaint.startTime;
          // Note: FCP and LCP are different metrics - LCP is measured separately below
        }

        // Measure LCP properly using PerformanceObserver
        try {
          const lcpEntries = performance.getEntriesByType('largest-contentful-paint');
          if (lcpEntries.length > 0) {
            const lastLcpEntry = lcpEntries[lcpEntries.length - 1] as PerformanceEntry;
            newMetrics.largestContentfulPaint = lastLcpEntry.startTime;
          }
        } catch (error) {
          // Fallback: if LCP is not available, we can use a reasonable estimate
          // but this should be rare in modern browsers
          console.warn('LCP measurement not available:', error);
        }

        // Memory usage (if available - Chrome-specific API)
        if ('memory' in performance) {
          const memory = (performance as any).memory;
          // Type check the memory object and its properties
          if (memory &&
              typeof memory === 'object' &&
              'usedJSHeapSize' in memory &&
              typeof memory.usedJSHeapSize === 'number') {
            newMetrics.memoryUsage = memory.usedJSHeapSize;
          }
        }
      }

      // React Query metrics
      const queryCache = queryClient.getQueryCache();
      const queries = queryCache.getAll();
      
      newMetrics.queryCount = queries.length;
      newMetrics.staleCacheEntries = queries.filter(query => query.isStale()).length;
      
      // Optimized cache size estimation
      let cacheSize = 0;
      let processedQueries = 0;
      const maxQueriesToProcess = 100; // Limit processing for performance

      for (const query of queries) {
        if (processedQueries >= maxQueriesToProcess) break;

        if (query.state.data) {
          try {
            // More efficient size estimation without full JSON.stringify
            const dataStr = typeof query.state.data === 'string'
              ? query.state.data
              : JSON.stringify(query.state.data);
            cacheSize += dataStr.length * 2; // UTF-16 byte estimate
            processedQueries++;
          } catch (_error) {
            // Ignore circular references and other serialization errors
            // Use a fallback size estimate for complex objects
            cacheSize += 1024; // 1KB fallback estimate
            processedQueries++;
          }
        }
      }

      // Extrapolate total cache size if we hit the limit
      if (queries.length > maxQueriesToProcess) {
        cacheSize = Math.round((cacheSize / processedQueries) * queries.length);
      }

      newMetrics.cacheSize = cacheSize;

      // Network metrics (if Performance Observer is available)
      if (typeof PerformanceObserver !== 'undefined') {
        const resourceEntries = performance.getEntriesByType('resource') as PerformanceResourceTiming[];
        newMetrics.networkRequests = resourceEntries.length;

        // Correctly detect failed requests by checking response status
        newMetrics.failedRequests = resourceEntries.filter(entry => {
          // Failed requests typically have:
          // 1. No response (duration > 0 but transferSize === 0 and responseEnd === 0)
          // 2. Or we can check for network errors (though status codes aren't available in Resource Timing API)
          const hasNetworkError = entry.duration > 0 &&
                                  entry.transferSize === 0 &&
                                  entry.responseEnd === 0;

          // Note: Resource Timing API doesn't expose HTTP status codes for security reasons
          // This is a limitation - for more accurate failed request detection,
          // we'd need to track this at the application level
          return hasNetworkError;
        }).length;
      }

      // Update metrics
      const updatedMetrics = { ...metricsRef.current, ...newMetrics } as PerformanceMetrics;
      metricsRef.current = updatedMetrics;
      setMetrics(updatedMetrics);

      // Check for performance alerts
      checkPerformanceAlerts(updatedMetrics);

    } catch (error) {
      console.error('Error collecting performance metrics:', error);
    }
  }, [enabled, queryClient, thresholds]);

  // Check for performance issues and generate alerts
  const checkPerformanceAlerts = useCallback((currentMetrics: PerformanceMetrics) => {
    const newAlerts: PerformanceAlert[] = [];

    Object.entries(thresholds).forEach(([metric, threshold]) => {
      const value = currentMetrics[metric as keyof PerformanceMetrics];
      
      if (typeof value === 'number' && value > threshold) {
        const alert: PerformanceAlert = {
          type: getAlertType(metric as keyof PerformanceMetrics, value, threshold),
          message: `${metric} exceeded threshold: ${value.toFixed(2)} > ${threshold}`,
          metric: metric as keyof PerformanceMetrics,
          value,
          threshold,
          suggestion: getOptimizationSuggestion(metric as keyof PerformanceMetrics)
        };
        
        newAlerts.push(alert);
        onAlert?.(alert);
      }
    });

    setAlerts(newAlerts);
  }, [thresholds, onAlert]);

  // Get alert type based on severity
  const getAlertType = (metric: keyof PerformanceMetrics, value: number, threshold: number): 'warning' | 'error' | 'info' => {
    const ratio = value / threshold;
    
    if (ratio > 2) return 'error';
    if (ratio > 1.5) return 'warning';
    return 'info';
  };

  // Get optimization suggestions
  const getOptimizationSuggestion = (metric: keyof PerformanceMetrics): string => {
    const suggestions: Record<keyof PerformanceMetrics, string> = {
      loadTime: 'Consider code splitting, lazy loading, or reducing bundle size',
      domContentLoaded: 'Optimize critical rendering path and reduce blocking resources',
      firstPaint: 'Minimize render-blocking resources and optimize CSS delivery',
      largestContentfulPaint: 'Optimize images, fonts, and critical resources',
      firstInputDelay: 'Optimize interaction responsiveness - reduce JavaScript execution time, use web workers, and minimize main thread blocking',
      cumulativeLayoutShift: 'Set explicit dimensions for images and avoid dynamic content insertion',
      queryCount: 'Consider query deduplication and cleanup unused queries',
      cacheSize: 'Implement cache cleanup strategies and reduce cached data size',
      staleCacheEntries: 'Implement better cache invalidation strategies',
      memoryUsage: 'Check for memory leaks and optimize data structures',
      networkRequests: 'Implement request batching and reduce unnecessary API calls',
      failedRequests: 'Implement better error handling and retry mechanisms',
      timeToInteractive: 'Optimize JavaScript bundle size and execution',
      adminPageLoadTime: 'Implement lazy loading for admin components',
      listRenderTime: 'Use virtual scrolling for large lists',
      formSubmissionTime: 'Optimize form validation and submission logic'
    };
    
    return suggestions[metric] || 'Review and optimize this metric';
  };

  // Report metrics to server
  const reportMetrics = useCallback(async () => {
    if (!enabled || !enableReporting || !metricsRef.current.loadTime) return;

    try {
      const headers: Record<string, string> = {
        'Content-Type': 'application/json',
      };

      // Add authentication header if token is provided
      if (authToken) {
        headers['Authorization'] = `Bearer ${authToken}`;
      }

      await fetch(reportingEndpoint, {
        method: 'POST',
        headers,
        body: JSON.stringify({
          page_name: window.location.pathname,
          timestamp: Date.now(),
          metrics: metricsRef.current,
          alerts: alerts.length,
          user_agent: navigator.userAgent,
        }),
      });
    } catch (error) {
      console.error('Error reporting performance metrics:', error);
    }
  }, [enabled, enableReporting, reportingEndpoint, authToken]);

  // Set up metric collection interval
  useEffect(() => {
    if (!enabled) return;

    // Initial collection
    collectMetrics();

    // Set up intervals (cast to number for browser compatibility)
    collectIntervalRef.current = setInterval(collectMetrics, collectInterval) as unknown as number;
    reportIntervalRef.current = setInterval(reportMetrics, reportInterval) as unknown as number;

    return () => {
      if (collectIntervalRef.current) clearInterval(collectIntervalRef.current);
      if (reportIntervalRef.current) clearInterval(reportIntervalRef.current);
    };
  }, [enabled, collectMetrics, reportMetrics, collectInterval, reportInterval]);

  // Manual performance check
  const runPerformanceCheck = useCallback(() => {
    collectMetrics();
  }, [collectMetrics]);

  // Clear alerts
  const clearAlerts = useCallback(() => {
    setAlerts([]);
  }, []);

  return {
    metrics,
    alerts,
    runPerformanceCheck,
    clearAlerts,
    isMonitoring: enabled,
  };
}
