import { useQueryClient, useMutation } from '@tanstack/react-query';
import { Trip, TripDifficulty, TripFormData } from '@/types/trip';
import { ClientTrip, toClientTrip } from '@/types/client-trip';
import { handleClientError } from '@/lib/error-handler';
import { useStandardQuery, useStandardMutation } from '@/hooks/useStandardQuery';

/**
 * React Query hooks for trip management
 */

// Query keys for consistent caching
export const TRIP_QUERY_KEYS = {
  trips: ['trips'] as const,
  trip: (id: string) => ['trips', id] as const,
  tripsPaginated: (params: TripQueryParams) => ['trips', 'paginated', params] as const,
  publicTrips: ['public-trips'] as const,
} as const;

// Types for query parameters
export interface TripQueryParams {
  page?: number;
  limit?: number;
  search?: string;
  destination?: string;
  difficulty?: TripDifficulty | 'all';
  isActive?: string | null;
  isFeatured?: boolean;
}

export interface PaginatedTripsResponse {
  data: Trip[];
  pagination: {
    page: number;
    limit: number;
    total: number;
    totalPages: number;
  };
}

// Use TripFormData directly for create/update operations
export type CreateTripRequest = TripFormData;

export interface UpdateTripRequest extends Partial<TripFormData> {
  id: string;
}

// API functions
const fetchTrips = async (params: TripQueryParams = {}): Promise<PaginatedTripsResponse> => {
  const searchParams = new URLSearchParams();
  
  if (params.page) searchParams.set('page', params.page.toString());
  if (params.limit) searchParams.set('limit', params.limit.toString());
  if (params.search) searchParams.set('search', params.search);
  if (params.destination && params.destination !== 'all') searchParams.set('destination', params.destination);
  if (params.difficulty && params.difficulty !== 'all') searchParams.set('difficulty', params.difficulty);
  if (params.isActive !== null && params.isActive !== undefined) {
    searchParams.set('isActive', params.isActive);
  }
  if (params.isFeatured !== undefined) searchParams.set('isFeatured', params.isFeatured.toString());

  const response = await fetch(`/api/admin/trips?${searchParams}`);
  
  if (!response.ok) {
    const errorData = await response.json().catch(() => ({}));
    throw new Error(errorData.error || `HTTP ${response.status}: Failed to fetch trips`);
  }

  return response.json();
};

const fetchTrip = async (id: string): Promise<Trip> => {
  const response = await fetch(`/api/admin/trips/${id}`);
  
  if (!response.ok) {
    const errorData = await response.json().catch(() => ({}));
    throw new Error(errorData.error || `HTTP ${response.status}: Failed to fetch trip`);
  }

  const data = await response.json();
  return data.data;
};

const createTrip = async (tripData: CreateTripRequest): Promise<Trip> => {
  const response = await fetch('/api/admin/trips', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    body: JSON.stringify(tripData),
  });

  if (!response.ok) {
    const errorData = await response.json().catch(() => ({}));
    throw new Error(errorData.error || `HTTP ${response.status}: Failed to create trip`);
  }

  const data = await response.json();
  return data.data;
};

const updateTrip = async ({ id, ...tripData }: UpdateTripRequest): Promise<Trip> => {
  const response = await fetch(`/api/admin/trips/${id}`, {
    method: 'PUT',
    headers: {
      'Content-Type': 'application/json',
    },
    body: JSON.stringify(tripData),
  });

  if (!response.ok) {
    const errorData = await response.json().catch(() => ({}));
    throw new Error(errorData.error || `HTTP ${response.status}: Failed to update trip`);
  }

  const data = await response.json();
  return data.data;
};

const deleteTrip = async (id: string): Promise<void> => {
  const response = await fetch(`/api/admin/trips/${id}`, {
    method: 'DELETE',
  });

  if (!response.ok) {
    const errorData = await response.json().catch(() => ({}));
    throw new Error(errorData.error || `HTTP ${response.status}: Failed to delete trip`);
  }
};

// React Query hooks
export const useTrips = (params: TripQueryParams = {}) => {
  return useStandardQuery<PaginatedTripsResponse>(
    [...TRIP_QUERY_KEYS.tripsPaginated(params)],
    () => fetchTrips(params),
    {
      errorMessage: 'Failed to fetch trips',
      dataType: 'list', // Use optimized list configuration
      placeholderData: (previousData) => previousData,
    }
  );
};

export const useTrip = (id: string, enabled: boolean = true) => {
  return useStandardQuery<Trip>(
    [...TRIP_QUERY_KEYS.trip(id)],
    () => fetchTrip(id),
    {
      enabled: enabled && !!id,
      errorMessage: 'Failed to fetch trip details',
      dataType: 'detail', // Use optimized detail configuration
    }
  );
};

export const useCreateTrip = () => {
  const queryClient = useQueryClient();

  return useStandardMutation<Trip, Error, CreateTripRequest>(
    createTrip,
    {
      successMessage: 'Trip created successfully',
      errorMessage: 'Failed to create trip',
      resourceType: 'trips',
      operation: 'create',
      useSmartInvalidation: true,
      onSuccess: (newTrip) => {
        // Add the new trip to the cache immediately
        queryClient.setQueryData(TRIP_QUERY_KEYS.trip(newTrip.id), newTrip);
      },
    }
  );
};

export const useUpdateTrip = () => {
  const queryClient = useQueryClient();

  return useStandardMutation<Trip, Error, UpdateTripRequest>(
    updateTrip,
    {
      successMessage: 'Trip updated successfully',
      errorMessage: 'Failed to update trip',
      resourceType: 'trips',
      operation: 'update',
      useSmartInvalidation: true,
      optimisticUpdate: (tripData) => {
        // Optimistically update the trip in cache
        queryClient.setQueryData(TRIP_QUERY_KEYS.trip(tripData.id), (oldData: Trip | undefined) => {
          if (!oldData) return oldData;
          return { ...oldData, ...tripData };
        });
      },
      onSuccess: (updatedTrip) => {
        // Update with real data from server
        queryClient.setQueryData(TRIP_QUERY_KEYS.trip(updatedTrip.id), updatedTrip);
      },
    }
  );
};

export const useDeleteTrip = () => {
  const queryClient = useQueryClient();

  return useStandardMutation<void, Error, string>(
    deleteTrip,
    {
      successMessage: 'Trip deleted successfully',
      errorMessage: 'Failed to delete trip',
      resourceType: 'trips',
      operation: 'delete',
      useSmartInvalidation: true,
      optimisticUpdate: (deletedId) => {
        // Optimistically remove from cache
        queryClient.removeQueries({ queryKey: TRIP_QUERY_KEYS.trip(deletedId) });
      },
      onSuccess: (_, deletedId) => {
        // Ensure removal from cache
        queryClient.removeQueries({ queryKey: TRIP_QUERY_KEYS.trip(deletedId) });
      },
    }
  );
};

// Hook for toggling trip active status
export const useToggleTripStatus = () => {
  const queryClient = useQueryClient();

  return useStandardMutation<Trip, Error, { id: string; isActive: boolean }>(
    async ({ id, isActive }) => {
      // First get the current trip data
      const currentTrip = queryClient.getQueryData(TRIP_QUERY_KEYS.trip(id)) as Trip;

      if (!currentTrip) {
        // If not in cache, fetch it
        const trip = await fetchTrip(id);
        return updateTrip({ ...trip, id, is_active: isActive });
      }

      return updateTrip({ ...currentTrip, id, is_active: isActive });
    },
    {
      successMessage: 'Trip status updated successfully',
      errorMessage: 'Failed to update trip status',
      resourceType: 'trips',
      operation: 'update',
      useSmartInvalidation: true,
      showSuccessToast: true, // Keep toast but make it faster
      optimisticUpdate: ({ id, isActive }) => {
        // Optimistically update the trip status in detail view
        queryClient.setQueryData(TRIP_QUERY_KEYS.trip(id), (oldData: Trip | undefined) => {
          if (!oldData) return oldData;
          return { ...oldData, is_active: isActive };
        });

        // Also update all paginated trip lists
        queryClient.setQueriesData(
          { queryKey: ['trips', 'paginated'] },
          (oldData: any) => {
            if (!oldData?.data) return oldData;
            return {
              ...oldData,
              data: oldData.data.map((trip: Trip) =>
                trip.id === id ? { ...trip, is_active: isActive } : trip
              )
            };
          }
        );
      },
      onSuccess: (updatedTrip, { id }) => {
        // Update with real data from server in detail view
        queryClient.setQueryData(TRIP_QUERY_KEYS.trip(id), updatedTrip);

        // Also update all paginated trip lists with real data
        queryClient.setQueriesData(
          { queryKey: ['trips', 'paginated'] },
          (oldData: any) => {
            if (!oldData?.data) return oldData;
            return {
              ...oldData,
              data: oldData.data.map((trip: Trip) =>
                trip.id === id ? updatedTrip : trip
              )
            };
          }
        );
      },
    }
  );
};

// Hook for toggling trip featured status
export const useToggleTripFeatured = () => {
  const queryClient = useQueryClient();

  return useStandardMutation<Trip, Error, { id: string; isFeatured: boolean }>(
    async ({ id, isFeatured }) => {
      // First get the current trip data
      const currentTrip = queryClient.getQueryData(TRIP_QUERY_KEYS.trip(id)) as Trip;

      if (!currentTrip) {
        // If not in cache, fetch it
        const trip = await fetchTrip(id);
        return updateTrip({ ...trip, id, is_featured: isFeatured });
      }

      return updateTrip({ ...currentTrip, id, is_featured: isFeatured });
    },
    {
      successMessage: 'Trip featured status updated successfully',
      errorMessage: 'Failed to update trip featured status',
      resourceType: 'trips',
      operation: 'update',
      useSmartInvalidation: true,
      showSuccessToast: true, // Keep toast but make it faster
      optimisticUpdate: ({ id, isFeatured }) => {
        // Optimistically update the trip featured status in detail view
        queryClient.setQueryData(TRIP_QUERY_KEYS.trip(id), (oldData: Trip | undefined) => {
          if (!oldData) return oldData;
          return { ...oldData, is_featured: isFeatured };
        });

        // Also update all paginated trip lists
        queryClient.setQueriesData(
          { queryKey: ['trips', 'paginated'] },
          (oldData: any) => {
            if (!oldData?.data) return oldData;
            return {
              ...oldData,
              data: oldData.data.map((trip: Trip) =>
                trip.id === id ? { ...trip, is_featured: isFeatured } : trip
              )
            };
          }
        );
      },
      onSuccess: (updatedTrip, { id }) => {
        // Update with real data from server in detail view
        queryClient.setQueryData(TRIP_QUERY_KEYS.trip(id), updatedTrip);

        // Also update all paginated trip lists with real data
        queryClient.setQueriesData(
          { queryKey: ['trips', 'paginated'] },
          (oldData: any) => {
            if (!oldData?.data) return oldData;
            return {
              ...oldData,
              data: oldData.data.map((trip: Trip) =>
                trip.id === id ? updatedTrip : trip
              )
            };
          }
        );
      },
    }
  );
};

// Hook for public trips (no authentication required)
export const usePublicTrips = (params: Omit<TripQueryParams, 'isActive'> = {}) => {
  return useStandardQuery(
    ['public-trips', params],
    async () => {
      const searchParams = new URLSearchParams();

      if (params.page) searchParams.set('page', params.page.toString());
      if (params.limit) searchParams.set('limit', params.limit.toString());
      if (params.search) searchParams.set('search', params.search);
      if (params.destination && params.destination !== 'all') searchParams.set('destination', params.destination);
      if (params.difficulty && params.difficulty !== 'all') searchParams.set('difficulty', params.difficulty);
      if (params.isFeatured !== undefined) searchParams.set('isFeatured', params.isFeatured.toString());

      const response = await fetch(`/api/trips?${searchParams}`);

      if (!response.ok) {
        throw new Error('Failed to fetch public trips');
      }

      return response.json();
    },
    {
      errorMessage: 'Failed to fetch public trips',
      dataType: 'public', // Use optimized public configuration
    }
  );
};

// Hook for public trip detail (no authentication required)
export const usePublicTrip = (slug: string, enabled: boolean = true) => {
  return useStandardQuery<ClientTrip>(
    ['public-trip', slug],
    async () => {
      const response = await fetch(`/api/trips/${slug}`);

      if (!response.ok) {
        throw new Error('Failed to fetch trip');
      }

      const data = await response.json();
      // Transform to client trip to exclude internal fields
      return toClientTrip(data);
    },
    {
      enabled: enabled && !!slug,
      errorMessage: 'Failed to fetch trip details',
      dataType: 'public', // Use optimized public configuration
    }
  );
};

// Note: Error handling is now built into useStandardMutation
// No need for additional error handling wrappers
