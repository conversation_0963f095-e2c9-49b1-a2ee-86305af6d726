'use client';

import { useEffect, useRef } from 'react';
import { usePathname } from 'next/navigation';

interface PagePerformanceMetric {
  page_name: string;
  load_time: number;
  dom_content_loaded: number;
  first_paint: number;
  largest_contentful_paint?: number;
  first_input_delay?: number;
  cumulative_layout_shift?: number;
}

// Send performance metrics to the server
async function sendPerformanceMetric(metric: PagePerformanceMetric) {
  try {
    await fetch('/api/admin/performance/client-metrics', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(metric),
    });
  } catch (error) {
    // Silently fail - don't disrupt user experience
    // Admin will check performance tab instead of console
  }
}

// Hook to automatically track page performance
export function usePagePerformance(pageName?: string | null) {
  const pathname = usePathname();
  const hasTracked = useRef(false);
  const pageNameToUse = pageName || pathname;

  useEffect(() => {
    // Only track once per page load
    if (hasTracked.current) return;

    // Only track in browser environment
    if (typeof window === 'undefined') return;

    // Don't track if pageName is null (disabled)
    if (pageName === null) return;

    // Store observer references for cleanup
    const observers: PerformanceObserver[] = [];

    const trackPerformance = () => {
      try {
        const navigation = performance.getEntriesByType('navigation')[0] as PerformanceNavigationTiming;

        if (!navigation) return;

        // Validate timing calculations to avoid negative values
        const validateTiming = (end: number, start: number): number => {
          if (!end || !start || end < start) return 0;
          return Math.max(0, end - start);
        };

        const metric: PagePerformanceMetric = {
          page_name: pageNameToUse,
          load_time: validateTiming(navigation.loadEventEnd, navigation.fetchStart),
          dom_content_loaded: validateTiming(navigation.domContentLoadedEventEnd, navigation.fetchStart),
          first_paint: validateTiming(navigation.responseEnd, navigation.fetchStart),
        };

        // Get additional Web Vitals if available
        if ('PerformanceObserver' in window) {
          // Track LCP (Largest Contentful Paint)
          try {
            const lcpObserver = new PerformanceObserver((list) => {
              const entries = list.getEntries();
              const lastEntry = entries[entries.length - 1] as any;
              if (lastEntry) {
                metric.largest_contentful_paint = lastEntry.startTime;
              }
            });
            lcpObserver.observe({ entryTypes: ['largest-contentful-paint'] });
            observers.push(lcpObserver);
          } catch (e) {
            // LCP not supported
          }

          // Track FID (First Input Delay)
          try {
            const fidObserver = new PerformanceObserver((list) => {
              const entries = list.getEntries();
              entries.forEach((entry: any) => {
                if (entry.processingStart && entry.startTime) {
                  metric.first_input_delay = entry.processingStart - entry.startTime;
                }
              });
            });
            fidObserver.observe({ entryTypes: ['first-input'] });
            observers.push(fidObserver);
          } catch (e) {
            // FID not supported
          }

          // Track CLS (Cumulative Layout Shift)
          try {
            let clsValue = 0;
            const clsObserver = new PerformanceObserver((list) => {
              const entries = list.getEntries();
              entries.forEach((entry: any) => {
                if (!entry.hadRecentInput) {
                  clsValue += entry.value;
                }
              });
              metric.cumulative_layout_shift = clsValue;
            });
            clsObserver.observe({ entryTypes: ['layout-shift'] });
            observers.push(clsObserver);
          } catch (e) {
            // CLS not supported
          }
        }

        // Send the metric after a short delay to capture additional metrics
        setTimeout(() => {
          sendPerformanceMetric(metric);
          hasTracked.current = true;
        }, 1000);

      } catch (error) {
        // Silently fail - admin will check performance tab instead
      }
    };

    // Track performance when page is fully loaded
    if (document.readyState === 'complete') {
      trackPerformance();
    } else {
      window.addEventListener('load', trackPerformance);
    }

    // Cleanup function to disconnect observers and remove event listeners
    return () => {
      window.removeEventListener('load', trackPerformance);
      observers.forEach(observer => {
        try {
          observer.disconnect();
        } catch (e) {
          // Observer already disconnected or not supported
        }
      });
    };
  }, [pageNameToUse, pageName]);

  // Reset tracking when pathname changes
  useEffect(() => {
    hasTracked.current = false;
  }, [pathname]);
}

// Hook for manual performance tracking
export function useManualPerformanceTracking() {
  const trackCustomMetric = async (metricName: string, value: number, unit: string = 'ms') => {
    try {
      await fetch('/api/admin/performance/custom-metrics', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          metric_name: metricName,
          value,
          unit,
          timestamp: new Date().toISOString(),
        }),
      });
    } catch (error) {
      // Silently fail - admin will check performance tab instead
    }
  };

  const startTimer = (operationName: string) => {
    const startTime = performance.now();
    
    return {
      end: () => {
        const duration = performance.now() - startTime;
        trackCustomMetric(operationName, duration);
        return duration;
      }
    };
  };

  return {
    trackCustomMetric,
    startTimer,
  };
}
