import { useQueryClient } from '@tanstack/react-query';
import { useStandardQuery, useStandardMutation } from '@/hooks/useStandardQuery';

// Query keys for consistent caching
export const TEAM_MEMBER_QUERY_KEYS = {
  teamMembers: ['team-members'] as const,
  teamMember: (id: string) => ['team-members', id] as const,
  teamMembersPaginated: (params: TeamMemberQueryParams) => ['team-members', 'paginated', params] as const,
} as const;

// Types
export interface TeamMember {
  id: string;
  name: string;
  position: string; // Database field is 'position', not 'role'
  bio: string; // Database field is NOT NULL
  image_url: string | null;
  is_active: boolean;
  sort_order: number; // Database field is 'sort_order', not 'display_order'
  created_at: string;
  updated_at: string;
}

export interface TeamMemberFormData {
  name: string;
  position: string; // Database field is 'position', not 'role'
  bio: string; // Database field is NOT NULL, so required in form
  image_url?: string;
  is_active?: boolean;
  sort_order?: number; // Database field is 'sort_order', not 'display_order'
}

export interface CreateTeamMemberRequest extends TeamMemberFormData {}
export interface UpdateTeamMemberRequest extends Partial<TeamMemberFormData> {
  id: string;
}

export interface TeamMemberQueryParams {
  page?: number;
  limit?: number;
  search?: string;
  isActive?: string;
}

export interface PaginatedTeamMembersResponse {
  teamMembers: TeamMember[];
  pagination: {
    page: number;
    limit: number;
    total: number;
    totalPages: number;
  };
}

// API functions
const fetchTeamMembers = async (params: TeamMemberQueryParams = {}): Promise<PaginatedTeamMembersResponse> => {
  const searchParams = new URLSearchParams();
  
  if (params.page) searchParams.set('page', params.page.toString());
  if (params.limit) searchParams.set('limit', params.limit.toString());
  if (params.search) searchParams.set('search', params.search);
  if (params.isActive && params.isActive !== 'all') searchParams.set('isActive', params.isActive);

  const response = await fetch(`/api/admin/team-members?${searchParams}`);
  
  if (!response.ok) {
    const errorData = await response.json().catch(() => ({}));
    throw new Error(errorData.error || `HTTP ${response.status}: Failed to fetch team members`);
  }
  
  return response.json();
};

const fetchTeamMember = async (id: string): Promise<TeamMember> => {
  const response = await fetch(`/api/admin/team-members/${id}`);
  
  if (!response.ok) {
    const errorData = await response.json().catch(() => ({}));
    throw new Error(errorData.error || `HTTP ${response.status}: Failed to fetch team member`);
  }
  
  const data = await response.json();
  return data.teamMember;
};

const createTeamMember = async (memberData: CreateTeamMemberRequest): Promise<TeamMember> => {
  const response = await fetch('/api/admin/team-members', {
    method: 'POST',
    headers: { 'Content-Type': 'application/json' },
    body: JSON.stringify(memberData),
  });

  if (!response.ok) {
    const errorData = await response.json().catch(() => ({}));
    throw new Error(errorData.error || `HTTP ${response.status}: Failed to create team member`);
  }

  const data = await response.json();
  return data.teamMember;
};

const updateTeamMember = async ({ id, ...memberData }: UpdateTeamMemberRequest): Promise<TeamMember> => {
  const response = await fetch(`/api/admin/team-members/${id}`, {
    method: 'PUT',
    headers: { 'Content-Type': 'application/json' },
    body: JSON.stringify(memberData),
  });

  if (!response.ok) {
    const errorData = await response.json().catch(() => ({}));
    throw new Error(errorData.error || `HTTP ${response.status}: Failed to update team member`);
  }

  const data = await response.json();
  return data.teamMember;
};

const deleteTeamMember = async (id: string): Promise<void> => {
  const response = await fetch(`/api/admin/team-members/${id}`, {
    method: 'DELETE',
  });

  if (!response.ok) {
    const errorData = await response.json().catch(() => ({}));
    throw new Error(errorData.error || `HTTP ${response.status}: Failed to delete team member`);
  }
};

// React Query hooks
export const useTeamMembers = (params: TeamMemberQueryParams = {}) => {
  return useStandardQuery<PaginatedTeamMembersResponse>(
    [...TEAM_MEMBER_QUERY_KEYS.teamMembersPaginated(params)],
    () => fetchTeamMembers(params),
    {
      errorMessage: 'Failed to fetch team members',
      placeholderData: (previousData) => previousData,
    }
  );
};

export const useTeamMember = (id: string, enabled: boolean = true) => {
  return useStandardQuery<TeamMember>(
    [...TEAM_MEMBER_QUERY_KEYS.teamMember(id)],
    () => fetchTeamMember(id),
    {
      enabled: enabled && !!id,
      errorMessage: 'Failed to fetch team member details',
    }
  );
};

export const useCreateTeamMember = () => {
  const queryClient = useQueryClient();

  return useStandardMutation<TeamMember, Error, CreateTeamMemberRequest>(
    createTeamMember,
    {
      successMessage: 'Team member created successfully',
      errorMessage: 'Failed to create team member',
      invalidateQueries: [
        [...TEAM_MEMBER_QUERY_KEYS.teamMembers],
        ['team-members', 'paginated'],
        ['public-team-members'] // Invalidate public team members cache
      ],
      onSuccess: (newMember) => {
        // Add the new member to the cache immediately
        queryClient.setQueryData([...TEAM_MEMBER_QUERY_KEYS.teamMember(newMember.id)], newMember);
      },
    }
  );
};

export const useUpdateTeamMember = () => {
  const queryClient = useQueryClient();

  return useStandardMutation<TeamMember, Error, UpdateTeamMemberRequest>(
    updateTeamMember,
    {
      successMessage: 'Team member updated successfully',
      errorMessage: 'Failed to update team member',
      invalidateQueries: [
        [...TEAM_MEMBER_QUERY_KEYS.teamMembers],
        ['team-members', 'paginated'],
        ['public-team-members'] // Invalidate public team members cache
      ],
      optimisticUpdate: (memberData) => {
        // Optimistically update the member in detail view
        queryClient.setQueryData([...TEAM_MEMBER_QUERY_KEYS.teamMember(memberData.id)], (oldData: TeamMember | undefined) => {
          if (!oldData) return oldData;
          return { ...oldData, ...memberData };
        });

        // Also update all paginated team member lists
        queryClient.setQueriesData(
          { queryKey: ['team-members', 'paginated'] },
          (oldData: any) => {
            if (!oldData?.teamMembers) return oldData;
            return {
              ...oldData,
              teamMembers: oldData.teamMembers.map((member: TeamMember) =>
                member.id === memberData.id ? { ...member, ...memberData } : member
              )
            };
          }
        );
      },
      onSuccess: (updatedMember) => {
        // Update with real data from server in detail view
        queryClient.setQueryData([...TEAM_MEMBER_QUERY_KEYS.teamMember(updatedMember.id)], updatedMember);

        // Also update all paginated team member lists with real data
        queryClient.setQueriesData(
          { queryKey: ['team-members', 'paginated'] },
          (oldData: any) => {
            if (!oldData?.teamMembers) return oldData;
            return {
              ...oldData,
              teamMembers: oldData.teamMembers.map((member: TeamMember) =>
                member.id === updatedMember.id ? updatedMember : member
              )
            };
          }
        );
      },
    }
  );
};

export const useDeleteTeamMember = () => {
  const queryClient = useQueryClient();

  return useStandardMutation<void, Error, string>(
    deleteTeamMember,
    {
      successMessage: 'Team member deleted successfully',
      errorMessage: 'Failed to delete team member',
      invalidateQueries: [
        [...TEAM_MEMBER_QUERY_KEYS.teamMembers],
        ['team-members', 'paginated'],
        ['public-team-members'] // Invalidate public team members cache
      ],
      optimisticUpdate: (deletedId) => {
        // Optimistically remove from cache
        queryClient.removeQueries({ queryKey: [...TEAM_MEMBER_QUERY_KEYS.teamMember(deletedId)] });
      },
      onSuccess: (_, deletedId) => {
        // Ensure removal from cache
        queryClient.removeQueries({ queryKey: [...TEAM_MEMBER_QUERY_KEYS.teamMember(deletedId)] });
      },
    }
  );
};

// Hook for public team members (no authentication required)
export const usePublicTeamMembers = () => {
  return useStandardQuery<TeamMember[]>(
    ['public-team-members'],
    async () => {
      const response = await fetch('/api/team-members');

      if (!response.ok) {
        throw new Error('Failed to fetch team members');
      }

      const data = await response.json();
      return data;
    },
    {
      staleTime: 10 * 60 * 1000, // 10 minutes (team data changes infrequently)
      gcTime: 30 * 60 * 1000, // 30 minutes
      errorMessage: 'Failed to fetch team members',
      refetchOnWindowFocus: true,
      refetchOnMount: true,
    }
  );
};

// Note: Error handling is now built into useStandardMutation
// No need for additional error handling wrappers
