import { UseQueryOptions } from '@tanstack/react-query';

/**
 * Optimized query configurations for different data types
 * This provides differentiated caching strategies based on data characteristics
 */
import type { QueryClient } from '@tanstack/react-query';

// Type guard for error with status
function isErrorWithStatus(error: unknown): error is { status: number } {
  return (
    typeof error === 'object' &&
    error !== null &&
    'status' in error &&
    typeof (error as any).status === 'number'
  );
}

// Base configuration for all queries
const baseConfig = {
  retry: (failureCount: number, error: unknown) => {
    // Don't retry on 4xx client errors
    if (isErrorWithStatus(error) && error.status >= 400 && error.status < 500) {
      return false;
    }
    return failureCount < 2;
  },
  refetchOnWindowFocus: false,
  refetchOnMount: true,
  refetchOnReconnect: true,
  retryDelay: (attemptIndex: number) => Math.min(1000 * 2 ** attemptIndex, 30000),
};

// Configuration for dashboard/statistics data
export const dashboardQueryConfig = {
  ...baseConfig,
  staleTime: 2 * 60 * 1000, // 2 minutes - dashboard stats change frequently
  gcTime: 10 * 60 * 1000, // 10 minutes
  refetchInterval: false, // Use smart real-time updates instead
};

// Configuration for list data (trips, blogs, inquiries)
export const listQueryConfig = {
  ...baseConfig,
  staleTime: 1 * 60 * 1000, // 1 minute - lists change more frequently
  gcTime: 15 * 60 * 1000, // 15 minutes
  refetchInterval: false,
};

// Configuration for detail data (individual items)
export const detailQueryConfig = {
  ...baseConfig,
  staleTime: 5 * 60 * 1000, // 5 minutes - details change less frequently
  gcTime: 30 * 60 * 1000, // 30 minutes
  refetchInterval: false,
};

// Configuration for static/reference data (roles, settings)
export const staticQueryConfig = {
  ...baseConfig,
  staleTime: 15 * 60 * 1000, // 15 minutes - static data rarely changes
  gcTime: 60 * 60 * 1000, // 1 hour
  refetchInterval: false,
};

// Configuration for public data (for public pages)
export const publicQueryConfig = {
  ...baseConfig,
  staleTime: 10 * 60 * 1000, // 10 minutes - public data can be cached longer
  gcTime: 30 * 60 * 1000, // 30 minutes
  refetchInterval: false,
  refetchOnWindowFocus: true, // Enable for public pages
};

// Configuration for real-time sensitive data
export const realTimeQueryConfig = {
  ...baseConfig,
  staleTime: 30 * 1000, // 30 seconds - very fresh data
  gcTime: 5 * 60 * 1000, // 5 minutes
  refetchInterval: false, // Use WebSocket or smart polling instead
};

/**
 * Get query configuration based on data type
 */
export function getQueryConfig(dataType: 'dashboard' | 'list' | 'detail' | 'static' | 'public' | 'realtime'): Partial<UseQueryOptions<any, any, any, any>> {
  switch (dataType) {
    case 'dashboard':
      return dashboardQueryConfig as Partial<UseQueryOptions<any, any, any, any>>;
    case 'list':
      return listQueryConfig as Partial<UseQueryOptions<any, any, any, any>>;
    case 'detail':
      return detailQueryConfig as Partial<UseQueryOptions<any, any, any, any>>;
    case 'static':
      return staticQueryConfig as Partial<UseQueryOptions<any, any, any, any>>;
    case 'public':
      return publicQueryConfig as Partial<UseQueryOptions<any, any, any, any>>;
    case 'realtime':
      return realTimeQueryConfig as Partial<UseQueryOptions<any, any, any, any>>;
    default:
      return baseConfig as Partial<UseQueryOptions<any, any, any, any>>;
  }
}

/**
 * Query key factories for consistent cache management
 */
export const queryKeys = {
  // Dashboard keys
  dashboard: ['dashboard'] as const,
  dashboardStats: () => [...queryKeys.dashboard, 'stats'] as const,
  
  // Admin data keys
  adminUsers: ['admin', 'users'] as const,
  adminUser: (id: string) => [...queryKeys.adminUsers, id] as const,
  adminRoles: ['admin', 'roles'] as const,
  
  // Trips keys
  trips: ['trips'] as const,
  tripsList: (params?: Record<string, any>) => [...queryKeys.trips, 'list', params] as const,
  trip: (id: string) => [...queryKeys.trips, id] as const,
  
  // Blogs keys
  blogs: ['blogs'] as const,
  blogsList: (params?: Record<string, any>) => [...queryKeys.blogs, 'list', params] as const,
  blog: (id: string) => [...queryKeys.blogs, id] as const,
  
  // Inquiries keys
  inquiries: ['inquiries'] as const,
  inquiriesList: (params?: Record<string, any>) => [...queryKeys.inquiries, 'list', params] as const,
  inquiry: (id: string) => [...queryKeys.inquiries, id] as const,
  
  // Photos keys
  photos: ['photos'] as const,
  photosList: (params?: Record<string, any>) => [...queryKeys.photos, 'list', params] as const,
  photoAlbum: (id: string) => [...queryKeys.photos, 'album', id] as const,
  
  // Team members keys
  teamMembers: ['team-members'] as const,
  teamMember: (id: string) => [...queryKeys.teamMembers, id] as const,
  
  // Performance keys
  performance: ['performance'] as const,
  performanceMetrics: (params?: Record<string, any>) => [...queryKeys.performance, 'metrics', params] as const,
  
  // Audit logs keys
  auditLogs: ['audit-logs'] as const,
  auditLogsList: (params?: Record<string, any>) => [...queryKeys.auditLogs, 'list', params] as const,
  
  // Public keys (for public pages)
  public: {
    trips: ['public', 'trips'] as const,
    trip: (slug: string) => ['public', 'trips', slug] as const,
    blogs: ['public', 'blogs'] as const,
    blog: (slug: string) => ['public', 'blogs', slug] as const,
    galleries: ['public', 'galleries'] as const,
    gallery: (id: string) => ['public', 'galleries', id] as const,
  },
} as const;

/**
 * Cache invalidation helpers
 */

// Define valid resource types
type ResourceType = 'trips' | 'blogs' | 'inquiries' | 'photos' | 'team-members' | 'admin-users';
type MutationOperation = 'create' | 'update' | 'delete';

export const cacheInvalidation = {
  // Invalidate all admin data
  invalidateAllAdmin: (queryClient: QueryClient) => {
    queryClient.invalidateQueries({ queryKey: ['admin'] });
    queryClient.invalidateQueries({ queryKey: queryKeys.dashboard });
  },

  // Invalidate specific resource type
  invalidateResource: (queryClient: QueryClient, resource: ResourceType) => {
    queryClient.invalidateQueries({ queryKey: [resource] });
    queryClient.invalidateQueries({ queryKey: queryKeys.dashboard }); // Dashboard shows stats for all resources
  },

  // Invalidate public data (when admin makes changes)
  invalidatePublicData: (queryClient: QueryClient) => {
    queryClient.invalidateQueries({ queryKey: ['public'] });
  },

  // Smart invalidation based on mutation type
  invalidateAfterMutation: (queryClient: QueryClient, resourceType: ResourceType, operation: MutationOperation) => {
    // Always invalidate the resource list
    queryClient.invalidateQueries({ queryKey: [resourceType] });
    
    // Always invalidate dashboard stats
    queryClient.invalidateQueries({ queryKey: queryKeys.dashboard });
    
    // For public-facing resources, also invalidate public cache
    if (['trips', 'blogs', 'photos'].includes(resourceType)) {
      queryClient.invalidateQueries({ queryKey: ['public'] });
    }
    
    // For create/delete operations, also refetch to ensure fresh data
    if (operation === 'create' || operation === 'delete') {
      queryClient.refetchQueries({ queryKey: [resourceType] });
    }
  },
};
