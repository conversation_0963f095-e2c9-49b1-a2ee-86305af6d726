import { useStandardQuery } from '@/hooks/useStandardQuery';

// Query keys for consistent caching
export const DASHBOARD_QUERY_KEYS = {
  dashboard: ['dashboard'] as const,
  stats: ['dashboard', 'stats'] as const,
} as const;

// Types
export interface DashboardStats {
  totalTrips: number;
  activeTrips: number;
  draftTrips: number;
  totalBlogs: number;
  publishedBlogs: number;
  draftBlogs: number;
  totalInquiries: number;
  newInquiries: number;
  respondedInquiries: number;
  totalPhotos: number;
  totalAdminUsers?: number;
  activeAdminUsers?: number;
  recentTrips: Array<{
    id: string;
    title: string;
    destination: string;
    is_active: boolean;
    created_at: string;
  }>;
  recentBlogs: Array<{
    id: string;
    title: string;
    is_published: boolean;
    created_at: string;
  }>;
  recentInquiries: Array<{
    id: string;
    name: string;
    email: string;
    status: string;
    created_at: string;
  }>;
}

// API functions
const fetchDashboardStats = async (): Promise<DashboardStats> => {
  const response = await fetch('/api/admin/dashboard');
  
  if (!response.ok) {
    const errorData = await response.json().catch(() => ({}));
    throw new Error(errorData.error || `HTTP ${response.status}: Failed to fetch dashboard stats`);
  }
  
  const data = await response.json();
  return data.stats;
};

// React Query hooks
export const useDashboardStats = () => {
  return useStandardQuery<DashboardStats>(
    [...DASHBOARD_QUERY_KEYS.dashboard],
    fetchDashboardStats,
    {
      errorMessage: 'Failed to fetch dashboard statistics',
      staleTime: 2 * 60 * 1000, // 2 minutes - dashboard data can be slightly stale
      gcTime: 5 * 60 * 1000, // 5 minutes
    }
  );
};

// Note: Dashboard is read-only, so no mutation hooks needed
