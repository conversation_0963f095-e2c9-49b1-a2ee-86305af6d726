import { useQueryClient } from '@tanstack/react-query';
import { Inquiry, InquiryStatus } from '@/types/inquiry';
import { useStandardQuery, useStandardMutation } from '@/hooks/useStandardQuery';

/**
 * React Query hooks for inquiry management
 */

// Query keys for consistent caching
export const INQUIRY_QUERY_KEYS = {
  inquiries: ['inquiries'] as const,
  inquiry: (id: string) => ['inquiries', id] as const,
  inquiriesPaginated: (params: InquiryQueryParams) => ['inquiries', 'paginated', params] as const,
} as const;

// Types for query parameters
export interface InquiryQueryParams {
  page?: number;
  limit?: number;
  search?: string;
  status?: string;
  inquiryType?: string;
  tripId?: string;
  startDate?: string;
  endDate?: string;
}

export interface PaginatedInquiriesResponse {
  inquiries: Inquiry[];
  pagination: {
    page: number;
    limit: number;
    total: number;
    totalPages: number;
  };
}

export interface UpdateInquiryRequest {
  id: string;
  status?: InquiryStatus;
  admin_notes?: string;
}

// API functions
const fetchInquiries = async (params: InquiryQueryParams = {}): Promise<PaginatedInquiriesResponse> => {
  const searchParams = new URLSearchParams();
  
  if (params.page) searchParams.append('page', params.page.toString());
  if (params.limit) searchParams.append('limit', params.limit.toString());
  if (params.search) searchParams.append('search', params.search);
  if (params.status && params.status !== 'all') searchParams.append('status', params.status);
  if (params.inquiryType && params.inquiryType !== 'all') searchParams.append('inquiryType', params.inquiryType);
  if (params.tripId && params.tripId !== 'all') searchParams.append('tripId', params.tripId);
  if (params.startDate) searchParams.append('startDate', params.startDate);
  if (params.endDate) searchParams.append('endDate', params.endDate);

  const response = await fetch(`/api/admin/inquiries?${searchParams.toString()}`);
  
  if (!response.ok) {
    const errorData = await response.json().catch(() => ({}));
    throw new Error(errorData.error || `HTTP ${response.status}: Failed to fetch inquiries`);
  }
  
  const data = await response.json();
  return {
    inquiries: data.data || [],
    pagination: data.pagination || { page: 1, limit: 10, total: 0, totalPages: 0 }
  };
};

const fetchInquiry = async (id: string): Promise<Inquiry> => {
  const response = await fetch(`/api/admin/inquiries/${id}`);
  
  if (!response.ok) {
    const errorData = await response.json().catch(() => ({}));
    throw new Error(errorData.error || `HTTP ${response.status}: Failed to fetch inquiry`);
  }
  
  const result = await response.json();
  return result.data;
};

const updateInquiry = async (data: UpdateInquiryRequest): Promise<Inquiry> => {
  const response = await fetch(`/api/admin/inquiries/${data.id}`, {
    method: 'PUT',
    headers: {
      'Content-Type': 'application/json',
    },
    body: JSON.stringify(data),
  });

  if (!response.ok) {
    const errorData = await response.json().catch(() => ({}));
    throw new Error(errorData.error || `HTTP ${response.status}: Failed to update inquiry`);
  }

  const result = await response.json();
  return result.data;
};

const deleteInquiry = async (id: string): Promise<void> => {
  const response = await fetch(`/api/admin/inquiries/${id}`, {
    method: 'DELETE',
  });

  if (!response.ok) {
    const errorData = await response.json().catch(() => ({}));
    throw new Error(errorData.error || `HTTP ${response.status}: Failed to delete inquiry`);
  }
};

// React Query hooks using modern patterns
export const useInquiries = (params: InquiryQueryParams = {}) => {
  return useStandardQuery<PaginatedInquiriesResponse>(
    [...INQUIRY_QUERY_KEYS.inquiriesPaginated(params)],
    () => fetchInquiries(params),
    {
      errorMessage: 'Failed to fetch inquiries',
      placeholderData: (previousData) => previousData,
    }
  );
};

export const useInquiry = (id: string, enabled: boolean = true) => {
  return useStandardQuery<Inquiry>(
    [...INQUIRY_QUERY_KEYS.inquiry(id)],
    () => fetchInquiry(id),
    {
      enabled: enabled && !!id,
      errorMessage: 'Failed to fetch inquiry details',
    }
  );
};

export const useUpdateInquiry = () => {
  const queryClient = useQueryClient();

  return useStandardMutation<Inquiry, Error, UpdateInquiryRequest>(
    updateInquiry,
    {
      successMessage: 'Inquiry updated successfully',
      errorMessage: 'Failed to update inquiry',
      invalidateQueries: [
        ['inquiries'],
        ['inquiries', 'paginated']
      ],
      optimisticUpdate: (inquiryData) => {
        // Optimistically update the inquiry in cache
        queryClient.setQueryData(INQUIRY_QUERY_KEYS.inquiry(inquiryData.id), (oldData: Inquiry | undefined) => {
          if (!oldData) return oldData;
          return { ...oldData, ...inquiryData };
        });
      },
      onSuccess: (updatedInquiry) => {
        // Update with real data from server
        queryClient.setQueryData(INQUIRY_QUERY_KEYS.inquiry(updatedInquiry.id), updatedInquiry);
      },
    }
  );
};

export const useDeleteInquiry = () => {
  const queryClient = useQueryClient();

  return useStandardMutation<void, Error, string>(
    deleteInquiry,
    {
      successMessage: 'Inquiry deleted successfully',
      errorMessage: 'Failed to delete inquiry',
      invalidateQueries: [
        ['inquiries'],
        ['inquiries', 'paginated']
      ],
      optimisticUpdate: (deletedId) => {
        // Optimistically remove from cache
        queryClient.removeQueries({ queryKey: INQUIRY_QUERY_KEYS.inquiry(deletedId) });
      },
      onSuccess: (_, deletedId) => {
        // Ensure removal from cache
        queryClient.removeQueries({ queryKey: INQUIRY_QUERY_KEYS.inquiry(deletedId) });
      },
    }
  );
};

// Hook for toggling inquiry status
export const useToggleInquiryStatus = () => {
  const queryClient = useQueryClient();

  return useStandardMutation<Inquiry, Error, { id: string; status: InquiryStatus }>(
    ({ id, status }) => updateInquiry({ id, status }),
    {
      successMessage: 'Inquiry status updated successfully',
      errorMessage: 'Failed to update inquiry status',
      invalidateQueries: [
        ['inquiries'],
        ['inquiries', 'paginated']
      ],
      optimisticUpdate: ({ id, status }) => {
        // Optimistically update the inquiry status
        queryClient.setQueryData(INQUIRY_QUERY_KEYS.inquiry(id), (oldData: Inquiry | undefined) => {
          if (!oldData) return oldData;
          return { ...oldData, status };
        });
      },
      onSuccess: (updatedInquiry) => {
        // Update with real data from server
        queryClient.setQueryData(INQUIRY_QUERY_KEYS.inquiry(updatedInquiry.id), updatedInquiry);
      },
    }
  );
};
